{"version": 3, "file": "node.js", "sources": ["../../src/node.ts"], "sourcesContent": ["/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n */\n\nimport { isBrowserBundle } from './env';\n\n/**\n * Checks whether we're in the Node.js or Browser environment\n *\n * @returns Answer to given question\n */\nexport function isNodeEnv(): boolean {\n  // explicitly check for browser bundles as those can be optimized statically\n  // by terser/rollup.\n  return (\n    !isBrowserBundle() &&\n    Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'\n  );\n}\n\n/**\n * Requires a module which is protected against bundler minification.\n *\n * @param request The module path to resolve\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types, @typescript-eslint/no-explicit-any\nexport function dynamicRequire(mod: any, request: string): any {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return mod.require(request);\n}\n\n/**\n * Helper for dynamically loading module that should work with linked dependencies.\n * The problem is that we _should_ be using `require(require.resolve(moduleName, { paths: [cwd()] }))`\n * However it's _not possible_ to do that with Webpack, as it has to know all the dependencies during\n * build time. `require.resolve` is also not available in any other way, so we cannot create,\n * a fake helper like we do with `dynamicRequire`.\n *\n * We always prefer to use local package, thus the value is not returned early from each `try/catch` block.\n * That is to mimic the behavior of `require.resolve` exactly.\n *\n * @param moduleName module name to require\n * @returns possibly required module\n */\nexport function loadModule<T>(moduleName: string): T | undefined {\n  let mod: T | undefined;\n\n  try {\n    mod = dynamicRequire(module, moduleName);\n  } catch (e) {\n    // no-empty\n  }\n\n  try {\n    const { cwd } = dynamicRequire(module, 'process');\n    mod = dynamicRequire(module, `${cwd()}/node_modules/${moduleName}`) as T;\n  } catch (e) {\n    // no-empty\n  }\n\n  return mod;\n}\n"], "names": ["isBrowserBundle"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,GAAY;AACrC;AACA;AACA,EAAE;AACF,IAAI,CAACA,mBAAe,EAAG;AACvB,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,OAAA,KAAY,WAAY,GAAE,UAAU,CAAC,MAAM,kBAAA;AACrF,IAAG;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,GAAG,EAAO,OAAO,EAAe;AAC/D;AACA,EAAE,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;AAC7B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAI,UAAU,EAAyB;AACjE,EAAE,IAAI,GAAG,CAAA;AACT;AACA,EAAE,IAAI;AACN,IAAI,MAAM,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;AAC5C,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA,GAAE;AACF;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,GAAA,EAAM,GAAE,cAAc,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;AACrD,IAAI,MAAM,cAAc,CAAC,MAAM,EAAE,CAAC,EAAA,GAAA,EAAA,CAAA,cAAA,EAAA,UAAA,CAAA,CAAA,CAAA,EAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA,GAAA;AACA;AACA,EAAA,OAAA,GAAA,CAAA;AACA;;;;;;"}