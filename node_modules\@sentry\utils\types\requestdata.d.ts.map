{"version": 3, "file": "requestdata.d.ts", "sourceRoot": "", "sources": ["../../src/requestdata.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,KAAK,EACL,wBAAwB,EACxB,kBAAkB,EAClB,WAAW,EACX,iBAAiB,EACjB,eAAe,EACf,eAAe,EAChB,MAAM,eAAe,CAAC;AAevB,QAAA,MAAM,wBAAwB,UAAkE,CAAC;AACjG,eAAO,MAAM,qBAAqB,UAA8B,CAAC;AAEjE,KAAK,gBAAgB,GAAG;IACtB,MAAM,EAAE;QACN,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACtD,CAAC;IACF,GAAG,EAAE;QACH,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK;YACzB,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;SACtB,CAAC;KACH,CAAC;CACH,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,4BAA4B,GAAG;IACzC,+EAA+E;IAC/E,OAAO,CAAC,EAAE;QACR,EAAE,CAAC,EAAE,OAAO,CAAC;QACb,OAAO,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,CAAC,OAAO,wBAAwB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;QACrE,WAAW,CAAC,EAAE,OAAO,GAAG,uBAAuB,CAAC;QAChD,IAAI,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,CAAC,OAAO,qBAAqB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;KAChE,CAAC;IAEF,8CAA8C;IAC9C,IAAI,CAAC,EAAE;QACL,MAAM,EAAE;YACN,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACtD,CAAC;QACF,GAAG,EAAE;YACH,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK;gBACzB,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;aACtB,CAAC;SACH,CAAC;KACH,CAAC;CACH,CAAC;AAEF,MAAM,MAAM,uBAAuB,GAAG,MAAM,GAAG,YAAY,GAAG,SAAS,CAAC;AAExE;;;;;GAKG;AACH,wBAAgB,2BAA2B,CACzC,WAAW,EAAE,WAAW,GAAG,SAAS,EACpC,GAAG,EAAE,kBAAkB,EACvB,IAAI,CAAC,EAAE,gBAAgB,GACtB,IAAI,CAkBN;AAED;;;;;;;;;;;;;GAaG;AACH,wBAAgB,yBAAyB,CACvC,GAAG,EAAE,kBAAkB,EACvB,OAAO,GAAE;IAAE,IAAI,CAAC,EAAE,OAAO,CAAC;IAAC,MAAM,CAAC,EAAE,OAAO,CAAC;IAAC,WAAW,CAAC,EAAE,MAAM,CAAA;CAAO,GACvE,CAAC,MAAM,EAAE,iBAAiB,CAAC,CA6B7B;AAuCD;;;;;;;;GAQG;AACH,wBAAgB,kBAAkB,CAChC,GAAG,EAAE,kBAAkB,EACvB,OAAO,CAAC,EAAE;IACR,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;IACnB,IAAI,CAAC,EAAE,gBAAgB,CAAC;CACzB,GACA,wBAAwB,CA8F1B;AAED;;;;;;;;GAQG;AACH,wBAAgB,qBAAqB,CACnC,KAAK,EAAE,KAAK,EACZ,GAAG,EAAE,kBAAkB,EACvB,OAAO,CAAC,EAAE,4BAA4B,GACrC,KAAK,CAgDP;AAkCD;;;GAGG;AAEH,wBAAgB,qBAAqB,CAAC,eAAe,EAAE,eAAe,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAe9F;AAED;;GAEG;AACH,wBAAgB,4BAA4B,CAAC,GAAG,EAAE,eAAe,GAAG,kBAAkB,CAOrF"}