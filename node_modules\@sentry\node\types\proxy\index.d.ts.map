{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../src/proxy/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;;;;;AAIH,OAAO,KAAK,KAAK,IAAI,MAAM,MAAM,CAAC;AAClC,OAAO,KAAK,EAAE,mBAAmB,EAAE,MAAM,MAAM,CAAC;AAChD,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAC3B,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAE3B,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAE1B,OAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,CAAC;AAC/B,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,QAAQ,CAAC;AAO/C,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,QAAQ,IAAI,MAAM,CAAC,EAAE,GAAG,QAAQ,GAAG,KAAK,CAAC;AAE/E,KAAK,cAAc,GAAG;IACpB,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;IACnD,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;CACrD,CAAC;AAEF,KAAK,WAAW,CAAC,CAAC,IAAI;KACnB,CAAC,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,KAAK;CAC/E,CAAC,MAAM,cAAc,CAAC,CAAC;AAExB,MAAM,MAAM,sBAAsB,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,GACpD,IAAI,CAAC,YAAY,GAAG;IAClB,OAAO,CAAC,EAAE,mBAAmB,GAAG,CAAC,MAAM,mBAAmB,CAAC,CAAC;CAC7D,CAAC;AAEJ;;;;;;;;;;;GAWG;AACH,qBAAa,eAAe,CAAC,GAAG,SAAS,MAAM,CAAE,SAAQ,KAAK;IAC5D,MAAM,CAAC,SAAS,6BAA8B;IAE9C,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC;IACpB,YAAY,EAAE,mBAAmB,GAAG,CAAC,MAAM,mBAAmB,CAAC,CAAC;IAChE,WAAW,EAAE,GAAG,CAAC,iBAAiB,GAAG,GAAG,CAAC,iBAAiB,CAAC;gBAE/C,KAAK,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,sBAAsB,CAAC,GAAG,CAAC;IAmBhE;;;OAGG;IACG,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC;CAiGpF"}