{"version": 3, "file": "worldwide.js", "sources": ["../../src/worldwide.ts"], "sourcesContent": ["/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n *\n * Note: This file was originally called `global.ts`, but was changed to unblock users which might be doing\n * string replaces with bundlers like Vite for `global` (would break imports that rely on importing from utils/src/global).\n *\n * Why worldwide?\n *\n * Why not?\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { Integration } from '@sentry/types';\n\nimport type { SdkSource } from './env';\n\n/** Internal global with common properties and Sentry extensions  */\nexport interface InternalGlobal {\n  navigator?: { userAgent?: string };\n  console: Console;\n  Sentry?: {\n    Integrations?: Integration[];\n  };\n  onerror?: {\n    (event: object | string, source?: string, lineno?: number, colno?: number, error?: Error): any;\n    __SENTRY_INSTRUMENTED__?: true;\n    __SENTRY_LOADER__?: true;\n  };\n  onunhandledrejection?: {\n    (event: unknown): boolean;\n    __SENTRY_INSTRUMENTED__?: true;\n    __SENTRY_LOADER__?: true;\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  SENTRY_SDK_SOURCE?: SdkSource;\n  /**\n   * Debug IDs are indirectly injected by Sentry CLI or bundler plugins to directly reference a particular source map\n   * for resolving of a source file. The injected code will place an entry into the record for each loaded bundle/JS\n   * file.\n   */\n  _sentryDebugIds?: Record<string, string>;\n  __SENTRY__: {\n    globalEventProcessors: any;\n    hub: any;\n    logger: any;\n    extensions?: {\n      /** Extension methods for the hub, which are bound to the current Hub instance */\n      // eslint-disable-next-line @typescript-eslint/ban-types\n      [key: string]: Function;\n    };\n  };\n  /**\n   * Raw module metadata that is injected by bundler plugins.\n   *\n   * Keys are `error.stack` strings, values are the metadata.\n   */\n  _sentryModuleMetadata?: Record<string, any>;\n}\n\n// The code below for 'isGlobalObj' and 'GLOBAL_OBJ' was copied from core-js before modification\n// https://github.com/zloirock/core-js/blob/1b944df55282cdc99c90db5f49eb0b6eda2cc0a3/packages/core-js/internals/global.js\n// core-js has the following licence:\n//\n// Copyright (c) 2014-2022 Denis Pushkarev\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n/** Returns 'obj' if it's the global object, otherwise returns undefined */\nfunction isGlobalObj(obj: { Math?: Math }): any | undefined {\n  return obj && obj.Math == Math ? obj : undefined;\n}\n\n/** Get's the global object for the current JavaScript runtime */\nexport const GLOBAL_OBJ: InternalGlobal =\n  (typeof globalThis == 'object' && isGlobalObj(globalThis)) ||\n  // eslint-disable-next-line no-restricted-globals\n  (typeof window == 'object' && isGlobalObj(window)) ||\n  (typeof self == 'object' && isGlobalObj(self)) ||\n  (typeof global == 'object' && isGlobalObj(global)) ||\n  (function (this: any) {\n    return this;\n  })() ||\n  {};\n\n/**\n * @deprecated Use GLOBAL_OBJ instead or WINDOW from @sentry/browser. This will be removed in v8\n */\nexport function getGlobalObject<T>(): T & InternalGlobal {\n  return GLOBAL_OBJ as T & InternalGlobal;\n}\n\n/**\n * Returns a global singleton contained in the global `__SENTRY__` object.\n *\n * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory\n * function and added to the `__SENTRY__` object.\n *\n * @param name name of the global singleton on __SENTRY__\n * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`\n * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value\n * @returns the singleton\n */\nexport function getGlobalSingleton<T>(name: keyof InternalGlobal['__SENTRY__'], creator: () => T, obj?: unknown): T {\n  const gbl = (obj || GLOBAL_OBJ) as InternalGlobal;\n  const __SENTRY__ = (gbl.__SENTRY__ = gbl.__SENTRY__ || {});\n  const singleton = __SENTRY__[name] || (__SENTRY__[name] = creator());\n  return singleton;\n}\n"], "names": [], "mappings": "AAkBA;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,GAAG,EAAoC;AAC5D,EAAE,OAAO,GAAI,IAAG,GAAG,CAAC,IAAK,IAAG,IAAK,GAAE,GAAI,GAAE,SAAS,CAAA;AAClD,CAAA;AACA;AACA;AACO,MAAM,UAAU;AACvB,EAAE,CAAC,OAAO,UAAA,IAAc,QAAA,IAAY,WAAW,CAAC,UAAU,CAAC;AAC3D;AACA,GAAG,OAAO,MAAA,IAAU,QAAA,IAAY,WAAW,CAAC,MAAM,CAAC,CAAE;AACrD,GAAG,OAAO,IAAA,IAAQ,QAAA,IAAY,WAAW,CAAC,IAAI,CAAC,CAAE;AACjD,GAAG,OAAO,MAAA,IAAU,QAAA,IAAY,WAAW,CAAC,MAAM,CAAC,CAAE;AACrD,EAAE,CAAC,YAAqB;AACxB,IAAI,OAAO,IAAI,CAAA;AACf,GAAG,GAAI;AACP,EAAE,GAAE;AACJ;AACA;AACA;AACA;AACO,SAAS,eAAe,GAA0B;AACzD,EAAE,OAAO,UAAW,EAAA;AACpB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAI,IAAI,EAAsC,OAAO,EAAW,GAAG,EAAe;AACpH,EAAE,MAAM,GAAI,IAAG,GAAI,IAAG,UAAU,CAAE,EAAA;AAClC,EAAE,MAAM,UAAA,IAAc,GAAG,CAAC,UAAA,GAAa,GAAG,CAAC,UAAA,IAAc,EAAE,CAAC,CAAA;AAC5D,EAAE,MAAM,SAAU,GAAE,UAAU,CAAC,IAAI,CAAE,KAAI,UAAU,CAAC,IAAI,CAAA,GAAI,OAAO,EAAE,CAAC,CAAA;AACtE,EAAE,OAAO,SAAS,CAAA;AAClB;;;;"}