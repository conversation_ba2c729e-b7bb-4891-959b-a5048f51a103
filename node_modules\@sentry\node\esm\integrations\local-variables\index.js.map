{"version": 3, "file": "index.js", "sources": ["../../../../src/integrations/local-variables/index.ts"], "sourcesContent": ["import { LocalVariablesSync, localVariablesSyncIntegration } from './local-variables-sync';\n\n/**\n * Adds local variables to exception frames.\n *\n * @deprecated Use `localVariablesIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const LocalVariables = LocalVariablesSync;\n// eslint-disable-next-line deprecation/deprecation\nexport type LocalVariables = LocalVariablesSync;\n\nexport const localVariablesIntegration = localVariablesSyncIntegration;\n"], "names": [], "mappings": ";;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,cAAe,GAAE,mBAAkB;AAChD;;AAGO,MAAM,yBAA0B,GAAE;;;;"}