<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cashfree Payment Gateway</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        input[type="text"],
        input[type="email"],
        input[type="tel"],
        input[type="number"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        input:focus {
            outline: none;
            border-color: #667eea;
        }

        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .pay-button:hover {
            transform: translateY(-2px);
        }

        .pay-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }

        .powered-by {
            text-align: center;
            margin-top: 30px;
            color: #999;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💳 Payment Gateway</h1>
            <p>Secure payment powered by Cashfree</p>
        </div>

        <div id="error-message" class="error"></div>

        <form id="payment-form">
            <div class="form-group">
                <label for="amount">Amount (₹)</label>
                <input type="number" id="amount" name="amount" required min="1" step="0.01" placeholder="Enter amount">
            </div>

            <div class="form-group">
                <label for="customerName">Full Name</label>
                <input type="text" id="customerName" name="customerName" required placeholder="Enter your full name">
            </div>

            <div class="form-group">
                <label for="customerEmail">Email Address</label>
                <input type="email" id="customerEmail" name="customerEmail" required placeholder="Enter your email">
            </div>

            <div class="form-group">
                <label for="customerPhone">Phone Number</label>
                <input type="tel" id="customerPhone" name="customerPhone" required placeholder="Enter your phone number">
            </div>

            <button type="submit" class="pay-button" id="pay-button">
                🔒 Pay Securely
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <p>Processing your payment...</p>
        </div>

        <div class="powered-by">
            Powered by <strong>Cashfree Payments</strong>
        </div>
    </div>

    <script src="https://sdk.cashfree.com/js/v3/cashfree.js"></script>
    <script>
        const form = document.getElementById('payment-form');
        const payButton = document.getElementById('pay-button');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('error-message');

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        function hideError() {
            errorMessage.style.display = 'none';
        }

        function showLoading() {
            form.style.display = 'none';
            loading.style.display = 'block';
        }

        function hideLoading() {
            form.style.display = 'block';
            loading.style.display = 'none';
        }

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            hideError();

            const formData = new FormData(form);
            const paymentData = {
                amount: formData.get('amount'),
                customerName: formData.get('customerName'),
                customerEmail: formData.get('customerEmail'),
                customerPhone: formData.get('customerPhone')
            };

            // Basic validation
            if (!paymentData.amount || parseFloat(paymentData.amount) <= 0) {
                showError('Please enter a valid amount');
                return;
            }

            if (!paymentData.customerName.trim()) {
                showError('Please enter your full name');
                return;
            }

            if (!paymentData.customerEmail.trim()) {
                showError('Please enter your email address');
                return;
            }

            if (!paymentData.customerPhone.trim()) {
                showError('Please enter your phone number');
                return;
            }

            try {
                showLoading();

                // Create order
                const response = await fetch('/payment/create-order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(paymentData)
                });

                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || 'Failed to create payment order');
                }

                // Initialize Cashfree checkout
                const cashfree = Cashfree({
                    mode: "sandbox" // Change to "production" for live environment
                });

                const checkoutOptions = {
                    paymentSessionId: result.paymentSessionId,
                    redirectTarget: "_self"
                };

                cashfree.checkout(checkoutOptions);

            } catch (error) {
                console.error('Payment error:', error);
                hideLoading();
                showError(error.message || 'Something went wrong. Please try again.');
            }
        });
    </script>
</body>
</html>
