import type { HandlerDataHistory } from '@sentry/types';
/**
 * Add an instrumentation handler for when a fetch request happens.
 * The handler function is called once when the request starts and once when it ends,
 * which can be identified by checking if it has an `endTimestamp`.
 *
 * Use at your own risk, this might break without changelog notice, only used internally.
 * @hidden
 */
export declare function addHistoryInstrumentationHandler(handler: (data: HandlerDataHistory) => void): void;
//# sourceMappingURL=history.d.ts.map