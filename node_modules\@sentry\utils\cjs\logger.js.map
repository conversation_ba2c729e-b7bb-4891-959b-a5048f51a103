{"version": 3, "file": "logger.js", "sources": ["../../src/logger.ts"], "sourcesContent": ["import type { ConsoleLevel } from '@sentry/types';\n\nimport { DEBUG_BUILD } from './debug-build';\nimport { GLOBAL_OBJ } from './worldwide';\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\nexport const CONSOLE_LEVELS: readonly ConsoleLevel[] = [\n  'debug',\n  'info',\n  'warn',\n  'error',\n  'log',\n  'assert',\n  'trace',\n] as const;\n\ntype LoggerMethod = (...args: unknown[]) => void;\ntype LoggerConsoleMethods = Record<ConsoleLevel, LoggerMethod>;\n\n/** This may be mutated by the console instrumentation. */\nexport const originalConsoleMethods: {\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [key in ConsoleLevel]?: (...args: any[]) => void;\n} = {};\n\n/** JSDoc */\ninterface Logger extends LoggerConsoleMethods {\n  disable(): void;\n  enable(): void;\n  isEnabled(): boolean;\n}\n\n/**\n * Temporarily disable sentry console instrumentations.\n *\n * @param callback The function to run against the original `console` messages\n * @returns The results of the callback\n */\nexport function consoleSandbox<T>(callback: () => T): T {\n  if (!('console' in GLOBAL_OBJ)) {\n    return callback();\n  }\n\n  const console = GLOBAL_OBJ.console as Console;\n  const wrappedFuncs: Partial<LoggerConsoleMethods> = {};\n\n  const wrappedLevels = Object.keys(originalConsoleMethods) as ConsoleLevel[];\n\n  // Restore all wrapped console methods\n  wrappedLevels.forEach(level => {\n    const originalConsoleMethod = originalConsoleMethods[level] as LoggerMethod;\n    wrappedFuncs[level] = console[level] as LoggerMethod | undefined;\n    console[level] = originalConsoleMethod;\n  });\n\n  try {\n    return callback();\n  } finally {\n    // Revert restoration to wrapped state\n    wrappedLevels.forEach(level => {\n      console[level] = wrappedFuncs[level] as LoggerMethod;\n    });\n  }\n}\n\nfunction makeLogger(): Logger {\n  let enabled = false;\n  const logger: Partial<Logger> = {\n    enable: () => {\n      enabled = true;\n    },\n    disable: () => {\n      enabled = false;\n    },\n    isEnabled: () => enabled,\n  };\n\n  if (DEBUG_BUILD) {\n    CONSOLE_LEVELS.forEach(name => {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      logger[name] = (...args: any[]) => {\n        if (enabled) {\n          consoleSandbox(() => {\n            GLOBAL_OBJ.console[name](`${PREFIX}[${name}]:`, ...args);\n          });\n        }\n      };\n    });\n  } else {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = () => undefined;\n    });\n  }\n\n  return logger as Logger;\n}\n\nexport const logger = makeLogger();\n"], "names": ["GLOBAL_OBJ", "DEBUG_BUILD"], "mappings": ";;;;;AAKA;AACA,MAAM,MAAA,GAAS,gBAAgB,CAAA;AAC/B;AACO,MAAM,cAAc,GAA4B;AACvD,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,CAAE,EAAA;;AAKF;MACa,sBAAsB;;AAGnC,GAAI,GAAE;AACN;AACA;;AAOA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAI,QAAQ,EAAc;AACxD,EAAE,IAAI,EAAE,aAAaA,oBAAU,CAAC,EAAE;AAClC,IAAI,OAAO,QAAQ,EAAE,CAAA;AACrB,GAAE;AACF;AACA,EAAE,MAAM,OAAA,GAAUA,oBAAU,CAAC,OAAQ,EAAA;AACrC,EAAE,MAAM,YAAY,GAAkC,EAAE,CAAA;AACxD;AACA,EAAE,MAAM,gBAAgB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAE,EAAA;AAC5D;AACA;AACA,EAAE,aAAa,CAAC,OAAO,CAAC,SAAS;AACjC,IAAI,MAAM,qBAAsB,GAAE,sBAAsB,CAAC,KAAK,CAAE,EAAA;AAChE,IAAI,YAAY,CAAC,KAAK,CAAA,GAAI,OAAO,CAAC,KAAK,CAAE,EAAA;AACzC,IAAI,OAAO,CAAC,KAAK,CAAA,GAAI,qBAAqB,CAAA;AAC1C,GAAG,CAAC,CAAA;AACJ;AACA,EAAE,IAAI;AACN,IAAI,OAAO,QAAQ,EAAE,CAAA;AACrB,YAAY;AACZ;AACA,IAAI,aAAa,CAAC,OAAO,CAAC,SAAS;AACnC,MAAM,OAAO,CAAC,KAAK,CAAA,GAAI,YAAY,CAAC,KAAK,CAAE,EAAA;AAC3C,KAAK,CAAC,CAAA;AACN,GAAE;AACF,CAAA;AACA;AACA,SAAS,UAAU,GAAW;AAC9B,EAAE,IAAI,OAAQ,GAAE,KAAK,CAAA;AACrB,EAAE,MAAM,MAAM,GAAoB;AAClC,IAAI,MAAM,EAAE,MAAM;AAClB,MAAM,OAAA,GAAU,IAAI,CAAA;AACpB,KAAK;AACL,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAA,GAAU,KAAK,CAAA;AACrB,KAAK;AACL,IAAI,SAAS,EAAE,MAAM,OAAO;AAC5B,GAAG,CAAA;AACH;AACA,EAAE,IAAIC,sBAAW,EAAE;AACnB,IAAI,cAAc,CAAC,OAAO,CAAC,QAAQ;AACnC;AACA,MAAM,MAAM,CAAC,IAAI,CAAA,GAAI,CAAC,GAAG,IAAI,KAAY;AACzC,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,cAAc,CAAC,MAAM;AAC/B,YAAYD,oBAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA,CAAA;AACA,WAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,cAAA,CAAA,OAAA,CAAA,IAAA,IAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,GAAA,MAAA,SAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,MAAA,EAAA;AACA,CAAA;AACA;AACA,MAAA,MAAA,GAAA,UAAA;;;;;;;"}