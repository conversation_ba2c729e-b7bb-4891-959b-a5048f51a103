{"version": 3, "file": "index.js", "sources": ["../../../../src/integrations/undici/index.ts"], "sourcesContent": ["import {\n  addBreadcrumb,\n  defineIntegration,\n  getActiveSpan,\n  getClient,\n  getCurrentScope,\n  getDynamicSamplingContextFromClient,\n  getDynamicSamplingContextFromSpan,\n  getIsolationScope,\n  hasTracingEnabled,\n  isSentryRequestUrl,\n  setHttpStatus,\n  spanToTraceHeader,\n} from '@sentry/core';\nimport type { EventProcessor, Integration, IntegrationFn, IntegrationFnResult, Span } from '@sentry/types';\nimport {\n  LRUMap,\n  dynamicSamplingContextToSentryBaggageHeader,\n  generateSentryTraceHeader,\n  getSanitizedUrlString,\n  parseUrl,\n  stringMatchesSomePattern,\n} from '@sentry/utils';\n\nimport type { NodeClient } from '../../client';\nimport { NODE_VERSION } from '../../nodeVersion';\nimport type {\n  DiagnosticsChannel,\n  RequestCreateMessage,\n  RequestEndMessage,\n  RequestErrorMessage,\n  RequestWithSentry,\n} from './types';\n\nexport enum ChannelName {\n  // https://github.com/nodejs/undici/blob/e6fc80f809d1217814c044f52ed40ef13f21e43c/docs/api/DiagnosticsChannel.md#undicirequestcreate\n  RequestCreate = 'undici:request:create',\n  RequestEnd = 'undici:request:headers',\n  RequestError = 'undici:request:error',\n}\n\nexport interface UndiciOptions {\n  /**\n   * Whether breadcrumbs should be recorded for requests\n   * Defaults to true\n   */\n  breadcrumbs: boolean;\n\n  /**\n   * Whether tracing spans should be created for requests\n   * If not set, this will be enabled/disabled based on if tracing is enabled.\n   */\n  tracing?: boolean;\n\n  /**\n   * Function determining whether or not to create spans to track outgoing requests to the given URL.\n   * By default, spans will be created for all outgoing requests.\n   */\n  shouldCreateSpanForRequest?: (url: string) => boolean;\n}\n\n// Please note that you cannot use `console.log` to debug the callbacks registered to the `diagnostics_channel` API.\n// To debug, you can use `writeFileSync` to write to a file:\n// https://nodejs.org/api/async_hooks.html#printing-in-asynchook-callbacks\n//\n// import { writeFileSync } from 'fs';\n// import { format } from 'util';\n//\n// function debug(...args: any): void {\n//   // Use a function like this one when debugging inside an AsyncHook callback\n//   // @ts-expect-error any\n//   writeFileSync('log.out', `${format(...args)}\\n`, { flag: 'a' });\n// }\n\nconst _nativeNodeFetchintegration = ((options?: Partial<UndiciOptions>) => {\n  // eslint-disable-next-line deprecation/deprecation\n  return new Undici(options) as unknown as IntegrationFnResult;\n}) satisfies IntegrationFn;\n\nexport const nativeNodeFetchintegration = defineIntegration(_nativeNodeFetchintegration);\n\n/**\n * Instruments outgoing HTTP requests made with the `undici` package via\n * Node's `diagnostics_channel` API.\n *\n * Supports Undici 4.7.0 or higher.\n *\n * Requires Node 16.17.0 or higher.\n *\n * @deprecated Use `nativeNodeFetchintegration()` instead.\n */\nexport class Undici implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Undici';\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public name: string = Undici.id;\n\n  private readonly _options: UndiciOptions;\n\n  private readonly _createSpanUrlMap: LRUMap<string, boolean> = new LRUMap(100);\n  private readonly _headersUrlMap: LRUMap<string, boolean> = new LRUMap(100);\n\n  public constructor(_options: Partial<UndiciOptions> = {}) {\n    this._options = {\n      breadcrumbs: _options.breadcrumbs === undefined ? true : _options.breadcrumbs,\n      tracing: _options.tracing,\n      shouldCreateSpanForRequest: _options.shouldCreateSpanForRequest,\n    };\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(_addGlobalEventProcessor: (callback: EventProcessor) => void): void {\n    // Requires Node 16+ to use the diagnostics_channel API.\n    if (NODE_VERSION.major < 16) {\n      return;\n    }\n\n    let ds: DiagnosticsChannel | undefined;\n    try {\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      ds = require('diagnostics_channel') as DiagnosticsChannel;\n    } catch (e) {\n      // no-op\n    }\n\n    if (!ds || !ds.subscribe) {\n      return;\n    }\n\n    // https://github.com/nodejs/undici/blob/e6fc80f809d1217814c044f52ed40ef13f21e43c/docs/api/DiagnosticsChannel.md\n    ds.subscribe(ChannelName.RequestCreate, this._onRequestCreate);\n    ds.subscribe(ChannelName.RequestEnd, this._onRequestEnd);\n    ds.subscribe(ChannelName.RequestError, this._onRequestError);\n  }\n\n  /** Helper that wraps shouldCreateSpanForRequest option */\n  private _shouldCreateSpan(url: string): boolean {\n    if (this._options.tracing === false || (this._options.tracing === undefined && !hasTracingEnabled())) {\n      return false;\n    }\n\n    if (this._options.shouldCreateSpanForRequest === undefined) {\n      return true;\n    }\n\n    const cachedDecision = this._createSpanUrlMap.get(url);\n    if (cachedDecision !== undefined) {\n      return cachedDecision;\n    }\n\n    const decision = this._options.shouldCreateSpanForRequest(url);\n    this._createSpanUrlMap.set(url, decision);\n    return decision;\n  }\n\n  private _onRequestCreate = (message: unknown): void => {\n    // eslint-disable-next-line deprecation/deprecation\n    if (!getClient()?.getIntegration(Undici)) {\n      return;\n    }\n\n    const { request } = message as RequestCreateMessage;\n\n    const stringUrl = request.origin ? request.origin.toString() + request.path : request.path;\n\n    const client = getClient<NodeClient>();\n    if (!client) {\n      return;\n    }\n\n    if (isSentryRequestUrl(stringUrl, client) || request.__sentry_span__ !== undefined) {\n      return;\n    }\n\n    const clientOptions = client.getOptions();\n    const scope = getCurrentScope();\n    const isolationScope = getIsolationScope();\n    const parentSpan = getActiveSpan();\n\n    const span = this._shouldCreateSpan(stringUrl) ? createRequestSpan(parentSpan, request, stringUrl) : undefined;\n    if (span) {\n      request.__sentry_span__ = span;\n    }\n\n    const shouldAttachTraceData = (url: string): boolean => {\n      if (clientOptions.tracePropagationTargets === undefined) {\n        return true;\n      }\n\n      const cachedDecision = this._headersUrlMap.get(url);\n      if (cachedDecision !== undefined) {\n        return cachedDecision;\n      }\n\n      const decision = stringMatchesSomePattern(url, clientOptions.tracePropagationTargets);\n      this._headersUrlMap.set(url, decision);\n      return decision;\n    };\n\n    if (shouldAttachTraceData(stringUrl)) {\n      const { traceId, spanId, sampled, dsc } = {\n        ...isolationScope.getPropagationContext(),\n        ...scope.getPropagationContext(),\n      };\n\n      const sentryTraceHeader = span ? spanToTraceHeader(span) : generateSentryTraceHeader(traceId, spanId, sampled);\n\n      const sentryBaggageHeader = dynamicSamplingContextToSentryBaggageHeader(\n        dsc ||\n          (span\n            ? getDynamicSamplingContextFromSpan(span)\n            : getDynamicSamplingContextFromClient(traceId, client, scope)),\n      );\n\n      setHeadersOnRequest(request, sentryTraceHeader, sentryBaggageHeader);\n    }\n  };\n\n  private _onRequestEnd = (message: unknown): void => {\n    // eslint-disable-next-line deprecation/deprecation\n    if (!getClient()?.getIntegration(Undici)) {\n      return;\n    }\n\n    const { request, response } = message as RequestEndMessage;\n\n    const stringUrl = request.origin ? request.origin.toString() + request.path : request.path;\n\n    if (isSentryRequestUrl(stringUrl, getClient())) {\n      return;\n    }\n\n    const span = request.__sentry_span__;\n    if (span) {\n      setHttpStatus(span, response.statusCode);\n      span.end();\n    }\n\n    if (this._options.breadcrumbs) {\n      addBreadcrumb(\n        {\n          category: 'http',\n          data: {\n            method: request.method,\n            status_code: response.statusCode,\n            url: stringUrl,\n          },\n          type: 'http',\n        },\n        {\n          event: 'response',\n          request,\n          response,\n        },\n      );\n    }\n  };\n\n  private _onRequestError = (message: unknown): void => {\n    // eslint-disable-next-line deprecation/deprecation\n    if (!getClient()?.getIntegration(Undici)) {\n      return;\n    }\n\n    const { request } = message as RequestErrorMessage;\n\n    const stringUrl = request.origin ? request.origin.toString() + request.path : request.path;\n\n    if (isSentryRequestUrl(stringUrl, getClient())) {\n      return;\n    }\n\n    const span = request.__sentry_span__;\n    if (span) {\n      span.setStatus('internal_error');\n      span.end();\n    }\n\n    if (this._options.breadcrumbs) {\n      addBreadcrumb(\n        {\n          category: 'http',\n          data: {\n            method: request.method,\n            url: stringUrl,\n          },\n          level: 'error',\n          type: 'http',\n        },\n        {\n          event: 'error',\n          request,\n        },\n      );\n    }\n  };\n}\n\nfunction setHeadersOnRequest(\n  request: RequestWithSentry,\n  sentryTrace: string,\n  sentryBaggageHeader: string | undefined,\n): void {\n  let hasSentryHeaders: boolean;\n  if (Array.isArray(request.headers)) {\n    hasSentryHeaders = request.headers.some(headerLine => headerLine === 'sentry-trace');\n  } else {\n    const headerLines = request.headers.split('\\r\\n');\n    hasSentryHeaders = headerLines.some(headerLine => headerLine.startsWith('sentry-trace:'));\n  }\n\n  if (hasSentryHeaders) {\n    return;\n  }\n\n  request.addHeader('sentry-trace', sentryTrace);\n  if (sentryBaggageHeader) {\n    request.addHeader('baggage', sentryBaggageHeader);\n  }\n}\n\nfunction createRequestSpan(\n  activeSpan: Span | undefined,\n  request: RequestWithSentry,\n  stringUrl: string,\n): Span | undefined {\n  const url = parseUrl(stringUrl);\n\n  const method = request.method || 'GET';\n  const data: Record<string, unknown> = {\n    'http.method': method,\n  };\n  if (url.search) {\n    data['http.query'] = url.search;\n  }\n  if (url.hash) {\n    data['http.fragment'] = url.hash;\n  }\n  // eslint-disable-next-line deprecation/deprecation\n  return activeSpan?.startChild({\n    op: 'http.client',\n    origin: 'auto.http.node.undici',\n    description: `${method} ${getSanitizedUrlString(url)}`,\n    data,\n  });\n}\n"], "names": [], "mappings": ";;;;;AAkCwB,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,WAAA,EAAA;EACxB,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACE,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACvC,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACrC,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACvC,CAAA,CAAA,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;;AAsBA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA;AACA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AACA,CAAA,EAAA;;AAEA,CAAA,CAAA,CAAA,CAAA,EAAM,+BAA+B,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAA8B,CAAA,EAAA;EAC3E,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACE,OAAO,CAAI,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE;AAC7B,CAAC,EAAE;;MAEU,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,2BAA2B,CAAC;;AAExF,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;CACA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,CAAA;AACO,CAAA,CAAA,CAAA,CAAA,EAAM,QAA8B;EAC3C,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;GACS,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,YAAA,CAAA,EAAA,CAAA,IAAA,CAAO,CAAA,EAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;;EAErC,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;EACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;kBACS,KAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAA,CAAA,CAAE;;oBAId,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA6C,EAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAA,CAAC;oBAC5D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAA0C,EAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAA,CAAC;;GAEnE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAA2B,EAAE,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACxD,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,EAAA;MACd,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAK,CAAA,CAAA,EAAA,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;MAC7E,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;MACzB,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACrE,CAAK;EACH;;EAEF,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;GACS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAA4C;IACvF,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACI,IAAI,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAM,CAAA,CAAA,CAAA,EAAA,EAAE,EAAE,EAAE;MAC3B,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,CAAA,CAAA,EAAI,EAAE;IACN,CAAI,CAAA,EAAA;MACR,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;MACM,GAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,EAAE;IACpC,EAAA,CAAO,CAAA,CAAA,CAAA,EAAA,CAAA,CAAC,EAAE;MAChB,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;IACI;;IAEA,CAAA,EAAA,CAAI,CAAC,CAAA,EAAA,CAAA,EAAM,CAAC,CAAE,CAAA,CAAC,SAAS,EAAE;MACxB,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEJ,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAE,CAAA,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAI,CAAA,CAAA,CAAA,CAAC,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;IAC9D,CAAE,CAAA,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAI,CAAA,CAAA,CAAA,CAAC,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;IACxD,CAAE,CAAA,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAI,CAAA,CAAA,CAAA,CAAC,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;EAC9D;;EAEF,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;GACU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAA,CAAA,CAAG,EAAmB;IAC9C,CAAA,EAAA,CAAI,IAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,MAAU,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,QAAY,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAa,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAE,CAAA,CAAC,EAAE;MACpG,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,KAAK;IACd;;IAEA,CAAA,EAAA,CAAI,IAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAA+B,SAAS,EAAE;MAC1D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,IAAI;IACb;;IAEA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAI,CAAA,CAAA,CAAA,CAAC,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAC;IACtD,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAmB,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE;MAChC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,cAAc;IACvB;;IAEA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAI,CAAA,CAAA,CAAA,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAC;IAC9D,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAA,CAAA,CAAG,CAAC,CAAG,CAAA,CAAA,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;IACzC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,QAAQ;EACjB;;GAEQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAmB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAoB,CAAA,EAAA;IACzD,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAE,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAE,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAC,EAAE;MACxC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,MAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,EAAA,EAAI,QAAQ;;IAE5B,CAAA,CAAA,CAAA,CAAA,EAAM,UAAY,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,OAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,KAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAI;;IAE1F,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAc;IACtC,CAAA,EAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE;MACX,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAA,EAAG,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,SAAS,EAAE;MAClF,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,MAAM,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,MAAM,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,EAAE;IACzC,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAE;IAC/B,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,EAAE;IAC1C,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE;;IAElC,CAAA,CAAA,CAAA,CAAA,EAAM,KAAO,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,SAAS,EAAA,EAAI,iBAAiB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,SAAS,EAAA,EAAI,SAAS;IAC9G,CAAA,EAAA,CAAI,IAAI,EAAE;MACR,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,IAAI;IAChC;;IAEA,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAwB,EAAA,CAAC,GAAG,EAAsB,CAAA,EAAA;MACtD,IAAI,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,SAAS,EAAE;QACvD,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,IAAI;MACb;;MAEA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAI,CAAA,CAAA,CAAA,CAAC,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAC;MACnD,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAmB,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE;QAChC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,cAAc;MACvB;;MAEA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,EAAE,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;MACrF,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAG,CAAC,CAAG,CAAA,CAAA,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;MACtC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,QAAQ;IACrB,CAAK;;IAED,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAC,EAAE;MACpC,CAAA,CAAA,CAAA,CAAA,EAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAI,CAAA,EAAA,EAAI,EAAA;QACxC,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAE,CAAA;QACzC,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAE,CAAA;MACxC,CAAO;;MAED,MAAM,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,KAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAI,CAAA,CAAA,CAAA,EAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,MAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC;;MAE9G,CAAA,CAAA,CAAA,CAAA,EAAM,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACrE,CAAI,CAAA,EAAA,CAAA;UACZ,CAAW,CAAA,CAAA,CAAA;YACG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiC,CAAC,CAAA,CAAA,CAAA,CAAI;YACtC,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmC,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,MAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAC;MAC1E,CAAO;;MAED,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,iBAAiB,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC;IACtE;EACJ,CAAA,CAAG;;GAEO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAgB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAoB,CAAA,EAAA;IACtD,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAE,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAE,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAC,EAAE;MACxC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,CAAA,CAAA,CAAA,CAAA,EAAM,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,SAAS,EAAA,EAAI,QAAQ;;IAEtC,CAAA,CAAA,CAAA,CAAA,EAAM,UAAY,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,OAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,KAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAI;;IAE1F,CAAA,EAAA,CAAI,kBAAkB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAC,EAAE;MAC9C,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,MAAM,CAAA,CAAA,CAAA,EAAA,EAAO,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,eAAe;IACpC,CAAA,EAAA,CAAI,IAAI,EAAE;MACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,CAAI,CAAA,CAAA,CAAA,EAAE,QAAQ,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC;MACxC,CAAI,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAA,CAAE;IACZ;;IAEA,IAAI,CAAI,CAAA,CAAA,CAAA,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,WAAW,EAAE;MAC7B,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACX;UACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;UAChB,IAAI,EAAE;YACJ,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;YACtB,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAChC,CAAA,CAAA,CAAG,EAAE,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACf,CAAA;UACD,CAAA,CAAA,CAAA,CAAI,EAAE,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;QACb,CAAA;QACD;UACE,CAAA,CAAA,CAAA,CAAA,CAAK,EAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACjB,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACP,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACT,CAAA;MACT,CAAO;IACH;EACJ,CAAA,CAAG;;GAEO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAkB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAoB,CAAA,EAAA;IACxD,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACI,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAE,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAE,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAC,EAAE;MACxC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,MAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,EAAA,EAAI,QAAQ;;IAE5B,CAAA,CAAA,CAAA,CAAA,EAAM,UAAY,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,OAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,KAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAI;;IAE1F,CAAA,EAAA,CAAI,kBAAkB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAC,EAAE;MAC9C,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,MAAM,CAAA,CAAA,CAAA,EAAA,EAAO,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,eAAe;IACpC,CAAA,EAAA,CAAI,IAAI,EAAE;MACR,IAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAC;MAChC,CAAI,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAA,CAAE;IACZ;;IAEA,IAAI,CAAI,CAAA,CAAA,CAAA,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,WAAW,EAAE;MAC7B,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACX;UACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;UAChB,IAAI,EAAE;YACJ,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;YACtB,CAAA,CAAA,CAAG,EAAE,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACf,CAAA;UACD,CAAA,CAAA,CAAA,CAAA,CAAK,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACd,CAAA,CAAA,CAAA,CAAI,EAAE,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;QACb,CAAA;QACD;UACE,CAAA,CAAA,CAAA,CAAA,CAAK,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACd,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA;MACT,CAAO;IACH;EACJ,CAAA,CAAG;AACH,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAC1B,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACP,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACX,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACrB,EAAQ;EACN,CAAA,CAAA,EAAI,gBAAgB;EACpB,CAAA,EAAA,CAAI,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,EAAE;IAClC,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,OAAO,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC;EACxF,EAAS,CAAA,CAAA,CAAA,EAAA;IACL,CAAA,CAAA,CAAA,CAAA,EAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;IACjD,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,IAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC;EAC3F;;EAEA,CAAA,EAAA,CAAI,gBAAgB,EAAE;IACpB,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;EACR;;EAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,cAAc,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC;EAC9C,CAAA,EAAA,CAAI,mBAAmB,EAAE;IACvB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,SAAS,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAmB,CAAC;EACnD;AACF;;AAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACxB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACV,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACP,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACX,EAAoB;EAClB,MAAM,CAAI,CAAA,EAAA,EAAE,QAAQ,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAC;;EAE/B,CAAA,CAAA,CAAA,CAAA,EAAM,OAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAU,KAAK;EACtC,CAAA,CAAA,CAAA,CAAA,EAAM,KAAgC,EAAA;IACpC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;EACzB,CAAG;EACD,CAAI,EAAA,CAAA,CAAA,CAAA,CAAG,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE;IACd,CAAA,CAAA,CAAA,CAAI,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAI,CAAG,CAAA,CAAA,CAAC,MAAM;EACjC;EACA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAG,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE;IACZ,CAAA,CAAA,CAAA,CAAI,CAAC,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAI,CAAG,CAAA,CAAA,CAAC,IAAI;EAClC;EACF,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACE,OAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,gBAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,UAAU,EAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,GAAA,CAAA;IAC5B,CAAA,CAAE,EAAE,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACjB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAC/B,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA,CAAA,CAAA,CAAA,CAAA;EACA,CAAA,CAAA,CAAA,CAAA;AACA;;"}