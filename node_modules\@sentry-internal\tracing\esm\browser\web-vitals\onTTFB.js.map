{"version": 3, "file": "onTTFB.js", "sources": ["../../../../src/browser/web-vitals/onTTFB.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../types';\nimport { bindReporter } from './lib/bindReporter';\nimport { getActivationStart } from './lib/getActivationStart';\nimport { getNavigationEntry } from './lib/getNavigationEntry';\nimport { initMetric } from './lib/initMetric';\nimport type { ReportCallback, ReportOpts } from './types';\nimport type { TTFBMetric } from './types/ttfb';\n\n/**\n * Runs in the next task after the page is done loading and/or prerendering.\n * @param callback\n */\nconst whenReady = (callback: () => void): void => {\n  if (!WINDOW.document) {\n    return;\n  }\n\n  if (WINDOW.document.prerendering) {\n    addEventListener('prerenderingchange', () => whenReady(callback), true);\n  } else if (WINDOW.document.readyState !== 'complete') {\n    addEventListener('load', () => whenReady(callback), true);\n  } else {\n    // Queue a task so the callback runs after `loadEventEnd`.\n    setTimeout(callback, 0);\n  }\n};\n\n/**\n * Calculates the [TTFB](https://web.dev/time-to-first-byte/) value for the\n * current page and calls the `callback` function once the page has loaded,\n * along with the relevant `navigation` performance entry used to determine the\n * value. The reported value is a `DOMHighResTimeStamp`.\n *\n * Note, this function waits until after the page is loaded to call `callback`\n * in order to ensure all properties of the `navigation` entry are populated.\n * This is useful if you want to report on other metrics exposed by the\n * [Navigation Timing API](https://w3c.github.io/navigation-timing/). For\n * example, the TTFB metric starts from the page's [time\n * origin](https://www.w3.org/TR/hr-time-2/#sec-time-origin), which means it\n * includes time spent on DNS lookup, connection negotiation, network latency,\n * and server processing time.\n */\nexport const onTTFB = (onReport: ReportCallback, opts?: ReportOpts): void => {\n  // Set defaults\n  // eslint-disable-next-line no-param-reassign\n  opts = opts || {};\n\n  // https://web.dev/ttfb/#what-is-a-good-ttfb-score\n  // const thresholds = [800, 1800];\n\n  const metric = initMetric('TTFB');\n  const report = bindReporter(onReport, metric, opts.reportAllChanges);\n\n  whenReady(() => {\n    const navEntry = getNavigationEntry() as TTFBMetric['entries'][number];\n\n    if (navEntry) {\n      // The activationStart reference is used because TTFB should be\n      // relative to page activation rather than navigation start if the\n      // page was prerendered. But in cases where `activationStart` occurs\n      // after the first byte is received, this time should be clamped at 0.\n      metric.value = Math.max(navEntry.responseStart - getActivationStart(), 0);\n\n      // In some cases the value reported is negative or is larger\n      // than the current page time. Ignore these cases:\n      // https://github.com/GoogleChrome/web-vitals/issues/137\n      // https://github.com/GoogleChrome/web-vitals/issues/162\n      if (metric.value < 0 || metric.value > performance.now()) return;\n\n      metric.entries = [navEntry];\n\n      report(true);\n    }\n  });\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAUA;AACA;AACA;AACA;AACA,MAAM,SAAU,GAAE,CAAC,QAAQ,KAAuB;AAClD,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACxB,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;AACpC,IAAI,gBAAgB,CAAC,oBAAoB,EAAE,MAAM,SAAS,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAA;AAC3E,GAAE,MAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAA,KAAe,UAAU,EAAE;AACxD,IAAI,gBAAgB,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAA;AAC7D,SAAS;AACT;AACA,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;AAC3B,GAAE;AACF,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,MAAO,GAAE,CAAC,QAAQ,EAAkB,IAAI,KAAwB;AAC7E;AACA;AACA,EAAE,IAAK,GAAE,IAAK,IAAG,EAAE,CAAA;AACnB;AACA;AACA;AACA;AACA,EAAE,MAAM,MAAO,GAAE,UAAU,CAAC,MAAM,CAAC,CAAA;AACnC,EAAE,MAAM,MAAA,GAAS,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;AACtE;AACA,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,MAAM,QAAA,GAAW,kBAAkB,EAAG,EAAA;AAC1C;AACA,IAAI,IAAI,QAAQ,EAAE;AAClB;AACA;AACA;AACA;AACA,MAAM,MAAM,CAAC,KAAA,GAAQ,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,kBAAkB,EAAE,EAAE,CAAC,CAAC,CAAA;AAC/E;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,MAAM,CAAC,KAAM,GAAE,KAAK,MAAM,CAAC,KAAA,GAAQ,WAAW,CAAC,GAAG,EAAE,EAAE,OAAM;AACtE;AACA,MAAM,MAAM,CAAC,OAAA,GAAU,CAAC,QAAQ,CAAC,CAAA;AACjC;AACA,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA;AAClB,KAAI;AACJ,GAAG,CAAC,CAAA;AACJ;;;;"}