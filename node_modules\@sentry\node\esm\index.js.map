{"version": 3, "file": "index.js", "sources": ["../../src/index.ts"], "sourcesContent": ["export type {\n  Bread<PERSON>rumb,\n  BreadcrumbHint,\n  PolymorphicRequest,\n  Request,\n  SdkInfo,\n  Event,\n  EventHint,\n  Exception,\n  Session,\n  // eslint-disable-next-line deprecation/deprecation\n  Severity,\n  SeverityLevel,\n  Span,\n  StackFrame,\n  Stacktrace,\n  Thread,\n  Transaction,\n  User,\n} from '@sentry/types';\nexport type { AddRequestDataToEventOptions, TransactionNamingScheme } from '@sentry/utils';\n\nexport type { NodeOptions } from './types';\n\nexport {\n  // eslint-disable-next-line deprecation/deprecation\n  addGlobalEventProcessor,\n  addEventProcessor,\n  addBreadcrumb,\n  addIntegration,\n  captureException,\n  captureEvent,\n  captureMessage,\n  close,\n  // eslint-disable-next-line deprecation/deprecation\n  configureScope,\n  createTransport,\n  // eslint-disable-next-line deprecation/deprecation\n  extractTraceparentData,\n  flush,\n  // eslint-disable-next-line deprecation/deprecation\n  getActiveTransaction,\n  getHubFromCarrier,\n  // eslint-disable-next-line deprecation/deprecation\n  getCurrentHub,\n  getClient,\n  isInitialized,\n  getCurrentScope,\n  getGlobalScope,\n  getIsolationScope,\n  // eslint-disable-next-line deprecation/deprecation\n  Hub,\n  lastEventId,\n  // eslint-disable-next-line deprecation/deprecation\n  makeMain,\n  setCurrentClient,\n  runWithAsyncContext,\n  Scope,\n  // eslint-disable-next-line deprecation/deprecation\n  startTransaction,\n  SDK_VERSION,\n  setContext,\n  setExtra,\n  setExtras,\n  setTag,\n  setTags,\n  setUser,\n  // eslint-disable-next-line deprecation/deprecation\n  spanStatusfromHttpCode,\n  getSpanStatusFromHttpCode,\n  setHttpStatus,\n  // eslint-disable-next-line deprecation/deprecation\n  trace,\n  withScope,\n  withIsolationScope,\n  captureCheckIn,\n  withMonitor,\n  setMeasurement,\n  getActiveSpan,\n  startSpan,\n  // eslint-disable-next-line deprecation/deprecation\n  startActiveSpan,\n  startInactiveSpan,\n  startSpanManual,\n  withActiveSpan,\n  continueTrace,\n  parameterize,\n  metrics,\n  functionToStringIntegration,\n  inboundFiltersIntegration,\n  linkedErrorsIntegration,\n  requestDataIntegration,\n  startSession,\n  captureSession,\n  endSession,\n} from '@sentry/core';\n\nexport {\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n} from '@sentry/core';\n\nexport type { SpanStatusType } from '@sentry/core';\n\nexport { autoDiscoverNodePerformanceMonitoringIntegrations } from './tracing';\n\nexport { NodeClient } from './client';\nexport { makeNodeTransport } from './transports';\nexport {\n  // eslint-disable-next-line deprecation/deprecation\n  defaultIntegrations,\n  getDefaultIntegrations,\n  init,\n  defaultStackParser,\n  getSentryRelease,\n} from './sdk';\nexport { addRequestDataToEvent, DEFAULT_USER_INCLUDES, extractRequestData } from '@sentry/utils';\n// eslint-disable-next-line deprecation/deprecation\nexport { deepReadDirSync } from './utils';\n\nimport { createGetModuleFromFilename } from './module';\n/**\n * @deprecated use `createGetModuleFromFilename` instead.\n */\nexport const getModuleFromFilename = createGetModuleFromFilename();\nexport { createGetModuleFromFilename };\n\n// eslint-disable-next-line deprecation/deprecation\nexport { enableAnrDetection } from './integrations/anr/legacy';\n\nimport { Integrations as CoreIntegrations } from '@sentry/core';\n\nimport * as Handlers from './handlers';\nimport * as NodeIntegrations from './integrations';\nimport * as TracingIntegrations from './tracing/integrations';\n\n// TODO: Deprecate this once we migrated tracing integrations\nexport const Integrations = {\n  // eslint-disable-next-line deprecation/deprecation\n  ...CoreIntegrations,\n  ...NodeIntegrations,\n  ...TracingIntegrations,\n};\n\nexport {\n  captureConsoleIntegration,\n  dedupeIntegration,\n  debugIntegration,\n  extraErrorDataIntegration,\n  reportingObserverIntegration,\n  rewriteFramesIntegration,\n  sessionTimingIntegration,\n  httpClientIntegration,\n} from '@sentry/integrations';\n\nexport { consoleIntegration } from './integrations/console';\nexport { onUncaughtExceptionIntegration } from './integrations/onuncaughtexception';\nexport { onUnhandledRejectionIntegration } from './integrations/onunhandledrejection';\nexport { modulesIntegration } from './integrations/modules';\nexport { contextLinesIntegration } from './integrations/contextlines';\nexport { nodeContextIntegration } from './integrations/context';\nexport { localVariablesIntegration } from './integrations/local-variables';\nexport { spotlightIntegration } from './integrations/spotlight';\nexport { anrIntegration } from './integrations/anr';\nexport { hapiIntegration } from './integrations/hapi';\n// eslint-disable-next-line deprecation/deprecation\nexport { Undici, nativeNodeFetchintegration } from './integrations/undici';\n// eslint-disable-next-line deprecation/deprecation\nexport { Http, httpIntegration } from './integrations/http';\n\n// TODO(v8): Remove all of these exports. They were part of a hotfix #10339 where we produced wrong .d.ts files because we were packing packages inside the /build folder.\nexport type { LocalVariablesIntegrationOptions } from './integrations/local-variables/common';\nexport type { DebugSession } from './integrations/local-variables/local-variables-sync';\nexport type { AnrIntegrationOptions } from './integrations/anr/common';\n// ---\n\nexport { Handlers };\n\nexport { trpcMiddleware } from './trpc';\n\nexport { hapiErrorPlugin } from './integrations/hapi';\n\nimport { instrumentCron } from './cron/cron';\nimport { instrumentNodeCron } from './cron/node-cron';\nimport { instrumentNodeSchedule } from './cron/node-schedule';\n\n/** Methods to instrument cron libraries for Sentry check-ins */\nexport const cron = {\n  instrumentCron,\n  instrumentNodeCron,\n  instrumentNodeSchedule,\n};\n"], "names": ["CoreIntegrations", "NodeIntegrations", "TracingIntegrations"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HA;AACA;AACA;AACa,MAAA,qBAAA,GAAwB,2BAA2B,GAAE;AAWlE;AACA;AACO,MAAM,eAAe;AAC5B;AACA,EAAE,GAAGA,cAAgB;AACrB,EAAE,GAAGC,KAAgB;AACrB,EAAE,GAAGC,YAAmB;AACxB,EAAC;AA2CD;AACA;AACO,MAAM,OAAO;AACpB,EAAE,cAAc;AAChB,EAAE,kBAAkB;AACpB,EAAE,sBAAsB;AACxB;;;;"}