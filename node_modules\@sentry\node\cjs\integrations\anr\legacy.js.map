{"version": 3, "file": "legacy.js", "sources": ["../../../../src/integrations/anr/legacy.ts"], "sourcesContent": ["import { getClient } from '@sentry/core';\nimport { Anr } from '.';\nimport type { NodeClient } from '../../client';\n\n// TODO (v8): Remove this entire file and the `enableAnrDetection` export\n\ninterface LegacyOptions {\n  entryScript: string;\n  pollInterval: number;\n  anrThreshold: number;\n  captureStackTrace: boolean;\n  debug: boolean;\n}\n\n/**\n * @deprecated Use the `Anr` integration instead.\n *\n * ```ts\n * import * as Sentry from '@sentry/node';\n *\n * Sentry.init({\n *   dsn: '__DSN__',\n *   integrations: [new Sentry.Integrations.Anr({ captureStackTrace: true })],\n * });\n * ```\n */\nexport function enableAnrDetection(options: Partial<LegacyOptions>): Promise<void> {\n  const client = getClient() as NodeClient;\n  // eslint-disable-next-line deprecation/deprecation\n  const integration = new Anr(options);\n  integration.setup(client);\n  return Promise.resolve();\n}\n"], "names": ["getClient", "<PERSON><PERSON>"], "mappings": ";;;;;AAIA;;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,OAAO,EAAyC;AACnF,EAAE,MAAM,MAAA,GAASA,cAAS,EAAG,EAAA;AAC7B;AACA,EAAE,MAAM,WAAY,GAAE,IAAIC,SAAG,CAAC,OAAO,CAAC,CAAA;AACtC,EAAE,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;AAC3B,EAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;AAC1B;;;;"}