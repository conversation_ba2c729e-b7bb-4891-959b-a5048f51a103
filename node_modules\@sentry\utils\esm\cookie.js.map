{"version": 3, "file": "cookie.js", "sources": ["../../src/cookie.ts"], "sourcesContent": ["/**\n * This code was originally copied from the 'cookie` module at v0.5.0 and was simplified for our use case.\n * https://github.com/jshttp/cookie/blob/a0c84147aab6266bdb3996cf4062e93907c0b0fc/index.js\n * It had the following license:\n *\n * (The MIT License)\n *\n * Copyright (c) 2012-2014 <PERSON> <<EMAIL>>\n * Copyright (c) 2015 <PERSON> <<EMAIL>>\n *\n * Permission is hereby granted, free of charge, to any person obtaining\n * a copy of this software and associated documentation files (the\n * 'Software'), to deal in the Software without restriction, including\n * without limitation the rights to use, copy, modify, merge, publish,\n * distribute, sublicense, and/or sell copies of the Software, and to\n * permit persons to whom the Software is furnished to do so, subject to\n * the following conditions:\n *\n * The above copyright notice and this permission notice shall be\n * included in all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\n * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\n * IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\n * CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n * TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\n * SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n */\n\n/**\n * Parses a cookie string\n */\nexport function parseCookie(str: string): Record<string, string> {\n  const obj: Record<string, string> = {};\n  let index = 0;\n\n  while (index < str.length) {\n    const eqIdx = str.indexOf('=', index);\n\n    // no more cookie pairs\n    if (eqIdx === -1) {\n      break;\n    }\n\n    let endIdx = str.indexOf(';', index);\n\n    if (endIdx === -1) {\n      endIdx = str.length;\n    } else if (endIdx < eqIdx) {\n      // backtrack on prior semicolon\n      index = str.lastIndexOf(';', eqIdx - 1) + 1;\n      continue;\n    }\n\n    const key = str.slice(index, eqIdx).trim();\n\n    // only assign once\n    if (undefined === obj[key]) {\n      let val = str.slice(eqIdx + 1, endIdx).trim();\n\n      // quoted values\n      if (val.charCodeAt(0) === 0x22) {\n        val = val.slice(1, -1);\n      }\n\n      try {\n        obj[key] = val.indexOf('%') !== -1 ? decodeURIComponent(val) : val;\n      } catch (e) {\n        obj[key] = val;\n      }\n    }\n\n    index = endIdx + 1;\n  }\n\n  return obj;\n}\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,GAAG,EAAkC;AACjE,EAAE,MAAM,GAAG,GAA2B,EAAE,CAAA;AACxC,EAAE,IAAI,KAAM,GAAE,CAAC,CAAA;AACf;AACA,EAAE,OAAO,KAAA,GAAQ,GAAG,CAAC,MAAM,EAAE;AAC7B,IAAI,MAAM,KAAM,GAAE,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AACzC;AACA;AACA,IAAI,IAAI,KAAA,KAAU,CAAC,CAAC,EAAE;AACtB,MAAM,MAAK;AACX,KAAI;AACJ;AACA,IAAI,IAAI,MAAO,GAAE,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AACxC;AACA,IAAI,IAAI,MAAA,KAAW,CAAC,CAAC,EAAE;AACvB,MAAM,MAAO,GAAE,GAAG,CAAC,MAAM,CAAA;AACzB,WAAW,IAAI,MAAO,GAAE,KAAK,EAAE;AAC/B;AACA,MAAM,KAAM,GAAE,GAAG,CAAC,WAAW,CAAC,GAAG,EAAE,KAAM,GAAE,CAAC,CAAA,GAAI,CAAC,CAAA;AACjD,MAAM,SAAQ;AACd,KAAI;AACJ;AACA,IAAI,MAAM,GAAA,GAAM,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,EAAE,CAAA;AAC9C;AACA;AACA,IAAI,IAAI,SAAU,KAAI,GAAG,CAAC,GAAG,CAAC,EAAE;AAChC,MAAM,IAAI,GAAI,GAAE,GAAG,CAAC,KAAK,CAAC,KAAA,GAAQ,CAAC,EAAE,MAAM,CAAC,CAAC,IAAI,EAAE,CAAA;AACnD;AACA;AACA,MAAM,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAA,KAAM,IAAI,EAAE;AACtC,QAAQ,GAAA,GAAM,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AAC9B,OAAM;AACN;AACA,MAAM,IAAI;AACV,QAAQ,GAAG,CAAC,GAAG,CAAA,GAAI,GAAG,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,IAAI,kBAAkB,CAAC,GAAG,CAAA,GAAI,GAAG,CAAA;AAC1E,OAAQ,CAAA,OAAO,CAAC,EAAE;AAClB,QAAQ,GAAG,CAAC,GAAG,CAAA,GAAI,GAAG,CAAA;AACtB,OAAM;AACN,KAAI;AACJ;AACA,IAAI,KAAM,GAAE,MAAO,GAAE,CAAC,CAAA;AACtB,GAAE;AACF;AACA,EAAE,OAAO,GAAG,CAAA;AACZ;;;;"}