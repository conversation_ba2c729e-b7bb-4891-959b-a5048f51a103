{"version": 3, "file": "index.js", "sources": ["../../../src/instrument/index.ts"], "sourcesContent": ["// TODO(v8): Consider moving this file (or at least parts of it) into the browser package. The registered handlers are mostly non-generic and we risk leaking runtime specific code into generic packages.\n\nimport { DEBUG_BUILD } from '../debug-build';\nimport { logger } from './../logger';\nimport type {\n  InstrumentHandlerCallback as _InstrumentHandlerCallback,\n  InstrumentHandlerType as _InstrumentHandlerType,\n} from './_handlers';\nimport { resetInstrumentationHandlers } from './_handlers';\nimport { addConsoleInstrumentationHandler } from './console';\nimport { addClickKeypressInstrumentationHandler } from './dom';\nimport { addFetchInstrumentationHandler } from './fetch';\nimport { addGlobalErrorInstrumentationHandler } from './globalError';\nimport { addGlobalUnhandledRejectionInstrumentationHandler } from './globalUnhandledRejection';\nimport { addHistoryInstrumentationHandler } from './history';\nimport { SENTRY_XHR_DATA_KEY, addXhrInstrumentationHandler } from './xhr';\n\n/**\n * Add handler that will be called when given type of instrumentation triggers.\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n * @deprecated Use the proper function per instrumentation type instead!\n */\nexport function addInstrumentationHandler(type: _InstrumentHandlerType, callback: _InstrumentHandlerCallback): void {\n  switch (type) {\n    case 'console':\n      return addConsoleInstrumentationHandler(callback);\n    case 'dom':\n      return addClickKeypressInstrumentationHandler(callback);\n    case 'xhr':\n      return addXhrInstrumentationHandler(callback);\n    case 'fetch':\n      return addFetchInstrumentationHandler(callback);\n    case 'history':\n      return addHistoryInstrumentationHandler(callback);\n    case 'error':\n      return addGlobalErrorInstrumentationHandler(callback);\n    case 'unhandledrejection':\n      return addGlobalUnhandledRejectionInstrumentationHandler(callback);\n    default:\n      DEBUG_BUILD && logger.warn('unknown instrumentation type:', type);\n  }\n}\n\n/**\n * @deprecated Use the specific handler data types from @sentry/types instead, e.g. HandlerDataFetch, HandlerDataConsole, ...\n */\ntype InstrumentHandlerCallback = _InstrumentHandlerCallback;\n\n/**\n * @deprecated Use the specific handler functions instead, e.g. addConsoleInstrumentationHandler, ...\n */\ntype InstrumentHandlerType = _InstrumentHandlerType;\n\n// eslint-disable-next-line deprecation/deprecation\nexport type { InstrumentHandlerCallback, InstrumentHandlerType };\n\nexport {\n  addConsoleInstrumentationHandler,\n  addClickKeypressInstrumentationHandler,\n  addXhrInstrumentationHandler,\n  addFetchInstrumentationHandler,\n  addHistoryInstrumentationHandler,\n  addGlobalErrorInstrumentationHandler,\n  addGlobalUnhandledRejectionInstrumentationHandler,\n  SENTRY_XHR_DATA_KEY,\n  // Only exported for tests\n  resetInstrumentationHandlers,\n};\n"], "names": ["addConsoleInstrumentationHandler", "addClickKeypressInstrumentationHandler", "addXhrInstrumentationHandler", "addFetchInstrumentationHandler", "addHistoryInstrumentationHandler", "addGlobalErrorInstrumentationHandler", "addGlobalUnhandledRejectionInstrumentationHandler", "DEBUG_BUILD", "logger"], "mappings": ";;;;;;;;;;;;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB,CAAC,IAAI,EAA0B,QAAQ,EAAoC;AACpH,EAAE,QAAQ,IAAI;AACd,IAAI,KAAK,SAAS;AAClB,MAAM,OAAOA,wCAAgC,CAAC,QAAQ,CAAC,CAAA;AACvD,IAAI,KAAK,KAAK;AACd,MAAM,OAAOC,0CAAsC,CAAC,QAAQ,CAAC,CAAA;AAC7D,IAAI,KAAK,KAAK;AACd,MAAM,OAAOC,gCAA4B,CAAC,QAAQ,CAAC,CAAA;AACnD,IAAI,KAAK,OAAO;AAChB,MAAM,OAAOC,oCAA8B,CAAC,QAAQ,CAAC,CAAA;AACrD,IAAI,KAAK,SAAS;AAClB,MAAM,OAAOC,wCAAgC,CAAC,QAAQ,CAAC,CAAA;AACvD,IAAI,KAAK,OAAO;AAChB,MAAM,OAAOC,gDAAoC,CAAC,QAAQ,CAAC,CAAA;AAC3D,IAAI,KAAK,oBAAoB;AAC7B,MAAM,OAAOC,0EAAiD,CAAC,QAAQ,CAAC,CAAA;AACxE,IAAI;AACJ,MAAMC,sBAAA,IAAeC,aAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,IAAI,CAAC,CAAA;AACvE,GAAE;AACF;;;;;;;;;;;;"}