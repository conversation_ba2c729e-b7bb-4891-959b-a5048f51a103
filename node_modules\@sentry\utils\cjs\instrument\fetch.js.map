{"version": 3, "file": "fetch.js", "sources": ["../../../src/instrument/fetch.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/ban-types */\nimport type { HandlerDataFetch } from '@sentry/types';\n\nimport { fill } from '../object';\nimport { supportsNativeFetch } from '../supports';\nimport { GLOBAL_OBJ } from '../worldwide';\nimport { addHandler, maybeInstrument, triggerHandlers } from './_handlers';\n\ntype FetchResource = string | { toString(): string } | { url: string };\n\n/**\n * Add an instrumentation handler for when a fetch request happens.\n * The handler function is called once when the request starts and once when it ends,\n * which can be identified by checking if it has an `endTimestamp`.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addFetchInstrumentationHandler(handler: (data: HandlerDataFetch) => void): void {\n  const type = 'fetch';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentFetch);\n}\n\nfunction instrumentFetch(): void {\n  if (!supportsNativeFetch()) {\n    return;\n  }\n\n  fill(GLOBAL_OBJ, 'fetch', function (originalFetch: () => void): () => void {\n    return function (...args: any[]): void {\n      const { method, url } = parseFetchArgs(args);\n\n      const handlerData: HandlerDataFetch = {\n        args,\n        fetchData: {\n          method,\n          url,\n        },\n        startTimestamp: Date.now(),\n      };\n\n      triggerHandlers('fetch', {\n        ...handlerData,\n      });\n\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      return originalFetch.apply(GLOBAL_OBJ, args).then(\n        (response: Response) => {\n          const finishedHandlerData: HandlerDataFetch = {\n            ...handlerData,\n            endTimestamp: Date.now(),\n            response,\n          };\n\n          triggerHandlers('fetch', finishedHandlerData);\n          return response;\n        },\n        (error: Error) => {\n          const erroredHandlerData: HandlerDataFetch = {\n            ...handlerData,\n            endTimestamp: Date.now(),\n            error,\n          };\n\n          triggerHandlers('fetch', erroredHandlerData);\n          // NOTE: If you are a Sentry user, and you are seeing this stack frame,\n          //       it means the sentry.javascript SDK caught an error invoking your application code.\n          //       This is expected behavior and NOT indicative of a bug with sentry.javascript.\n          throw error;\n        },\n      );\n    };\n  });\n}\n\nfunction hasProp<T extends string>(obj: unknown, prop: T): obj is Record<string, string> {\n  return !!obj && typeof obj === 'object' && !!(obj as Record<string, string>)[prop];\n}\n\nfunction getUrlFromResource(resource: FetchResource): string {\n  if (typeof resource === 'string') {\n    return resource;\n  }\n\n  if (!resource) {\n    return '';\n  }\n\n  if (hasProp(resource, 'url')) {\n    return resource.url;\n  }\n\n  if (resource.toString) {\n    return resource.toString();\n  }\n\n  return '';\n}\n\n/**\n * Parses the fetch arguments to find the used Http method and the url of the request.\n * Exported for tests only.\n */\nexport function parseFetchArgs(fetchArgs: unknown[]): { method: string; url: string } {\n  if (fetchArgs.length === 0) {\n    return { method: 'GET', url: '' };\n  }\n\n  if (fetchArgs.length === 2) {\n    const [url, options] = fetchArgs as [FetchResource, object];\n\n    return {\n      url: getUrlFromResource(url),\n      method: hasProp(options, 'method') ? String(options.method).toUpperCase() : 'GET',\n    };\n  }\n\n  const arg = fetchArgs[0];\n  return {\n    url: getUrlFromResource(arg as FetchResource),\n    method: hasProp(arg, 'method') ? String(arg.method).toUpperCase() : 'GET',\n  };\n}\n"], "names": ["add<PERSON><PERSON><PERSON>", "maybeInstrument", "supportsNativeFetch", "fill", "GLOBAL_OBJ", "triggerHandlers"], "mappings": ";;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,8BAA8B,CAAC,OAAO,EAA0C;AAChG,EAAE,MAAM,IAAK,GAAE,OAAO,CAAA;AACtB,EAAEA,oBAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAC3B,EAAEC,yBAAe,CAAC,IAAI,EAAE,eAAe,CAAC,CAAA;AACxC,CAAA;AACA;AACA,SAAS,eAAe,GAAS;AACjC,EAAE,IAAI,CAACC,4BAAmB,EAAE,EAAE;AAC9B,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAEC,WAAI,CAACC,oBAAU,EAAE,OAAO,EAAE,UAAU,aAAa,EAA0B;AAC7E,IAAI,OAAO,UAAU,GAAG,IAAI,EAAe;AAC3C,MAAM,MAAM,EAAE,MAAM,EAAE,GAAA,KAAQ,cAAc,CAAC,IAAI,CAAC,CAAA;AAClD;AACA,MAAM,MAAM,WAAW,GAAqB;AAC5C,QAAQ,IAAI;AACZ,QAAQ,SAAS,EAAE;AACnB,UAAU,MAAM;AAChB,UAAU,GAAG;AACb,SAAS;AACT,QAAQ,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE;AAClC,OAAO,CAAA;AACP;AACA,MAAMC,yBAAe,CAAC,OAAO,EAAE;AAC/B,QAAQ,GAAG,WAAW;AACtB,OAAO,CAAC,CAAA;AACR;AACA;AACA,MAAM,OAAO,aAAa,CAAC,KAAK,CAACD,oBAAU,EAAE,IAAI,CAAC,CAAC,IAAI;AACvD,QAAQ,CAAC,QAAQ,KAAe;AAChC,UAAU,MAAM,mBAAmB,GAAqB;AACxD,YAAY,GAAG,WAAW;AAC1B,YAAY,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;AACpC,YAAY,QAAQ;AACpB,WAAW,CAAA;AACX;AACA,UAAUC,yBAAe,CAAC,OAAO,EAAE,mBAAmB,CAAC,CAAA;AACvD,UAAU,OAAO,QAAQ,CAAA;AACzB,SAAS;AACT,QAAQ,CAAC,KAAK,KAAY;AAC1B,UAAU,MAAM,kBAAkB,GAAqB;AACvD,YAAY,GAAG,WAAW;AAC1B,YAAY,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;AACpC,YAAY,KAAK;AACjB,WAAW,CAAA;AACX;AACA,UAAUA,yBAAe,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAA;AACtD;AACA;AACA;AACA,UAAU,MAAM,KAAK,CAAA;AACrB,SAAS;AACT,OAAO,CAAA;AACP,KAAK,CAAA;AACL,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA,SAAS,OAAO,CAAmB,GAAG,EAAW,IAAI,EAAoC;AACzF,EAAE,OAAO,CAAC,CAAC,GAAI,IAAG,OAAO,GAAI,KAAI,QAAS,IAAG,CAAC,CAAC,CAAC,MAA+B,IAAI,CAAC,CAAA;AACpF,CAAA;AACA;AACA,SAAS,kBAAkB,CAAC,QAAQ,EAAyB;AAC7D,EAAE,IAAI,OAAO,QAAS,KAAI,QAAQ,EAAE;AACpC,IAAI,OAAO,QAAQ,CAAA;AACnB,GAAE;AACF;AACA,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,OAAO,EAAE,CAAA;AACb,GAAE;AACF;AACA,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;AAChC,IAAI,OAAO,QAAQ,CAAC,GAAG,CAAA;AACvB,GAAE;AACF;AACA,EAAE,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACzB,IAAI,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAA;AAC9B,GAAE;AACF;AACA,EAAE,OAAO,EAAE,CAAA;AACX,CAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAC,SAAS,EAA8C;AACtF,EAAE,IAAI,SAAS,CAAC,MAAO,KAAI,CAAC,EAAE;AAC9B,IAAI,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAA,EAAI,CAAA;AACrC,GAAE;AACF;AACA,EAAE,IAAI,SAAS,CAAC,MAAO,KAAI,CAAC,EAAE;AAC9B,IAAI,MAAM,CAAC,GAAG,EAAE,OAAO,CAAA,GAAI,SAAU,EAAA;AACrC;AACA,IAAI,OAAO;AACX,MAAM,GAAG,EAAE,kBAAkB,CAAC,GAAG,CAAC;AAClC,MAAM,MAAM,EAAE,OAAO,CAAC,OAAO,EAAE,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,EAAC,GAAI,KAAK;AACvF,KAAK,CAAA;AACL,GAAE;AACF;AACA,EAAE,MAAM,GAAI,GAAE,SAAS,CAAC,CAAC,CAAC,CAAA;AAC1B,EAAE,OAAO;AACT,IAAI,GAAG,EAAE,kBAAkB,CAAC,KAAqB;AACjD,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,EAAE,QAAQ,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAC,GAAI,KAAK;AAC7E,GAAG,CAAA;AACH;;;;;"}