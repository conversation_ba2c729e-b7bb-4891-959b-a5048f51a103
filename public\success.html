<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Cashfree</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .success-icon {
            font-size: 80px;
            color: #4CAF50;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .order-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: left;
        }

        .order-details h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #555;
        }

        .detail-value {
            color: #333;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .powered-by {
            margin-top: 30px;
            color: #999;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✅</div>
        
        <div class="header">
            <h1>Payment Successful!</h1>
            <p>Thank you for your payment. Your transaction has been completed successfully.</p>
        </div>

        <div class="order-details" id="order-details">
            <h3>Transaction Details</h3>
            <div class="detail-row">
                <span class="detail-label">Order ID:</span>
                <span class="detail-value" id="order-id">Loading...</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Transaction ID:</span>
                <span class="detail-value" id="transaction-id">Loading...</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="detail-value" style="color: #4CAF50; font-weight: bold;">SUCCESS</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date & Time:</span>
                <span class="detail-value" id="transaction-time">Loading...</span>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <p>Fetching transaction details...</p>
        </div>

        <div class="actions">
            <a href="/" class="btn btn-primary">Make Another Payment</a>
            <button onclick="window.print()" class="btn btn-secondary">Print Receipt</button>
        </div>

        <div class="powered-by">
            Powered by <strong>Cashfree Payments</strong>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const orderId = urlParams.get('order_id');
        const orderToken = urlParams.get('order_token');

        // Update order ID immediately
        if (orderId) {
            document.getElementById('order-id').textContent = orderId;
        }

        // Set current time
        document.getElementById('transaction-time').textContent = new Date().toLocaleString();

        // Fetch additional transaction details
        async function fetchTransactionDetails() {
            if (!orderId) {
                document.getElementById('loading').style.display = 'none';
                return;
            }

            try {
                const response = await fetch(`/payment/status/${orderId}`);
                const result = await response.json();

                if (response.ok && result.success && result.payments && result.payments.length > 0) {
                    const payment = result.payments[0];
                    
                    // Update transaction details
                    if (payment.cf_payment_id) {
                        document.getElementById('transaction-id').textContent = payment.cf_payment_id;
                    }
                    
                    if (payment.payment_time) {
                        document.getElementById('transaction-time').textContent = 
                            new Date(payment.payment_time).toLocaleString();
                    }
                } else {
                    console.log('No additional payment details available');
                    document.getElementById('transaction-id').textContent = 'N/A';
                }
            } catch (error) {
                console.error('Error fetching transaction details:', error);
                document.getElementById('transaction-id').textContent = 'N/A';
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }

        // Fetch details on page load
        fetchTransactionDetails();

        // Auto-redirect after 30 seconds (optional)
        setTimeout(() => {
            const autoRedirect = confirm('Would you like to make another payment?');
            if (autoRedirect) {
                window.location.href = '/';
            }
        }, 30000);
    </script>
</body>
</html>
