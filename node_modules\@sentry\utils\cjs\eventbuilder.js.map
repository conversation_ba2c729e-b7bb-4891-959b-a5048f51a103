{"version": 3, "file": "eventbuilder.js", "sources": ["../../src/eventbuilder.ts"], "sourcesContent": ["import type {\n  Client,\n  Event,\n  EventHint,\n  Exception,\n  Extras,\n  Hub,\n  Mechanism,\n  ParameterizedString,\n  Severity,\n  SeverityLevel,\n  StackFrame,\n  StackParser,\n} from '@sentry/types';\n\nimport { isError, isParameterizedString, isPlainObject } from './is';\nimport { addExceptionMechanism, addExceptionTypeValue } from './misc';\nimport { normalizeToSize } from './normalize';\nimport { extractExceptionKeysForMessage } from './object';\n\n/**\n * Extracts stack frames from the error.stack string\n */\nexport function parseStackFrames(stackParser: StackParser, error: Error): StackFrame[] {\n  return stackParser(error.stack || '', 1);\n}\n\n/**\n * Extracts stack frames from the error and builds a Sentry Exception\n */\nexport function exceptionFromError(stackParser: StackParser, error: Error): Exception {\n  const exception: Exception = {\n    type: error.name || error.constructor.name,\n    value: error.message,\n  };\n\n  const frames = parseStackFrames(stackParser, error);\n  if (frames.length) {\n    exception.stacktrace = { frames };\n  }\n\n  return exception;\n}\n\nfunction getMessageForObject(exception: object): string {\n  if ('name' in exception && typeof exception.name === 'string') {\n    let message = `'${exception.name}' captured as exception`;\n\n    if ('message' in exception && typeof exception.message === 'string') {\n      message += ` with message '${exception.message}'`;\n    }\n\n    return message;\n  } else if ('message' in exception && typeof exception.message === 'string') {\n    return exception.message;\n  } else {\n    // This will allow us to group events based on top-level keys\n    // which is much better than creating new group when any key/value change\n    return `Object captured as exception with keys: ${extractExceptionKeysForMessage(\n      exception as Record<string, unknown>,\n    )}`;\n  }\n}\n\n/**\n * Builds and Event from a Exception\n *\n * TODO(v8): Remove getHub fallback\n * @hidden\n */\nexport function eventFromUnknownInput(\n  // eslint-disable-next-line deprecation/deprecation\n  getHubOrClient: (() => Hub) | Client | undefined,\n  stackParser: StackParser,\n  exception: unknown,\n  hint?: EventHint,\n): Event {\n  const client =\n    typeof getHubOrClient === 'function'\n      ? // eslint-disable-next-line deprecation/deprecation\n        getHubOrClient().getClient()\n      : getHubOrClient;\n\n  let ex: unknown = exception;\n  const providedMechanism: Mechanism | undefined =\n    hint && hint.data && (hint.data as { mechanism: Mechanism }).mechanism;\n  const mechanism: Mechanism = providedMechanism || {\n    handled: true,\n    type: 'generic',\n  };\n\n  let extras: Extras | undefined;\n\n  if (!isError(exception)) {\n    if (isPlainObject(exception)) {\n      const normalizeDepth = client && client.getOptions().normalizeDepth;\n      extras = { ['__serialized__']: normalizeToSize(exception as Record<string, unknown>, normalizeDepth) };\n\n      const message = getMessageForObject(exception);\n      ex = (hint && hint.syntheticException) || new Error(message);\n      (ex as Error).message = message;\n    } else {\n      // This handles when someone does: `throw \"something awesome\";`\n      // We use synthesized Error here so we can extract a (rough) stack trace.\n      ex = (hint && hint.syntheticException) || new Error(exception as string);\n      (ex as Error).message = exception as string;\n    }\n    mechanism.synthetic = true;\n  }\n\n  const event: Event = {\n    exception: {\n      values: [exceptionFromError(stackParser, ex as Error)],\n    },\n  };\n\n  if (extras) {\n    event.extra = extras;\n  }\n\n  addExceptionTypeValue(event, undefined, undefined);\n  addExceptionMechanism(event, mechanism);\n\n  return {\n    ...event,\n    event_id: hint && hint.event_id,\n  };\n}\n\n/**\n * Builds and Event from a Message\n * @hidden\n */\nexport function eventFromMessage(\n  stackParser: StackParser,\n  message: ParameterizedString,\n  // eslint-disable-next-line deprecation/deprecation\n  level: Severity | SeverityLevel = 'info',\n  hint?: EventHint,\n  attachStacktrace?: boolean,\n): Event {\n  const event: Event = {\n    event_id: hint && hint.event_id,\n    level,\n  };\n\n  if (attachStacktrace && hint && hint.syntheticException) {\n    const frames = parseStackFrames(stackParser, hint.syntheticException);\n    if (frames.length) {\n      event.exception = {\n        values: [\n          {\n            value: message,\n            stacktrace: { frames },\n          },\n        ],\n      };\n    }\n  }\n\n  if (isParameterizedString(message)) {\n    const { __sentry_template_string__, __sentry_template_values__ } = message;\n\n    event.logentry = {\n      message: __sentry_template_string__,\n      params: __sentry_template_values__,\n    };\n    return event;\n  }\n\n  event.message = message;\n  return event;\n}\n"], "names": ["extractExceptionKeysForMessage", "isError", "isPlainObject", "normalizeToSize", "addExceptionTypeValue", "addExceptionMechanism", "isParameterizedString"], "mappings": ";;;;;;;AAoBA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,WAAW,EAAe,KAAK,EAAuB;AACvF,EAAE,OAAO,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAA;AAC1C,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,WAAW,EAAe,KAAK,EAAoB;AACtF,EAAE,MAAM,SAAS,GAAc;AAC/B,IAAI,IAAI,EAAE,KAAK,CAAC,IAAA,IAAQ,KAAK,CAAC,WAAW,CAAC,IAAI;AAC9C,IAAI,KAAK,EAAE,KAAK,CAAC,OAAO;AACxB,GAAG,CAAA;AACH;AACA,EAAE,MAAM,SAAS,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;AACrD,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;AACrB,IAAI,SAAS,CAAC,UAAA,GAAa,EAAE,QAAQ,CAAA;AACrC,GAAE;AACF;AACA,EAAE,OAAO,SAAS,CAAA;AAClB,CAAA;AACA;AACA,SAAS,mBAAmB,CAAC,SAAS,EAAkB;AACxD,EAAE,IAAI,MAAO,IAAG,SAAU,IAAG,OAAO,SAAS,CAAC,IAAA,KAAS,QAAQ,EAAE;AACjE,IAAI,IAAI,OAAQ,GAAE,CAAC,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAA;AAC7D;AACA,IAAI,IAAI,SAAU,IAAG,SAAU,IAAG,OAAO,SAAS,CAAC,OAAA,KAAY,QAAQ,EAAE;AACzE,MAAM,OAAA,IAAW,CAAC,eAAe,EAAE,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;AACvD,KAAI;AACJ;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAI,MAAK,IAAI,aAAa,SAAA,IAAa,OAAO,SAAS,CAAC,OAAQ,KAAI,QAAQ,EAAE;AAC9E,IAAI,OAAO,SAAS,CAAC,OAAO,CAAA;AAC5B,SAAS;AACT;AACA;AACA,IAAI,OAAO,CAAC,wCAAwC,EAAEA,qCAA8B;AACpF,MAAM,SAAU;AAChB,KAAK,CAAC,CAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA;AACA;AACA,EAAA,cAAA;AACA,EAAA,WAAA;AACA,EAAA,SAAA;AACA,EAAA,IAAA;AACA,EAAA;AACA,EAAA,MAAA,MAAA;AACA,IAAA,OAAA,cAAA,KAAA,UAAA;AACA;AACA,QAAA,cAAA,EAAA,CAAA,SAAA,EAAA;AACA,QAAA,cAAA,CAAA;AACA;AACA,EAAA,IAAA,EAAA,GAAA,SAAA,CAAA;AACA,EAAA,MAAA,iBAAA;AACA,IAAA,IAAA,IAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,SAAA,CAAA;AACA,EAAA,MAAA,SAAA,GAAA,iBAAA,IAAA;AACA,IAAA,OAAA,EAAA,IAAA;AACA,IAAA,IAAA,EAAA,SAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,IAAA,MAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAAC,UAAA,CAAA,SAAA,CAAA,EAAA;AACA,IAAA,IAAAC,gBAAA,CAAA,SAAA,CAAA,EAAA;AACA,MAAA,MAAA,cAAA,GAAA,MAAA,IAAA,MAAA,CAAA,UAAA,EAAA,CAAA,cAAA,CAAA;AACA,MAAA,MAAA,GAAA,EAAA,CAAA,gBAAA,GAAAC,yBAAA,CAAA,SAAA,GAAA,cAAA,CAAA,EAAA,CAAA;AACA;AACA,MAAA,MAAA,OAAA,GAAA,mBAAA,CAAA,SAAA,CAAA,CAAA;AACA,MAAA,EAAA,GAAA,CAAA,IAAA,IAAA,IAAA,CAAA,kBAAA,KAAA,IAAA,KAAA,CAAA,OAAA,CAAA,CAAA;AACA,MAAA,CAAA,EAAA,GAAA,OAAA,GAAA,OAAA,CAAA;AACA,KAAA,MAAA;AACA;AACA;AACA,MAAA,EAAA,GAAA,CAAA,IAAA,IAAA,IAAA,CAAA,kBAAA,KAAA,IAAA,KAAA,CAAA,SAAA,EAAA,CAAA;AACA,MAAA,CAAA,EAAA,GAAA,OAAA,GAAA,SAAA,EAAA;AACA,KAAA;AACA,IAAA,SAAA,CAAA,SAAA,GAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,KAAA,GAAA;AACA,IAAA,SAAA,EAAA;AACA,MAAA,MAAA,EAAA,CAAA,kBAAA,CAAA,WAAA,EAAA,EAAA,EAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,IAAA,MAAA,EAAA;AACA,IAAA,KAAA,CAAA,KAAA,GAAA,MAAA,CAAA;AACA,GAAA;AACA;AACA,EAAAC,0BAAA,CAAA,KAAA,EAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AACA,EAAAC,0BAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA;AACA;AACA,EAAA,OAAA;AACA,IAAA,GAAA,KAAA;AACA,IAAA,QAAA,EAAA,IAAA,IAAA,IAAA,CAAA,QAAA;AACA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,gBAAA;AACA,EAAA,WAAA;AACA,EAAA,OAAA;AACA;AACA,EAAA,KAAA,GAAA,MAAA;AACA,EAAA,IAAA;AACA,EAAA,gBAAA;AACA,EAAA;AACA,EAAA,MAAA,KAAA,GAAA;AACA,IAAA,QAAA,EAAA,IAAA,IAAA,IAAA,CAAA,QAAA;AACA,IAAA,KAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,IAAA,gBAAA,IAAA,IAAA,IAAA,IAAA,CAAA,kBAAA,EAAA;AACA,IAAA,MAAA,MAAA,GAAA,gBAAA,CAAA,WAAA,EAAA,IAAA,CAAA,kBAAA,CAAA,CAAA;AACA,IAAA,IAAA,MAAA,CAAA,MAAA,EAAA;AACA,MAAA,KAAA,CAAA,SAAA,GAAA;AACA,QAAA,MAAA,EAAA;AACA,UAAA;AACA,YAAA,KAAA,EAAA,OAAA;AACA,YAAA,UAAA,EAAA,EAAA,MAAA,EAAA;AACA,WAAA;AACA,SAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,IAAAC,wBAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,MAAA,EAAA,0BAAA,EAAA,0BAAA,EAAA,GAAA,OAAA,CAAA;AACA;AACA,IAAA,KAAA,CAAA,QAAA,GAAA;AACA,MAAA,OAAA,EAAA,0BAAA;AACA,MAAA,MAAA,EAAA,0BAAA;AACA,KAAA,CAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,KAAA,CAAA,OAAA,GAAA,OAAA,CAAA;AACA,EAAA,OAAA,KAAA,CAAA;AACA;;;;;;;"}