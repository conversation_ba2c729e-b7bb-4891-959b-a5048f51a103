{"version": 3, "file": "request.js", "sources": ["../../../src/browser/request.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  getClient,\n  getCurrentScope,\n  getDynamicSamplingContextFromClient,\n  getDynamicSamplingContextFromSpan,\n  getIsolationScope,\n  hasTracingEnabled,\n  setHttpStatus,\n  spanToJSON,\n  spanToTraceHeader,\n  startInactiveSpan,\n} from '@sentry/core';\nimport type { HandlerDataXhr, SentryWrappedXMLHttpRequest, Span } from '@sentry/types';\nimport {\n  BAGGAGE_HEADER_NAME,\n  SENTRY_XHR_DATA_KEY,\n  addFetchInstrumentationHandler,\n  addXhrInstrumentationHandler,\n  browserPerformanceTimeOrigin,\n  dynamicSamplingContextToSentryBaggageHeader,\n  generateSentryTraceHeader,\n  parseUrl,\n  stringMatchesSomePattern,\n} from '@sentry/utils';\n\nimport { instrumentFetchRequest } from '../common/fetch';\nimport { addPerformanceInstrumentationHandler } from './instrument';\nimport { WINDOW } from './types';\n\nexport const DEFAULT_TRACE_PROPAGATION_TARGETS = ['localhost', /^\\/(?!\\/)/];\n\n/** Options for Request Instrumentation */\nexport interface RequestInstrumentationOptions {\n  /**\n   * @deprecated Will be removed in v8.\n   * Use `shouldCreateSpanForRequest` to control span creation and `tracePropagationTargets` to control\n   * trace header attachment.\n   */\n  tracingOrigins: Array<string | RegExp>;\n\n  /**\n   * List of strings and/or regexes used to determine which outgoing requests will have `sentry-trace` and `baggage`\n   * headers attached.\n   *\n   * @deprecated Use the top-level `tracePropagationTargets` option in `Sentry.init` instead.\n   * This option will be removed in v8.\n   *\n   * Default: ['localhost', /^\\//] @see {DEFAULT_TRACE_PROPAGATION_TARGETS}\n   */\n  tracePropagationTargets: Array<string | RegExp>;\n\n  /**\n   * Flag to disable patching all together for fetch requests.\n   *\n   * Default: true\n   */\n  traceFetch: boolean;\n\n  /**\n   * Flag to disable patching all together for xhr requests.\n   *\n   * Default: true\n   */\n  traceXHR: boolean;\n\n  /**\n   * If true, Sentry will capture http timings and add them to the corresponding http spans.\n   *\n   * Default: true\n   */\n  enableHTTPTimings: boolean;\n\n  /**\n   * This function will be called before creating a span for a request with the given url.\n   * Return false if you don't want a span for the given url.\n   *\n   * Default: (url: string) => true\n   */\n  shouldCreateSpanForRequest?(this: void, url: string): boolean;\n}\n\nexport const defaultRequestInstrumentationOptions: RequestInstrumentationOptions = {\n  traceFetch: true,\n  traceXHR: true,\n  enableHTTPTimings: true,\n  // TODO (v8): Remove this property\n  tracingOrigins: DEFAULT_TRACE_PROPAGATION_TARGETS,\n  tracePropagationTargets: DEFAULT_TRACE_PROPAGATION_TARGETS,\n};\n\n/** Registers span creators for xhr and fetch requests  */\nexport function instrumentOutgoingRequests(_options?: Partial<RequestInstrumentationOptions>): void {\n  const {\n    traceFetch,\n    traceXHR,\n    // eslint-disable-next-line deprecation/deprecation\n    tracePropagationTargets,\n    // eslint-disable-next-line deprecation/deprecation\n    tracingOrigins,\n    shouldCreateSpanForRequest,\n    enableHTTPTimings,\n  } = {\n    traceFetch: defaultRequestInstrumentationOptions.traceFetch,\n    traceXHR: defaultRequestInstrumentationOptions.traceXHR,\n    ..._options,\n  };\n\n  const shouldCreateSpan =\n    typeof shouldCreateSpanForRequest === 'function' ? shouldCreateSpanForRequest : (_: string) => true;\n\n  // TODO(v8) Remove tracingOrigins here\n  // The only reason we're passing it in here is because this instrumentOutgoingRequests function is publicly exported\n  // and we don't want to break the API. We can remove it in v8.\n  const shouldAttachHeadersWithTargets = (url: string): boolean =>\n    shouldAttachHeaders(url, tracePropagationTargets || tracingOrigins);\n\n  const spans: Record<string, Span> = {};\n\n  if (traceFetch) {\n    addFetchInstrumentationHandler(handlerData => {\n      const createdSpan = instrumentFetchRequest(handlerData, shouldCreateSpan, shouldAttachHeadersWithTargets, spans);\n      // We cannot use `window.location` in the generic fetch instrumentation,\n      // but we need it for reliable `server.address` attribute.\n      // so we extend this in here\n      if (createdSpan) {\n        const fullUrl = getFullURL(handlerData.fetchData.url);\n        const host = fullUrl ? parseUrl(fullUrl).host : undefined;\n        createdSpan.setAttributes({\n          'http.url': fullUrl,\n          'server.address': host,\n        });\n      }\n\n      if (enableHTTPTimings && createdSpan) {\n        addHTTPTimings(createdSpan);\n      }\n    });\n  }\n\n  if (traceXHR) {\n    addXhrInstrumentationHandler(handlerData => {\n      const createdSpan = xhrCallback(handlerData, shouldCreateSpan, shouldAttachHeadersWithTargets, spans);\n      if (enableHTTPTimings && createdSpan) {\n        addHTTPTimings(createdSpan);\n      }\n    });\n  }\n}\n\nfunction isPerformanceResourceTiming(entry: PerformanceEntry): entry is PerformanceResourceTiming {\n  return (\n    entry.entryType === 'resource' &&\n    'initiatorType' in entry &&\n    typeof (entry as PerformanceResourceTiming).nextHopProtocol === 'string' &&\n    (entry.initiatorType === 'fetch' || entry.initiatorType === 'xmlhttprequest')\n  );\n}\n\n/**\n * Creates a temporary observer to listen to the next fetch/xhr resourcing timings,\n * so that when timings hit their per-browser limit they don't need to be removed.\n *\n * @param span A span that has yet to be finished, must contain `url` on data.\n */\nfunction addHTTPTimings(span: Span): void {\n  const { url } = spanToJSON(span).data || {};\n\n  if (!url || typeof url !== 'string') {\n    return;\n  }\n\n  const cleanup = addPerformanceInstrumentationHandler('resource', ({ entries }) => {\n    entries.forEach(entry => {\n      if (isPerformanceResourceTiming(entry) && entry.name.endsWith(url)) {\n        const spanData = resourceTimingEntryToSpanData(entry);\n        spanData.forEach(data => span.setAttribute(...data));\n        // In the next tick, clean this handler up\n        // We have to wait here because otherwise this cleans itself up before it is fully done\n        setTimeout(cleanup);\n      }\n    });\n  });\n}\n\n/**\n * Converts ALPN protocol ids to name and version.\n *\n * (https://www.iana.org/assignments/tls-extensiontype-values/tls-extensiontype-values.xhtml#alpn-protocol-ids)\n * @param nextHopProtocol PerformanceResourceTiming.nextHopProtocol\n */\nexport function extractNetworkProtocol(nextHopProtocol: string): { name: string; version: string } {\n  let name = 'unknown';\n  let version = 'unknown';\n  let _name = '';\n  for (const char of nextHopProtocol) {\n    // http/1.1 etc.\n    if (char === '/') {\n      [name, version] = nextHopProtocol.split('/');\n      break;\n    }\n    // h2, h3 etc.\n    if (!isNaN(Number(char))) {\n      name = _name === 'h' ? 'http' : _name;\n      version = nextHopProtocol.split(_name)[1];\n      break;\n    }\n    _name += char;\n  }\n  if (_name === nextHopProtocol) {\n    // webrtc, ftp, etc.\n    name = _name;\n  }\n  return { name, version };\n}\n\nfunction getAbsoluteTime(time: number = 0): number {\n  return ((browserPerformanceTimeOrigin || performance.timeOrigin) + time) / 1000;\n}\n\nfunction resourceTimingEntryToSpanData(resourceTiming: PerformanceResourceTiming): [string, string | number][] {\n  const { name, version } = extractNetworkProtocol(resourceTiming.nextHopProtocol);\n\n  const timingSpanData: [string, string | number][] = [];\n\n  timingSpanData.push(['network.protocol.version', version], ['network.protocol.name', name]);\n\n  if (!browserPerformanceTimeOrigin) {\n    return timingSpanData;\n  }\n  return [\n    ...timingSpanData,\n    ['http.request.redirect_start', getAbsoluteTime(resourceTiming.redirectStart)],\n    ['http.request.fetch_start', getAbsoluteTime(resourceTiming.fetchStart)],\n    ['http.request.domain_lookup_start', getAbsoluteTime(resourceTiming.domainLookupStart)],\n    ['http.request.domain_lookup_end', getAbsoluteTime(resourceTiming.domainLookupEnd)],\n    ['http.request.connect_start', getAbsoluteTime(resourceTiming.connectStart)],\n    ['http.request.secure_connection_start', getAbsoluteTime(resourceTiming.secureConnectionStart)],\n    ['http.request.connection_end', getAbsoluteTime(resourceTiming.connectEnd)],\n    ['http.request.request_start', getAbsoluteTime(resourceTiming.requestStart)],\n    ['http.request.response_start', getAbsoluteTime(resourceTiming.responseStart)],\n    ['http.request.response_end', getAbsoluteTime(resourceTiming.responseEnd)],\n  ];\n}\n\n/**\n * A function that determines whether to attach tracing headers to a request.\n * This was extracted from `instrumentOutgoingRequests` to make it easier to test shouldAttachHeaders.\n * We only export this fuction for testing purposes.\n */\nexport function shouldAttachHeaders(url: string, tracePropagationTargets: (string | RegExp)[] | undefined): boolean {\n  return stringMatchesSomePattern(url, tracePropagationTargets || DEFAULT_TRACE_PROPAGATION_TARGETS);\n}\n\n/**\n * Create and track xhr request spans\n *\n * @returns Span if a span was created, otherwise void.\n */\n// eslint-disable-next-line complexity\nexport function xhrCallback(\n  handlerData: HandlerDataXhr,\n  shouldCreateSpan: (url: string) => boolean,\n  shouldAttachHeaders: (url: string) => boolean,\n  spans: Record<string, Span>,\n): Span | undefined {\n  const xhr = handlerData.xhr;\n  const sentryXhrData = xhr && xhr[SENTRY_XHR_DATA_KEY];\n\n  if (!hasTracingEnabled() || !xhr || xhr.__sentry_own_request__ || !sentryXhrData) {\n    return undefined;\n  }\n\n  const shouldCreateSpanResult = shouldCreateSpan(sentryXhrData.url);\n\n  // check first if the request has finished and is tracked by an existing span which should now end\n  if (handlerData.endTimestamp && shouldCreateSpanResult) {\n    const spanId = xhr.__sentry_xhr_span_id__;\n    if (!spanId) return;\n\n    const span = spans[spanId];\n    if (span && sentryXhrData.status_code !== undefined) {\n      setHttpStatus(span, sentryXhrData.status_code);\n      span.end();\n\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete spans[spanId];\n    }\n    return undefined;\n  }\n\n  const scope = getCurrentScope();\n  const isolationScope = getIsolationScope();\n\n  const fullUrl = getFullURL(sentryXhrData.url);\n  const host = fullUrl ? parseUrl(fullUrl).host : undefined;\n\n  const span = shouldCreateSpanResult\n    ? startInactiveSpan({\n        name: `${sentryXhrData.method} ${sentryXhrData.url}`,\n        onlyIfParent: true,\n        attributes: {\n          type: 'xhr',\n          'http.method': sentryXhrData.method,\n          'http.url': fullUrl,\n          url: sentryXhrData.url,\n          'server.address': host,\n          [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.http.browser',\n        },\n        op: 'http.client',\n      })\n    : undefined;\n\n  if (span) {\n    xhr.__sentry_xhr_span_id__ = span.spanContext().spanId;\n    spans[xhr.__sentry_xhr_span_id__] = span;\n  }\n\n  const client = getClient();\n\n  if (xhr.setRequestHeader && shouldAttachHeaders(sentryXhrData.url) && client) {\n    const { traceId, spanId, sampled, dsc } = {\n      ...isolationScope.getPropagationContext(),\n      ...scope.getPropagationContext(),\n    };\n\n    const sentryTraceHeader = span ? spanToTraceHeader(span) : generateSentryTraceHeader(traceId, spanId, sampled);\n\n    const sentryBaggageHeader = dynamicSamplingContextToSentryBaggageHeader(\n      dsc ||\n        (span ? getDynamicSamplingContextFromSpan(span) : getDynamicSamplingContextFromClient(traceId, client, scope)),\n    );\n\n    setHeaderOnXhr(xhr, sentryTraceHeader, sentryBaggageHeader);\n  }\n\n  return span;\n}\n\nfunction setHeaderOnXhr(\n  xhr: SentryWrappedXMLHttpRequest,\n  sentryTraceHeader: string,\n  sentryBaggageHeader: string | undefined,\n): void {\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    xhr.setRequestHeader!('sentry-trace', sentryTraceHeader);\n    if (sentryBaggageHeader) {\n      // From MDN: \"If this method is called several times with the same header, the values are merged into one single request header.\"\n      // We can therefore simply set a baggage header without checking what was there before\n      // https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/setRequestHeader\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      xhr.setRequestHeader!(BAGGAGE_HEADER_NAME, sentryBaggageHeader);\n    }\n  } catch (_) {\n    // Error: InvalidStateError: Failed to execute 'setRequestHeader' on 'XMLHttpRequest': The object's state must be OPENED.\n  }\n}\n\nfunction getFullURL(url: string): string | undefined {\n  try {\n    // By adding a base URL to new URL(), this will also work for relative urls\n    // If `url` is a full URL, the base URL is ignored anyhow\n    const parsed = new URL(url, WINDOW.location.origin);\n    return parsed.href;\n  } catch {\n    return undefined;\n  }\n}\n"], "names": ["addFetchInstrumentationHandler", "instrumentFetchRequest", "parseUrl", "addXhrInstrumentationHandler", "spanToJSON", "addPerformanceInstrumentationHandler", "browserPerformanceTimeOrigin", "stringMatchesSomePattern", "SENTRY_XHR_DATA_KEY", "hasTracingEnabled", "setHttpStatus", "getCurrentScope", "getIsolationScope", "startInactiveSpan", "SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN", "getClient", "spanToTraceHeader", "generateSentryTraceHeader", "dynamicSamplingContextToSentryBaggageHeader", "getDynamicSamplingContextFromSpan", "getDynamicSamplingContextFromClient", "BAGGAGE_HEADER_NAME", "WINDOW"], "mappings": ";;;;;;;;AAAA;AA8BA;MACa,iCAAkC,GAAE,CAAC,WAAW,EAAE,WAAW,EAAC;AAC3E;AACA;;AAkDO,MAAM,oCAAoC,GAAkC;AACnF,EAAE,UAAU,EAAE,IAAI;AAClB,EAAE,QAAQ,EAAE,IAAI;AAChB,EAAE,iBAAiB,EAAE,IAAI;AACzB;AACA,EAAE,cAAc,EAAE,iCAAiC;AACnD,EAAE,uBAAuB,EAAE,iCAAiC;AAC5D,EAAC;AACD;AACA;AACO,SAAS,0BAA0B,CAAC,QAAQ,EAAiD;AACpG,EAAE,MAAM;AACR,IAAI,UAAU;AACd,IAAI,QAAQ;AACZ;AACA,IAAI,uBAAuB;AAC3B;AACA,IAAI,cAAc;AAClB,IAAI,0BAA0B;AAC9B,IAAI,iBAAiB;AACrB,MAAM;AACN,IAAI,UAAU,EAAE,oCAAoC,CAAC,UAAU;AAC/D,IAAI,QAAQ,EAAE,oCAAoC,CAAC,QAAQ;AAC3D,IAAI,GAAG,QAAQ;AACf,GAAG,CAAA;AACH;AACA,EAAE,MAAM,gBAAiB;AACzB,IAAI,OAAO,0BAA2B,KAAI,UAAW,GAAE,0BAA2B,GAAE,CAAC,CAAC,KAAa,IAAI,CAAA;AACvG;AACA;AACA;AACA;AACA,EAAE,MAAM,8BAAA,GAAiC,CAAC,GAAG;AAC7C,IAAI,mBAAmB,CAAC,GAAG,EAAE,uBAAwB,IAAG,cAAc,CAAC,CAAA;AACvE;AACA,EAAE,MAAM,KAAK,GAAyB,EAAE,CAAA;AACxC;AACA,EAAE,IAAI,UAAU,EAAE;AAClB,IAAIA,oCAA8B,CAAC,WAAA,IAAe;AAClD,MAAM,MAAM,WAAA,GAAcC,4BAAsB,CAAC,WAAW,EAAE,gBAAgB,EAAE,8BAA8B,EAAE,KAAK,CAAC,CAAA;AACtH;AACA;AACA;AACA,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,MAAM,OAAQ,GAAE,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA;AAC7D,QAAQ,MAAM,IAAA,GAAO,OAAA,GAAUC,cAAQ,CAAC,OAAO,CAAC,CAAC,IAAK,GAAE,SAAS,CAAA;AACjE,QAAQ,WAAW,CAAC,aAAa,CAAC;AAClC,UAAU,UAAU,EAAE,OAAO;AAC7B,UAAU,gBAAgB,EAAE,IAAI;AAChC,SAAS,CAAC,CAAA;AACV,OAAM;AACN;AACA,MAAM,IAAI,iBAAkB,IAAG,WAAW,EAAE;AAC5C,QAAQ,cAAc,CAAC,WAAW,CAAC,CAAA;AACnC,OAAM;AACN,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAIC,kCAA4B,CAAC,WAAA,IAAe;AAChD,MAAM,MAAM,WAAA,GAAc,WAAW,CAAC,WAAW,EAAE,gBAAgB,EAAE,8BAA8B,EAAE,KAAK,CAAC,CAAA;AAC3G,MAAM,IAAI,iBAAkB,IAAG,WAAW,EAAE;AAC5C,QAAQ,cAAc,CAAC,WAAW,CAAC,CAAA;AACnC,OAAM;AACN,KAAK,CAAC,CAAA;AACN,GAAE;AACF,CAAA;AACA;AACA,SAAS,2BAA2B,CAAC,KAAK,EAAwD;AAClG,EAAE;AACF,IAAI,KAAK,CAAC,SAAU,KAAI,UAAW;AACnC,IAAI,eAAA,IAAmB,KAAM;AAC7B,IAAI,OAAO,CAAC,KAAA,GAAoC,eAAA,KAAoB,QAAS;AAC7E,KAAK,KAAK,CAAC,aAAc,KAAI,OAAQ,IAAG,KAAK,CAAC,aAAc,KAAI,gBAAgB,CAAA;AAChF,IAAG;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,IAAI,EAAc;AAC1C,EAAE,MAAM,EAAE,GAAI,EAAA,GAAIC,eAAU,CAAC,IAAI,CAAC,CAAC,IAAK,IAAG,EAAE,CAAA;AAC7C;AACA,EAAE,IAAI,CAAC,GAAA,IAAO,OAAO,GAAA,KAAQ,QAAQ,EAAE;AACvC,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAE,MAAM,OAAA,GAAUC,+CAAoC,CAAC,UAAU,EAAE,CAAC,EAAE,OAAA,EAAS,KAAK;AACpF,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS;AAC7B,MAAM,IAAI,2BAA2B,CAAC,KAAK,CAAE,IAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC1E,QAAQ,MAAM,QAAS,GAAE,6BAA6B,CAAC,KAAK,CAAC,CAAA;AAC7D,QAAQ,QAAQ,CAAC,OAAO,CAAC,IAAK,IAAG,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,CAAC,CAAA;AAC5D;AACA;AACA,QAAQ,UAAU,CAAC,OAAO,CAAC,CAAA;AAC3B,OAAM;AACN,KAAK,CAAC,CAAA;AACN,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,eAAe,EAA6C;AACnG,EAAE,IAAI,IAAK,GAAE,SAAS,CAAA;AACtB,EAAE,IAAI,OAAQ,GAAE,SAAS,CAAA;AACzB,EAAE,IAAI,KAAM,GAAE,EAAE,CAAA;AAChB,EAAE,KAAK,MAAM,IAAK,IAAG,eAAe,EAAE;AACtC;AACA,IAAI,IAAI,IAAK,KAAI,GAAG,EAAE;AACtB,MAAM,CAAC,IAAI,EAAE,OAAO,CAAA,GAAI,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;AAClD,MAAM,MAAK;AACX,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC9B,MAAM,IAAA,GAAO,KAAM,KAAI,MAAM,MAAA,GAAS,KAAK,CAAA;AAC3C,MAAM,OAAA,GAAU,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAC/C,MAAM,MAAK;AACX,KAAI;AACJ,IAAI,KAAA,IAAS,IAAI,CAAA;AACjB,GAAE;AACF,EAAE,IAAI,KAAM,KAAI,eAAe,EAAE;AACjC;AACA,IAAI,IAAA,GAAO,KAAK,CAAA;AAChB,GAAE;AACF,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAA;AAC1B,CAAA;AACA;AACA,SAAS,eAAe,CAAC,IAAI,GAAW,CAAC,EAAU;AACnD,EAAE,OAAO,CAAC,CAACC,kCAAA,IAAgC,WAAW,CAAC,UAAU,IAAI,IAAI,IAAI,IAAI,CAAA;AACjF,CAAA;AACA;AACA,SAAS,6BAA6B,CAAC,cAAc,EAA0D;AAC/G,EAAE,MAAM,EAAE,IAAI,EAAE,OAAQ,EAAA,GAAI,sBAAsB,CAAC,cAAc,CAAC,eAAe,CAAC,CAAA;AAClF;AACA,EAAE,MAAM,cAAc,GAAgC,EAAE,CAAA;AACxD;AACA,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC,0BAA0B,EAAE,OAAO,CAAC,EAAE,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC,CAAA;AAC7F;AACA,EAAE,IAAI,CAACA,kCAA4B,EAAE;AACrC,IAAI,OAAO,cAAc,CAAA;AACzB,GAAE;AACF,EAAE,OAAO;AACT,IAAI,GAAG,cAAc;AACrB,IAAI,CAAC,6BAA6B,EAAE,eAAe,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AAClF,IAAI,CAAC,0BAA0B,EAAE,eAAe,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAC5E,IAAI,CAAC,kCAAkC,EAAE,eAAe,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;AAC3F,IAAI,CAAC,gCAAgC,EAAE,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;AACvF,IAAI,CAAC,4BAA4B,EAAE,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAChF,IAAI,CAAC,sCAAsC,EAAE,eAAe,CAAC,cAAc,CAAC,qBAAqB,CAAC,CAAC;AACnG,IAAI,CAAC,6BAA6B,EAAE,eAAe,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAC/E,IAAI,CAAC,4BAA4B,EAAE,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;AAChF,IAAI,CAAC,6BAA6B,EAAE,eAAe,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;AAClF,IAAI,CAAC,2BAA2B,EAAE,eAAe,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AAC9E,GAAG,CAAA;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,GAAG,EAAU,uBAAuB,EAA4C;AACpH,EAAE,OAAOC,8BAAwB,CAAC,GAAG,EAAE,uBAAwB,IAAG,iCAAiC,CAAC,CAAA;AACpG,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW;AAC3B,EAAE,WAAW;AACb,EAAE,gBAAgB;AAClB,EAAE,mBAAmB;AACrB,EAAE,KAAK;AACP,EAAoB;AACpB,EAAE,MAAM,GAAA,GAAM,WAAW,CAAC,GAAG,CAAA;AAC7B,EAAE,MAAM,gBAAgB,GAAA,IAAO,GAAG,CAACC,yBAAmB,CAAC,CAAA;AACvD;AACA,EAAE,IAAI,CAACC,sBAAiB,MAAM,CAAC,GAAI,IAAG,GAAG,CAAC,sBAAA,IAA0B,CAAC,aAAa,EAAE;AACpF,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,yBAAyB,gBAAgB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;AACpE;AACA;AACA,EAAE,IAAI,WAAW,CAAC,YAAa,IAAG,sBAAsB,EAAE;AAC1D,IAAI,MAAM,MAAA,GAAS,GAAG,CAAC,sBAAsB,CAAA;AAC7C,IAAI,IAAI,CAAC,MAAM,EAAE,OAAM;AACvB;AACA,IAAI,MAAM,IAAK,GAAE,KAAK,CAAC,MAAM,CAAC,CAAA;AAC9B,IAAI,IAAI,IAAK,IAAG,aAAa,CAAC,WAAA,KAAgB,SAAS,EAAE;AACzD,MAAMC,kBAAa,CAAC,IAAI,EAAE,aAAa,CAAC,WAAW,CAAC,CAAA;AACpD,MAAM,IAAI,CAAC,GAAG,EAAE,CAAA;AAChB;AACA;AACA,MAAM,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA;AAC1B,KAAI;AACJ,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,KAAA,GAAQC,oBAAe,EAAE,CAAA;AACjC,EAAE,MAAM,cAAA,GAAiBC,sBAAiB,EAAE,CAAA;AAC5C;AACA,EAAE,MAAM,UAAU,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;AAC/C,EAAE,MAAM,IAAA,GAAO,OAAA,GAAUV,cAAQ,CAAC,OAAO,CAAC,CAAC,IAAK,GAAE,SAAS,CAAA;AAC3D;AACA,EAAE,MAAM,OAAO,sBAAA;AACf,MAAMW,sBAAiB,CAAC;AACxB,QAAQ,IAAI,EAAE,CAAC,EAAA,aAAA,CAAA,MAAA,CAAA,CAAA,EAAA,aAAA,CAAA,GAAA,CAAA,CAAA;AACA,QAAA,YAAA,EAAA,IAAA;AACA,QAAA,UAAA,EAAA;AACA,UAAA,IAAA,EAAA,KAAA;AACA,UAAA,aAAA,EAAA,aAAA,CAAA,MAAA;AACA,UAAA,UAAA,EAAA,OAAA;AACA,UAAA,GAAA,EAAA,aAAA,CAAA,GAAA;AACA,UAAA,gBAAA,EAAA,IAAA;AACA,UAAA,CAAAC,qCAAA,GAAA,mBAAA;AACA,SAAA;AACA,QAAA,EAAA,EAAA,aAAA;AACA,OAAA,CAAA;AACA,MAAA,SAAA,CAAA;AACA;AACA,EAAA,IAAA,IAAA,EAAA;AACA,IAAA,GAAA,CAAA,sBAAA,GAAA,IAAA,CAAA,WAAA,EAAA,CAAA,MAAA,CAAA;AACA,IAAA,KAAA,CAAA,GAAA,CAAA,sBAAA,CAAA,GAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,MAAA,GAAAC,cAAA,EAAA,CAAA;AACA;AACA,EAAA,IAAA,GAAA,CAAA,gBAAA,IAAA,mBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,IAAA,MAAA,EAAA;AACA,IAAA,MAAA,EAAA,OAAA,EAAA,MAAA,EAAA,OAAA,EAAA,GAAA,EAAA,GAAA;AACA,MAAA,GAAA,cAAA,CAAA,qBAAA,EAAA;AACA,MAAA,GAAA,KAAA,CAAA,qBAAA,EAAA;AACA,KAAA,CAAA;AACA;AACA,IAAA,MAAA,iBAAA,GAAA,IAAA,GAAAC,sBAAA,CAAA,IAAA,CAAA,GAAAC,+BAAA,CAAA,OAAA,EAAA,MAAA,EAAA,OAAA,CAAA,CAAA;AACA;AACA,IAAA,MAAA,mBAAA,GAAAC,iDAAA;AACA,MAAA,GAAA;AACA,SAAA,IAAA,GAAAC,sCAAA,CAAA,IAAA,CAAA,GAAAC,wCAAA,CAAA,OAAA,EAAA,MAAA,EAAA,KAAA,CAAA,CAAA;AACA,KAAA,CAAA;AACA;AACA,IAAA,cAAA,CAAA,GAAA,EAAA,iBAAA,EAAA,mBAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,IAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,cAAA;AACA,EAAA,GAAA;AACA,EAAA,iBAAA;AACA,EAAA,mBAAA;AACA,EAAA;AACA,EAAA,IAAA;AACA;AACA,IAAA,GAAA,CAAA,gBAAA,CAAA,cAAA,EAAA,iBAAA,CAAA,CAAA;AACA,IAAA,IAAA,mBAAA,EAAA;AACA;AACA;AACA;AACA;AACA,MAAA,GAAA,CAAA,gBAAA,CAAAC,yBAAA,EAAA,mBAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA;AACA,GAAA;AACA,CAAA;AACA;AACA,SAAA,UAAA,CAAA,GAAA,EAAA;AACA,EAAA,IAAA;AACA;AACA;AACA,IAAA,MAAA,MAAA,GAAA,IAAA,GAAA,CAAA,GAAA,EAAAC,YAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;AACA,IAAA,OAAA,MAAA,CAAA,IAAA,CAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,OAAA,SAAA,CAAA;AACA,GAAA;AACA;;;;;;;;;"}