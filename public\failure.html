<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed - Cashfree</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }

        .failure-icon {
            font-size: 80px;
            color: #f44336;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .header p {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .error-details {
            background: #ffebee;
            border: 1px solid #ffcdd2;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: left;
        }

        .error-details h3 {
            color: #d32f2f;
            margin-bottom: 15px;
            text-align: center;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #ffcdd2;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #555;
        }

        .detail-value {
            color: #333;
        }

        .reasons {
            background: #fff3e0;
            border: 1px solid #ffcc02;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: left;
        }

        .reasons h3 {
            color: #f57c00;
            margin-bottom: 15px;
            text-align: center;
        }

        .reasons ul {
            list-style-type: none;
            padding: 0;
        }

        .reasons li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .reasons li:before {
            content: "•";
            color: #f57c00;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-danger {
            background: #f44336;
            color: white;
        }

        .powered-by {
            margin-top: 30px;
            color: #999;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="failure-icon">❌</div>
        
        <div class="header">
            <h1>Payment Failed</h1>
            <p>We're sorry, but your payment could not be processed at this time.</p>
        </div>

        <div class="error-details">
            <h3>Transaction Details</h3>
            <div class="detail-row">
                <span class="detail-label">Order ID:</span>
                <span class="detail-value" id="order-id">N/A</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="detail-value" style="color: #f44336; font-weight: bold;">FAILED</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Error Message:</span>
                <span class="detail-value" id="error-message">Payment was declined</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date & Time:</span>
                <span class="detail-value" id="transaction-time"></span>
            </div>
        </div>

        <div class="reasons">
            <h3>Common Reasons for Payment Failure</h3>
            <ul>
                <li>Insufficient funds in your account</li>
                <li>Incorrect card details or expired card</li>
                <li>Bank declined the transaction</li>
                <li>Network connectivity issues</li>
                <li>Daily transaction limit exceeded</li>
                <li>Card not enabled for online transactions</li>
            </ul>
        </div>

        <div class="actions">
            <a href="/" class="btn btn-primary">Try Again</a>
            <a href="mailto:<EMAIL>" class="btn btn-secondary">Contact Support</a>
        </div>

        <div class="powered-by">
            Powered by <strong>Cashfree Payments</strong>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const orderId = urlParams.get('order_id');
        const errorMsg = urlParams.get('error_msg') || urlParams.get('txMsg');

        // Update order details
        if (orderId) {
            document.getElementById('order-id').textContent = orderId;
        }

        if (errorMsg) {
            document.getElementById('error-message').textContent = decodeURIComponent(errorMsg);
        }

        // Set current time
        document.getElementById('transaction-time').textContent = new Date().toLocaleString();

        // Auto-redirect after 60 seconds (optional)
        setTimeout(() => {
            const autoRedirect = confirm('Would you like to try the payment again?');
            if (autoRedirect) {
                window.location.href = '/';
            }
        }, 60000);
    </script>
</body>
</html>
