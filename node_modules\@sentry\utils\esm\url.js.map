{"version": 3, "file": "url.js", "sources": ["../../src/url.ts"], "sourcesContent": ["type PartialURL = {\n  host?: string;\n  path?: string;\n  protocol?: string;\n  relative?: string;\n  search?: string;\n  hash?: string;\n};\n\n/**\n * Parses string form of URL into an object\n * // borrowed from https://tools.ietf.org/html/rfc3986#appendix-B\n * // intentionally using regex and not <a/> href parsing trick because React Native and other\n * // environments where DOM might not be available\n * @returns parsed URL object\n */\nexport function parseUrl(url: string): PartialURL {\n  if (!url) {\n    return {};\n  }\n\n  const match = url.match(/^(([^:/?#]+):)?(\\/\\/([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?$/);\n\n  if (!match) {\n    return {};\n  }\n\n  // coerce to undefined values to empty string so we don't get 'undefined'\n  const query = match[6] || '';\n  const fragment = match[8] || '';\n  return {\n    host: match[4],\n    path: match[5],\n    protocol: match[2],\n    search: query,\n    hash: fragment,\n    relative: match[5] + query + fragment, // everything minus origin\n  };\n}\n\n/**\n * Strip the query string and fragment off of a given URL or path (if present)\n *\n * @param urlPath Full URL or path, including possible query string and/or fragment\n * @returns URL or path without query string or fragment\n */\nexport function stripUrlQueryAndFragment(urlPath: string): string {\n  // eslint-disable-next-line no-useless-escape\n  return urlPath.split(/[\\?#]/, 1)[0];\n}\n\n/**\n * Returns number of URL segments of a passed string URL.\n */\nexport function getNumberOfUrlSegments(url: string): number {\n  // split at '/' or at '\\/' to split regex urls correctly\n  return url.split(/\\\\?\\//).filter(s => s.length > 0 && s !== ',').length;\n}\n\n/**\n * Takes a URL object and returns a sanitized string which is safe to use as span description\n * see: https://develop.sentry.dev/sdk/data-handling/#structuring-data\n */\nexport function getSanitizedUrlString(url: PartialURL): string {\n  const { protocol, host, path } = url;\n\n  const filteredHost =\n    (host &&\n      host\n        // Always filter out authority\n        .replace(/^.*@/, '[filtered]:[filtered]@')\n        // Don't show standard :80 (http) and :443 (https) ports to reduce the noise\n        // TODO: Use new URL global if it exists\n        .replace(/(:80)$/, '')\n        .replace(/(:443)$/, '')) ||\n    '';\n\n  return `${protocol ? `${protocol}://` : ''}${filteredHost}${path}`;\n}\n"], "names": [], "mappings": "AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,GAAG,EAAsB;AAClD,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,OAAO,EAAE,CAAA;AACb,GAAE;AACF;AACA,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAA;AACzF;AACA,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,EAAE,CAAA;AACb,GAAE;AACF;AACA;AACA,EAAE,MAAM,QAAQ,KAAK,CAAC,CAAC,CAAA,IAAK,EAAE,CAAA;AAC9B,EAAE,MAAM,WAAW,KAAK,CAAC,CAAC,CAAA,IAAK,EAAE,CAAA;AACjC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AAClB,IAAI,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AAClB,IAAI,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;AACtB,IAAI,MAAM,EAAE,KAAK;AACjB,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAE,GAAE,KAAM,GAAE,QAAQ;AACzC,GAAG,CAAA;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,wBAAwB,CAAC,OAAO,EAAkB;AAClE;AACA,EAAE,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AACrC,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,GAAG,EAAkB;AAC5D;AACA,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,MAAO,GAAE,CAAE,IAAG,MAAM,GAAG,CAAC,CAAC,MAAM,CAAA;AACzE,CAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,qBAAqB,CAAC,GAAG,EAAsB;AAC/D,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAA,EAAO,GAAE,GAAG,CAAA;AACtC;AACA,EAAE,MAAM,YAAa;AACrB,IAAI,CAAC,IAAK;AACV,MAAM,IAAA;AACN;AACA,SAAS,OAAO,CAAC,MAAM,EAAE,wBAAwB,CAAA;AACjD;AACA;AACA,SAAS,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAA;AAC7B,SAAS,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;AAC/B,IAAI,EAAE,CAAA;AACN;AACA,EAAE,OAAO,CAAC,EAAA,QAAA,GAAA,CAAA,EAAA,QAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,EAAA,YAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AACA;;;;"}