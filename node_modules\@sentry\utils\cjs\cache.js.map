{"version": 3, "file": "cache.js", "sources": ["../../src/cache.ts"], "sourcesContent": ["/**\n * Creates a cache that evicts keys in fifo order\n * @param size {Number}\n */\nexport function makeFifoCache<Key extends string, Value>(\n  size: number,\n): {\n  get: (key: Key) => Value | undefined;\n  add: (key: Key, value: Value) => void;\n  delete: (key: Key) => boolean;\n  clear: () => void;\n  size: () => number;\n} {\n  // Maintain a fifo queue of keys, we cannot rely on Object.keys as the browser may not support it.\n  let evictionOrder: Key[] = [];\n  let cache: Record<string, Value> = {};\n\n  return {\n    add(key: Key, value: Value) {\n      while (evictionOrder.length >= size) {\n        // shift is O(n) but this is small size and only happens if we are\n        // exceeding the cache size so it should be fine.\n        const evictCandidate = evictionOrder.shift();\n\n        if (evictCandidate !== undefined) {\n          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n          delete cache[evictCandidate];\n        }\n      }\n\n      // in case we have a collision, delete the old key.\n      if (cache[key]) {\n        this.delete(key);\n      }\n\n      evictionOrder.push(key);\n      cache[key] = value;\n    },\n    clear() {\n      cache = {};\n      evictionOrder = [];\n    },\n    get(key: Key): Value | undefined {\n      return cache[key];\n    },\n    size() {\n      return evictionOrder.length;\n    },\n    // Delete cache key and return true if it existed, false otherwise.\n    delete(key: Key): boolean {\n      if (!cache[key]) {\n        return false;\n      }\n\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete cache[key];\n\n      for (let i = 0; i < evictionOrder.length; i++) {\n        if (evictionOrder[i] === key) {\n          evictionOrder.splice(i, 1);\n          break;\n        }\n      }\n\n      return true;\n    },\n  };\n}\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;AACO,SAAS,aAAa;AAC7B,EAAE,IAAI;AACN;;AAMA,CAAE;AACF;AACA,EAAE,IAAI,aAAa,GAAU,EAAE,CAAA;AAC/B,EAAE,IAAI,KAAK,GAA0B,EAAE,CAAA;AACvC;AACA,EAAE,OAAO;AACT,IAAI,GAAG,CAAC,GAAG,EAAO,KAAK,EAAS;AAChC,MAAM,OAAO,aAAa,CAAC,MAAO,IAAG,IAAI,EAAE;AAC3C;AACA;AACA,QAAQ,MAAM,cAAe,GAAE,aAAa,CAAC,KAAK,EAAE,CAAA;AACpD;AACA,QAAQ,IAAI,cAAe,KAAI,SAAS,EAAE;AAC1C;AACA,UAAU,OAAO,KAAK,CAAC,cAAc,CAAC,CAAA;AACtC,SAAQ;AACR,OAAM;AACN;AACA;AACA,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE;AACtB,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AACxB,OAAM;AACN;AACA,MAAM,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AAC7B,MAAM,KAAK,CAAC,GAAG,CAAA,GAAI,KAAK,CAAA;AACxB,KAAK;AACL,IAAI,KAAK,GAAG;AACZ,MAAM,KAAA,GAAQ,EAAE,CAAA;AAChB,MAAM,aAAA,GAAgB,EAAE,CAAA;AACxB,KAAK;AACL,IAAI,GAAG,CAAC,GAAG,EAA0B;AACrC,MAAM,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA;AACvB,KAAK;AACL,IAAI,IAAI,GAAG;AACX,MAAM,OAAO,aAAa,CAAC,MAAM,CAAA;AACjC,KAAK;AACL;AACA,IAAI,MAAM,CAAC,GAAG,EAAgB;AAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACvB,QAAQ,OAAO,KAAK,CAAA;AACpB,OAAM;AACN;AACA;AACA,MAAM,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA;AACvB;AACA,MAAM,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAE,GAAE,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD,QAAQ,IAAI,aAAa,CAAC,CAAC,CAAE,KAAI,GAAG,EAAE;AACtC,UAAU,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACpC,UAAU,MAAK;AACf,SAAQ;AACR,OAAM;AACN;AACA,MAAM,OAAO,IAAI,CAAA;AACjB,KAAK;AACL,GAAG,CAAA;AACH;;;;"}