const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { Cashfree, initializeCashfree, validateConfig, getConfig } = require('../config/cashfree');

const router = express.Router();

// Initialize Cashfree on module load
if (!validateConfig()) {
    console.error('❌ Cashfree configuration validation failed');
    process.exit(1);
}

if (!initializeCashfree()) {
    console.error('❌ Cashfree initialization failed');
    process.exit(1);
}

// Create payment order
router.post('/create-order', async (req, res) => {
    try {
        const { amount, customerName, customerEmail, customerPhone } = req.body;

        // Validate required fields
        if (!amount || !customerName || !customerEmail || !customerPhone) {
            return res.status(400).json({
                error: 'Missing required fields',
                required: ['amount', 'customerName', 'customerEmail', 'customerPhone']
            });
        }

        // Validate amount
        if (isNaN(amount) || parseFloat(amount) <= 0) {
            return res.status(400).json({
                error: 'Invalid amount',
                message: 'Amount must be a positive number'
            });
        }

        const config = getConfig();
        const orderId = `ORDER_${Date.now()}_${uuidv4().substring(0, 8)}`;

        // Create order request
        const orderRequest = {
            order_id: orderId,
            order_amount: parseFloat(amount),
            order_currency: 'INR',
            customer_details: {
                customer_id: `CUST_${Date.now()}`,
                customer_name: customerName,
                customer_email: customerEmail,
                customer_phone: customerPhone
            },
            order_meta: {
                return_url: config.returnUrl,
                notify_url: config.notifyUrl
            }
        };

        console.log('📝 Creating order:', orderId);

        // Create order with Cashfree
        const response = await Cashfree.PGCreateOrder("2023-08-01", orderRequest);
        
        if (response.data && response.data.payment_session_id) {
            console.log('✅ Order created successfully:', orderId);
            
            res.json({
                success: true,
                orderId: orderId,
                paymentSessionId: response.data.payment_session_id,
                orderAmount: parseFloat(amount),
                customerDetails: {
                    name: customerName,
                    email: customerEmail,
                    phone: customerPhone
                }
            });
        } else {
            throw new Error('Failed to create payment session');
        }

    } catch (error) {
        console.error('❌ Error creating order:', error);
        res.status(500).json({
            error: 'Failed to create payment order',
            message: error.message
        });
    }
});

// Payment return handler
router.get('/return', (req, res) => {
    const { order_id, order_token } = req.query;
    
    console.log('🔄 Payment return:', { order_id, order_token });
    
    // Redirect to success page with order details
    res.redirect(`/success.html?order_id=${order_id}&order_token=${order_token}`);
});

// Payment webhook handler
router.post('/webhook', async (req, res) => {
    try {
        const { orderId, orderAmount, referenceId, txStatus, paymentMode, txMsg, txTime } = req.body;
        
        console.log('📨 Webhook received:', {
            orderId,
            orderAmount,
            referenceId,
            txStatus,
            paymentMode,
            txTime
        });

        // Verify payment status with Cashfree
        if (orderId) {
            try {
                const orderStatus = await Cashfree.PGOrderFetchPayments("2023-08-01", orderId);
                console.log('📊 Order status from Cashfree:', orderStatus.data);
            } catch (error) {
                console.error('❌ Error fetching order status:', error.message);
            }
        }

        // Process the webhook based on payment status
        if (txStatus === 'SUCCESS') {
            console.log('✅ Payment successful for order:', orderId);
            // Add your success logic here (update database, send confirmation email, etc.)
        } else if (txStatus === 'FAILED') {
            console.log('❌ Payment failed for order:', orderId);
            // Add your failure logic here
        } else {
            console.log('⏳ Payment pending for order:', orderId);
            // Add your pending logic here
        }

        // Always respond with 200 to acknowledge webhook receipt
        res.status(200).json({ status: 'OK' });

    } catch (error) {
        console.error('❌ Webhook processing error:', error);
        res.status(500).json({ error: 'Webhook processing failed' });
    }
});

// Get order status
router.get('/status/:orderId', async (req, res) => {
    try {
        const { orderId } = req.params;
        
        console.log('📊 Checking status for order:', orderId);
        
        const response = await Cashfree.PGOrderFetchPayments("2023-08-01", orderId);
        
        res.json({
            success: true,
            orderId: orderId,
            payments: response.data
        });

    } catch (error) {
        console.error('❌ Error fetching order status:', error);
        res.status(500).json({
            error: 'Failed to fetch order status',
            message: error.message
        });
    }
});

module.exports = router;
