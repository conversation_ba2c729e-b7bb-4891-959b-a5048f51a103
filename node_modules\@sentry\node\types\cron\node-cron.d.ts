export interface NodeCronOptions {
    name: string;
    timezone?: string;
}
export interface NodeCron {
    schedule: (cronExpression: string, callback: () => void, options: NodeCronOptions) => unknown;
}
/**
 * Wraps the `node-cron` library with check-in monitoring.
 *
 * ```ts
 * import * as Sen<PERSON> from "@sentry/node";
 * import cron from "node-cron";
 *
 * const cronWithCheckIn = Sentry.cron.instrumentNodeCron(cron);
 *
 * cronWithCheckIn.schedule(
 *   "* * * * *",
 *   () => {
 *     console.log("running a task every minute");
 *   },
 *   { name: "my-cron-job" },
 * );
 * ```
 */
export declare function instrumentNodeCron<T>(lib: Partial<NodeCron> & T): T;
//# sourceMappingURL=node-cron.d.ts.map