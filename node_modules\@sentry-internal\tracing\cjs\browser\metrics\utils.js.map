{"version": 3, "file": "utils.js", "sources": ["../../../../src/browser/metrics/utils.ts"], "sourcesContent": ["import type { Transaction } from '@sentry/core';\nimport type { Span, SpanContext } from '@sentry/types';\n\n/**\n * Checks if a given value is a valid measurement value.\n */\nexport function isMeasurementValue(value: unknown): value is number {\n  return typeof value === 'number' && isFinite(value);\n}\n\n/**\n * Helper function to start child on transactions. This function will make sure that the transaction will\n * use the start timestamp of the created child span if it is earlier than the transactions actual\n * start timestamp.\n *\n * Note: this will not be possible anymore in v8,\n * unless we do some special handling for browser here...\n */\nexport function _startChild(transaction: Transaction, { startTimestamp, ...ctx }: SpanContext): Span {\n  // eslint-disable-next-line deprecation/deprecation\n  if (startTimestamp && transaction.startTimestamp > startTimestamp) {\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.startTimestamp = startTimestamp;\n  }\n\n  // eslint-disable-next-line deprecation/deprecation\n  return transaction.startChild({\n    startTimestamp,\n    ...ctx,\n  });\n}\n"], "names": [], "mappings": ";;AAGA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,KAAK,EAA4B;AACpE,EAAE,OAAO,OAAO,KAAM,KAAI,YAAY,QAAQ,CAAC,KAAK,CAAC,CAAA;AACrD,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,WAAW,EAAe,EAAE,cAAc,EAAE,GAAG,GAAA,EAAK,EAAqB;AACrG;AACA,EAAE,IAAI,cAAe,IAAG,WAAW,CAAC,cAAA,GAAiB,cAAc,EAAE;AACrE;AACA,IAAI,WAAW,CAAC,cAAe,GAAE,cAAc,CAAA;AAC/C,GAAE;AACF;AACA;AACA,EAAE,OAAO,WAAW,CAAC,UAAU,CAAC;AAChC,IAAI,cAAc;AAClB,IAAI,GAAG,GAAG;AACV,GAAG,CAAC,CAAA;AACJ;;;;;"}