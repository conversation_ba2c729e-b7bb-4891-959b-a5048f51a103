import type { HandlerDataXhr } from '@sentry/types';
export declare const SENTRY_XHR_DATA_KEY = "__sentry_xhr_v3__";
/**
 * Add an instrumentation handler for when an XHR request happens.
 * The handler function is called once when the request starts and once when it ends,
 * which can be identified by checking if it has an `endTimestamp`.
 *
 * Use at your own risk, this might break without changelog notice, only used internally.
 * @hidden
 */
export declare function addXhrInstrumentationHandler(handler: (data: HandlerDataXhr) => void): void;
/** Exported only for tests. */
export declare function instrumentXHR(): void;
//# sourceMappingURL=xhr.d.ts.map