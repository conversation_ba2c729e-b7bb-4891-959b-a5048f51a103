{"version": 3, "file": "errorhandling.js", "sources": ["../../../../src/integrations/utils/errorhandling.ts"], "sourcesContent": ["import { getClient } from '@sentry/core';\nimport { consoleSandbox, logger } from '@sentry/utils';\n\nimport type { NodeClient } from '../../client';\nimport { DEBUG_BUILD } from '../../debug-build';\n\nconst DEFAULT_SHUTDOWN_TIMEOUT = 2000;\n\n/**\n * @hidden\n */\nexport function logAndExitProcess(error: Error): void {\n  consoleSandbox(() => {\n    // eslint-disable-next-line no-console\n    console.error(error);\n  });\n\n  const client = getClient<NodeClient>();\n\n  if (client === undefined) {\n    DEBUG_BUILD && logger.warn('No NodeClient was defined, we are exiting the process now.');\n    global.process.exit(1);\n  }\n\n  const options = client.getOptions();\n  const timeout =\n    (options && options.shutdownTimeout && options.shutdownTimeout > 0 && options.shutdownTimeout) ||\n    DEFAULT_SHUTDOWN_TIMEOUT;\n  client.close(timeout).then(\n    (result: boolean) => {\n      if (!result) {\n        DEBUG_BUILD && logger.warn('We reached the timeout for emptying the request buffer, still exiting now!');\n      }\n      global.process.exit(1);\n    },\n    error => {\n      DEBUG_BUILD && logger.error(error);\n    },\n  );\n}\n"], "names": ["consoleSandbox", "getClient", "DEBUG_BUILD", "logger"], "mappings": ";;;;;;AAMA,MAAM,wBAAA,GAA2B,IAAI,CAAA;AACrC;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,KAAK,EAAe;AACtD,EAAEA,oBAAc,CAAC,MAAM;AACvB;AACA,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;AACxB,GAAG,CAAC,CAAA;AACJ;AACA,EAAE,MAAM,MAAA,GAASC,cAAS,EAAc,CAAA;AACxC;AACA,EAAE,IAAI,MAAO,KAAI,SAAS,EAAE;AAC5B,IAAIC,0BAAeC,YAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAA;AAC5F,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AAC1B,GAAE;AACF;AACA,EAAE,MAAM,OAAQ,GAAE,MAAM,CAAC,UAAU,EAAE,CAAA;AACrC,EAAE,MAAM,OAAQ;AAChB,IAAI,CAAC,OAAQ,IAAG,OAAO,CAAC,mBAAmB,OAAO,CAAC,eAAA,GAAkB,CAAE,IAAG,OAAO,CAAC,eAAe;AACjG,IAAI,wBAAwB,CAAA;AAC5B,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI;AAC5B,IAAI,CAAC,MAAM,KAAc;AACzB,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQD,0BAAeC,YAAM,CAAC,IAAI,CAAC,4EAA4E,CAAC,CAAA;AAChH,OAAM;AACN,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;AAC5B,KAAK;AACL,IAAI,SAAS;AACb,MAAMD,0BAAeC,YAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;AACxC,KAAK;AACL,GAAG,CAAA;AACH;;;;"}