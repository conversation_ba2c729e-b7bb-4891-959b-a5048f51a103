const { Cashfree } = require('cashfree-pg');

// Initialize Cashfree with environment variables
const initializeCashfree = () => {
    try {
        // Set environment
        const environment = process.env.CASHFREE_ENVIRONMENT === 'PRODUCTION' 
            ? Cashfree.Environment.PRODUCTION 
            : Cashfree.Environment.SANDBOX;

        // Initialize Cashfree
        Cashfree.XClientId = process.env.CASHFREE_APP_ID;
        Cashfree.XClientSecret = process.env.CASHFREE_SECRET_KEY;
        Cashfree.XEnvironment = environment;

        console.log('✅ Cashfree initialized successfully');
        console.log(`📍 Environment: ${process.env.CASHFREE_ENVIRONMENT || 'SANDBOX'}`);
        
        return true;
    } catch (error) {
        console.error('❌ Failed to initialize Cashfree:', error.message);
        return false;
    }
};

// Validate Cashfree configuration
const validateConfig = () => {
    const requiredEnvVars = [
        'CASHFREE_APP_ID',
        'CASHFREE_SECRET_KEY'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:', missingVars);
        return false;
    }

    return true;
};

// Get Cashfree configuration
const getConfig = () => {
    return {
        appId: process.env.CASHFREE_APP_ID,
        secretKey: process.env.CASHFREE_SECRET_KEY,
        environment: process.env.CASHFREE_ENVIRONMENT || 'SANDBOX',
        baseUrl: process.env.BASE_URL || 'http://localhost:3000',
        returnUrl: process.env.RETURN_URL || 'http://localhost:3000/payment/return',
        notifyUrl: process.env.NOTIFY_URL || 'http://localhost:3000/payment/webhook'
    };
};

module.exports = {
    Cashfree,
    initializeCashfree,
    validateConfig,
    getConfig
};
