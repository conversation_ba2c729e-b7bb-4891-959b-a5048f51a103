{"version": 3, "file": "node-cron.js", "sources": ["../../../src/cron/node-cron.ts"], "sourcesContent": ["import { with<PERSON>oni<PERSON> } from '@sentry/core';\nimport { replaceCronNames } from './common';\n\nexport interface NodeCronOptions {\n  name: string;\n  timezone?: string;\n}\n\nexport interface NodeCron {\n  schedule: (cronExpression: string, callback: () => void, options: NodeCronOptions) => unknown;\n}\n\n/**\n * Wraps the `node-cron` library with check-in monitoring.\n *\n * ```ts\n * import * as Sentry from \"@sentry/node\";\n * import cron from \"node-cron\";\n *\n * const cronWithCheckIn = Sentry.cron.instrumentNodeCron(cron);\n *\n * cronWithCheckIn.schedule(\n *   \"* * * * *\",\n *   () => {\n *     console.log(\"running a task every minute\");\n *   },\n *   { name: \"my-cron-job\" },\n * );\n * ```\n */\nexport function instrumentNodeCron<T>(lib: Partial<NodeCron> & T): T {\n  return new Proxy(lib, {\n    get(target, prop: keyof NodeCron) {\n      if (prop === 'schedule' && target.schedule) {\n        // When 'get' is called for schedule, return a proxied version of the schedule function\n        return new Proxy(target.schedule, {\n          apply(target, thisArg, argArray: Parameters<NodeCron['schedule']>) {\n            const [expression, , options] = argArray;\n\n            if (!options?.name) {\n              throw new Error('Missing \"name\" for scheduled job. A name is required for Sentry check-in monitoring.');\n            }\n\n            return withMonitor(\n              options.name,\n              () => {\n                return target.apply(thisArg, argArray);\n              },\n              {\n                schedule: { type: 'crontab', value: replaceCronNames(expression) },\n                timezone: options?.timezone,\n              },\n            );\n          },\n        });\n      } else {\n        return target[prop];\n      }\n    },\n  });\n}\n"], "names": ["withMonitor", "replaceCronNames"], "mappings": ";;;;;;;;;AAYA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,IAAA,CAAA,EAAA,EAAA,EAAA,EAAA,CAAA,CAAA;CACA,IAAA,CAAA,EAAA,CAAA,EAAA;CACA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,IAAA,CAAA;CACA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;CACA,EAAA,CAAA;CACA,EAAA,CAAA,CAAA;CACA,CAAA;AACO,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAI,CAAA,CAAA,CAAG,EAA4B;EACnE,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA,CAAA,CAAC,GAAG,EAAE;IACpB,CAAG,CAAA,CAAA,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,IAAI,EAAkB;MAChC,CAAA,EAAA,CAAI,KAAS,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAc,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,QAAQ,EAAE;QAClD,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,OAAO,CAAI,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,QAAQ,EAAE;UAChC,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,QAAQ,EAAoC;YACjE,CAAA,CAAA,CAAA,CAAA,EAAM,CAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAI,OAAO,EAAA,EAAI,QAAQ;;YAExC,IAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,OAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAE,MAAI,EAAE;cAClB,MAAM,CAAI,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsF,CAAC;YACzG;;YAEA,OAAOA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;cAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAI,CAAA,CAAA,CAAA;cACZ,CAAM,EAAA,CAAA,EAAA;gBACJ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,MAAM,CAAC,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;cACvC,CAAA;cACD;gBACE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,EAAE,CAAA,CAAA,CAAA,CAAI,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAA,CAAA,CAAA,CAAA,CAAK,EAAEC,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAgB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,EAAG,CAAA;gBAClE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA;cAC5B,CAAA;YACf,CAAa;UACF,CAAA;QACX,CAAS,CAAC;MACV,EAAa,CAAA,CAAA,CAAA,EAAA;QACL,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC;MACrB;IACD,CAAA;EACL,CAAG,CAAC;AACJ;;"}