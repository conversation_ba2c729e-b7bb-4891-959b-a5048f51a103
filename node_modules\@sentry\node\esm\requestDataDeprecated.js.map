{"version": 3, "file": "requestDataDeprecated.js", "sources": ["../../src/requestDataDeprecated.ts"], "sourcesContent": ["/**\n * Deprecated functions which are slated for removal in v8. When the time comes, this entire file can be deleted.\n *\n * See https://github.com/getsentry/sentry-javascript/pull/5257.\n */\n\n/* eslint-disable deprecation/deprecation */\n/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { Event, ExtractedNodeRequestData, PolymorphicRequest } from '@sentry/types';\nimport type { AddRequestDataToEventOptions } from '@sentry/utils';\nimport { addRequestDataToEvent, extractRequestData as _extractRequestData } from '@sentry/utils';\n\n/**\n * @deprecated `Handlers.ExpressRequest` is deprecated and will be removed in v8. Use `PolymorphicRequest` instead.\n */\nexport type ExpressRequest = PolymorphicRequest;\n\n/**\n * Normalizes data from the request object, accounting for framework differences.\n *\n * @deprecated `Handlers.extractRequestData` is deprecated and will be removed in v8. Use `extractRequestData` instead.\n *\n * @param req The request object from which to extract data\n * @param keys An optional array of keys to include in the normalized data.\n * @returns An object containing normalized request data\n */\nexport function extractRequestData(req: { [key: string]: any }, keys?: string[]): ExtractedNodeRequestData {\n  return _extractRequestData(req, { include: keys });\n}\n\n/**\n * Options deciding what parts of the request to use when enhancing an event\n *\n * @deprecated `Handlers.ParseRequestOptions` is deprecated and will be removed in v8. Use\n * `AddRequestDataToEventOptions` in `@sentry/utils` instead.\n */\nexport type ParseRequestOptions = AddRequestDataToEventOptions['include'] & {\n  serverName?: boolean;\n  version?: boolean;\n};\n\n/**\n * Enriches passed event with request data.\n *\n * @deprecated `Handlers.parseRequest` is deprecated and will be removed in v8. Use `addRequestDataToEvent` instead.\n *\n * @param event Will be mutated and enriched with req data\n * @param req Request object\n * @param options object containing flags to enable functionality\n * @hidden\n */\nexport function parseRequest(event: Event, req: ExpressRequest, options: ParseRequestOptions = {}): Event {\n  return addRequestDataToEvent(event, req, { include: options });\n}\n"], "names": ["_extractRequestData"], "mappings": ";;AAYA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,kBAAkB,CAAC,GAAG,EAA0B,IAAI,EAAuC;AAC3G,EAAE,OAAOA,oBAAmB,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,IAAK,EAAC,CAAC,CAAA;AACpD,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,KAAK,EAAS,GAAG,EAAkB,OAAO,GAAwB,EAAE,EAAS;AAC1G,EAAE,OAAO,qBAAqB,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,OAAO,EAAE,OAAQ,EAAC,CAAC,CAAA;AAChE;;;;"}