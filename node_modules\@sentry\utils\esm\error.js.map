{"version": 3, "file": "error.js", "sources": ["../../src/error.ts"], "sourcesContent": ["import type { ConsoleLevel } from '@sentry/types';\n\n/** An error emitted by Sentry SDKs and related utilities. */\nexport class SentryError extends Error {\n  /** Display name of this error instance. */\n  public name: string;\n\n  public logLevel: ConsoleLevel;\n\n  public constructor(public message: string, logLevel: ConsoleLevel = 'warn') {\n    super(message);\n\n    this.name = new.target.prototype.constructor.name;\n    // This sets the prototype to be `Error`, not `SentryError`. It's unclear why we do this, but commenting this line\n    // out causes various (seemingly totally unrelated) playwright tests consistently time out. FYI, this makes\n    // instances of `SentryError` fail `obj instanceof SentryError` checks.\n    Object.setPrototypeOf(this, new.target.prototype);\n    this.logLevel = logLevel;\n  }\n}\n"], "names": [], "mappings": "AAEA;AACO,MAAM,WAAY,SAAQ,KAAM,CAAA;AACvC;;AAKA,GAAS,WAAW,EAAQ,OAAO,EAAU,QAAQ,GAAiB,MAAM,EAAE;AAC9E,IAAI,KAAK,CAAC,OAAO,CAAC,CAAA,IAAA,CAAA,OAAA,GAAA,OAAA,CAClB;AACA,IAAI,IAAI,CAAC,IAAK,GAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAA;AACrD;AACA;AACA;AACA,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;AACrD,IAAI,IAAI,CAAC,QAAS,GAAE,QAAQ,CAAA;AAC5B,GAAE;AACF;;;;"}