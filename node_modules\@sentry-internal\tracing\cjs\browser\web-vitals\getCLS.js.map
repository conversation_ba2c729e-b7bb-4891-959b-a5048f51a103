{"version": 3, "file": "getCLS.js", "sources": ["../../../../src/browser/web-vitals/getCLS.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { initMetric } from './lib/initMetric';\nimport { observe } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport type { CLSMetric, ReportCallback, StopListening } from './types';\n\n/**\n * Calculates the [CLS](https://web.dev/cls/) value for the current page and\n * calls the `callback` function once the value is ready to be reported, along\n * with all `layout-shift` performance entries that were used in the metric\n * value calculation. The reported value is a `double` (corresponding to a\n * [layout shift score](https://web.dev/cls/#layout-shift-score)).\n *\n * If the `reportAllChanges` configuration option is set to `true`, the\n * `callback` function will be called as soon as the value is initially\n * determined as well as any time the value changes throughout the page\n * lifespan.\n *\n * _**Important:** CLS should be continually monitored for changes throughout\n * the entire lifespan of a page—including if the user returns to the page after\n * it's been hidden/backgrounded. However, since browsers often [will not fire\n * additional callbacks once the user has backgrounded a\n * page](https://developer.chrome.com/blog/page-lifecycle-api/#advice-hidden),\n * `callback` is always called when the page's visibility state changes to\n * hidden. As a result, the `callback` function might be called multiple times\n * during the same page load._\n */\nexport const onCLS = (\n  onReport: ReportCallback,\n  options: { reportAllChanges?: boolean } = {},\n): StopListening | undefined => {\n  const metric = initMetric('CLS', 0);\n  let report: ReturnType<typeof bindReporter>;\n\n  let sessionValue = 0;\n  let sessionEntries: PerformanceEntry[] = [];\n\n  // const handleEntries = (entries: Metric['entries']) => {\n  const handleEntries = (entries: LayoutShift[]): void => {\n    entries.forEach(entry => {\n      // Only count layout shifts without recent user input.\n      if (!entry.hadRecentInput) {\n        const firstSessionEntry = sessionEntries[0];\n        const lastSessionEntry = sessionEntries[sessionEntries.length - 1];\n\n        // If the entry occurred less than 1 second after the previous entry and\n        // less than 5 seconds after the first entry in the session, include the\n        // entry in the current session. Otherwise, start a new session.\n        if (\n          sessionValue &&\n          sessionEntries.length !== 0 &&\n          entry.startTime - lastSessionEntry.startTime < 1000 &&\n          entry.startTime - firstSessionEntry.startTime < 5000\n        ) {\n          sessionValue += entry.value;\n          sessionEntries.push(entry);\n        } else {\n          sessionValue = entry.value;\n          sessionEntries = [entry];\n        }\n\n        // If the current session value is larger than the current CLS value,\n        // update CLS and the entries contributing to it.\n        if (sessionValue > metric.value) {\n          metric.value = sessionValue;\n          metric.entries = sessionEntries;\n          if (report) {\n            report();\n          }\n        }\n      }\n    });\n  };\n\n  const po = observe('layout-shift', handleEntries);\n  if (po) {\n    report = bindReporter(onReport, metric, options.reportAllChanges);\n\n    const stopListening = (): void => {\n      handleEntries(po.takeRecords() as CLSMetric['entries']);\n      report(true);\n    };\n\n    onHidden(stopListening);\n\n    return stopListening;\n  }\n\n  return;\n};\n"], "names": ["initMetric", "observe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onHidden"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,QAAQ;AACrB,EAAE,QAAQ;AACV,EAAE,OAAO,GAAmC,EAAE;AAC9C,KAAgC;AAChC,EAAE,MAAM,SAASA,qBAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;AACrC,EAAE,IAAI,MAAM,CAAA;AACZ;AACA,EAAE,IAAI,YAAa,GAAE,CAAC,CAAA;AACtB,EAAE,IAAI,cAAc,GAAuB,EAAE,CAAA;AAC7C;AACA;AACA,EAAE,MAAM,aAAA,GAAgB,CAAC,OAAO,KAA0B;AAC1D,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS;AAC7B;AACA,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;AACjC,QAAQ,MAAM,iBAAkB,GAAE,cAAc,CAAC,CAAC,CAAC,CAAA;AACnD,QAAQ,MAAM,gBAAiB,GAAE,cAAc,CAAC,cAAc,CAAC,MAAA,GAAS,CAAC,CAAC,CAAA;AAC1E;AACA;AACA;AACA;AACA,QAAQ;AACR,UAAU,YAAa;AACvB,UAAU,cAAc,CAAC,MAAO,KAAI,CAAE;AACtC,UAAU,KAAK,CAAC,SAAU,GAAE,gBAAgB,CAAC,SAAA,GAAY,IAAK;AAC9D,UAAU,KAAK,CAAC,SAAA,GAAY,iBAAiB,CAAC,YAAY,IAAA;AAC1D,UAAU;AACV,UAAU,YAAa,IAAG,KAAK,CAAC,KAAK,CAAA;AACrC,UAAU,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AACpC,eAAe;AACf,UAAU,YAAa,GAAE,KAAK,CAAC,KAAK,CAAA;AACpC,UAAU,cAAe,GAAE,CAAC,KAAK,CAAC,CAAA;AAClC,SAAQ;AACR;AACA;AACA;AACA,QAAQ,IAAI,YAAA,GAAe,MAAM,CAAC,KAAK,EAAE;AACzC,UAAU,MAAM,CAAC,KAAM,GAAE,YAAY,CAAA;AACrC,UAAU,MAAM,CAAC,OAAQ,GAAE,cAAc,CAAA;AACzC,UAAU,IAAI,MAAM,EAAE;AACtB,YAAY,MAAM,EAAE,CAAA;AACpB,WAAU;AACV,SAAQ;AACR,OAAM;AACN,KAAK,CAAC,CAAA;AACN,GAAG,CAAA;AACH;AACA,EAAE,MAAM,KAAKC,eAAO,CAAC,cAAc,EAAE,aAAa,CAAC,CAAA;AACnD,EAAE,IAAI,EAAE,EAAE;AACV,IAAI,MAAA,GAASC,yBAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,gBAAgB,CAAC,CAAA;AACrE;AACA,IAAI,MAAM,aAAA,GAAgB,MAAY;AACtC,MAAM,aAAa,CAAC,EAAE,CAAC,WAAW,IAA2B,CAAA;AAC7D,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA;AAClB,KAAK,CAAA;AACL;AACA,IAAIC,iBAAQ,CAAC,aAAa,CAAC,CAAA;AAC3B;AACA,IAAI,OAAO,aAAa,CAAA;AACxB,GAAE;AACF;AACA,EAAE,OAAM;AACR;;;;"}