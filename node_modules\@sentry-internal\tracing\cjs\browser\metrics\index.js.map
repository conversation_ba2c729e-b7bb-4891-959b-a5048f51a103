{"version": 3, "file": "index.js", "sources": ["../../../../src/browser/metrics/index.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { IdleTransaction, Transaction } from '@sentry/core';\nimport {\n  Span,\n  getActiveTransaction,\n  getClient,\n  hasTracingEnabled,\n  isValidSampleRate,\n  setMeasurement,\n} from '@sentry/core';\nimport type { ClientOptions, Measurements, SpanContext, TransactionContext } from '@sentry/types';\nimport { browserPerformanceTimeOrigin, getComponentName, htmlTreeAsString, logger, parseUrl } from '@sentry/utils';\n\nimport { spanToJSON } from '@sentry/core';\nimport { DEBUG_BUILD } from '../../common/debug-build';\nimport {\n  addClsInstrumentationHandler,\n  addFidInstrumentationHandler,\n  addInpInstrumentationHandler,\n  addLcpInstrumentationHandler,\n  addPerformanceInstrumentationHandler,\n  addTtfbInstrumentationHandler,\n} from '../instrument';\nimport { WINDOW } from '../types';\nimport { getVisibilityWatcher } from '../web-vitals/lib/getVisibilityWatcher';\nimport type {\n  InteractionRouteNameMapping,\n  NavigatorDeviceMemory,\n  NavigatorNetworkInformation,\n} from '../web-vitals/types';\nimport { _startChild, isMeasurementValue } from './utils';\n\nimport { createSpanEnvelope } from '@sentry/core';\nimport { getNavigationEntry } from '../web-vitals/lib/getNavigationEntry';\n\nconst MAX_INT_AS_BYTES = **********;\n\n/**\n * Converts from milliseconds to seconds\n * @param time time in ms\n */\nfunction msToSec(time: number): number {\n  return time / 1000;\n}\n\nfunction getBrowserPerformanceAPI(): Performance | undefined {\n  // @ts-expect-error we want to make sure all of these are available, even if TS is sure they are\n  return WINDOW && WINDOW.addEventListener && WINDOW.performance;\n}\n\nlet _performanceCursor: number = 0;\n\nlet _measurements: Measurements = {};\nlet _lcpEntry: LargestContentfulPaint | undefined;\nlet _clsEntry: LayoutShift | undefined;\n\n/**\n * Start tracking web vitals.\n * The callback returned by this function can be used to stop tracking & ensure all measurements are final & captured.\n *\n * @returns A function that forces web vitals collection\n */\nexport function startTrackingWebVitals(): () => void {\n  const performance = getBrowserPerformanceAPI();\n  if (performance && browserPerformanceTimeOrigin) {\n    // @ts-expect-error we want to make sure all of these are available, even if TS is sure they are\n    if (performance.mark) {\n      WINDOW.performance.mark('sentry-tracing-init');\n    }\n    const fidCallback = _trackFID();\n    const clsCallback = _trackCLS();\n    const lcpCallback = _trackLCP();\n    const ttfbCallback = _trackTtfb();\n\n    return (): void => {\n      fidCallback();\n      clsCallback();\n      lcpCallback();\n      ttfbCallback();\n    };\n  }\n\n  return () => undefined;\n}\n\n/**\n * Start tracking long tasks.\n */\nexport function startTrackingLongTasks(): void {\n  addPerformanceInstrumentationHandler('longtask', ({ entries }) => {\n    for (const entry of entries) {\n      // eslint-disable-next-line deprecation/deprecation\n      const transaction = getActiveTransaction() as IdleTransaction | undefined;\n      if (!transaction) {\n        return;\n      }\n      const startTime = msToSec((browserPerformanceTimeOrigin as number) + entry.startTime);\n      const duration = msToSec(entry.duration);\n\n      // eslint-disable-next-line deprecation/deprecation\n      transaction.startChild({\n        description: 'Main UI thread blocked',\n        op: 'ui.long-task',\n        origin: 'auto.ui.browser.metrics',\n        startTimestamp: startTime,\n        endTimestamp: startTime + duration,\n      });\n    }\n  });\n}\n\n/**\n * Start tracking interaction events.\n */\nexport function startTrackingInteractions(): void {\n  addPerformanceInstrumentationHandler('event', ({ entries }) => {\n    for (const entry of entries) {\n      // eslint-disable-next-line deprecation/deprecation\n      const transaction = getActiveTransaction() as IdleTransaction | undefined;\n      if (!transaction) {\n        return;\n      }\n\n      if (entry.name === 'click') {\n        const startTime = msToSec((browserPerformanceTimeOrigin as number) + entry.startTime);\n        const duration = msToSec(entry.duration);\n\n        const span: SpanContext = {\n          description: htmlTreeAsString(entry.target),\n          op: `ui.interaction.${entry.name}`,\n          origin: 'auto.ui.browser.metrics',\n          startTimestamp: startTime,\n          endTimestamp: startTime + duration,\n        };\n\n        const componentName = getComponentName(entry.target);\n        if (componentName) {\n          span.attributes = { 'ui.component_name': componentName };\n        }\n\n        // eslint-disable-next-line deprecation/deprecation\n        transaction.startChild(span);\n      }\n    }\n  });\n}\n\n/**\n * Start tracking INP webvital events.\n */\nexport function startTrackingINP(\n  interactionIdtoRouteNameMapping: InteractionRouteNameMapping,\n  interactionsSampleRate: number,\n): () => void {\n  const performance = getBrowserPerformanceAPI();\n  if (performance && browserPerformanceTimeOrigin) {\n    const inpCallback = _trackINP(interactionIdtoRouteNameMapping, interactionsSampleRate);\n\n    return (): void => {\n      inpCallback();\n    };\n  }\n\n  return () => undefined;\n}\n\n/** Starts tracking the Cumulative Layout Shift on the current page. */\nfunction _trackCLS(): () => void {\n  return addClsInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1];\n    if (!entry) {\n      return;\n    }\n\n    DEBUG_BUILD && logger.log('[Measurements] Adding CLS');\n    _measurements['cls'] = { value: metric.value, unit: '' };\n    _clsEntry = entry as LayoutShift;\n  }, true);\n}\n\n/** Starts tracking the Largest Contentful Paint on the current page. */\nfunction _trackLCP(): () => void {\n  return addLcpInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1];\n    if (!entry) {\n      return;\n    }\n\n    DEBUG_BUILD && logger.log('[Measurements] Adding LCP');\n    _measurements['lcp'] = { value: metric.value, unit: 'millisecond' };\n    _lcpEntry = entry as LargestContentfulPaint;\n  }, true);\n}\n\n/** Starts tracking the First Input Delay on the current page. */\nfunction _trackFID(): () => void {\n  return addFidInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1];\n    if (!entry) {\n      return;\n    }\n\n    const timeOrigin = msToSec(browserPerformanceTimeOrigin as number);\n    const startTime = msToSec(entry.startTime);\n    DEBUG_BUILD && logger.log('[Measurements] Adding FID');\n    _measurements['fid'] = { value: metric.value, unit: 'millisecond' };\n    _measurements['mark.fid'] = { value: timeOrigin + startTime, unit: 'second' };\n  });\n}\n\nfunction _trackTtfb(): () => void {\n  return addTtfbInstrumentationHandler(({ metric }) => {\n    const entry = metric.entries[metric.entries.length - 1];\n    if (!entry) {\n      return;\n    }\n\n    DEBUG_BUILD && logger.log('[Measurements] Adding TTFB');\n    _measurements['ttfb'] = { value: metric.value, unit: 'millisecond' };\n  });\n}\n\nconst INP_ENTRY_MAP: Record<string, 'click' | 'hover' | 'drag' | 'press'> = {\n  click: 'click',\n  pointerdown: 'click',\n  pointerup: 'click',\n  mousedown: 'click',\n  mouseup: 'click',\n  touchstart: 'click',\n  touchend: 'click',\n  mouseover: 'hover',\n  mouseout: 'hover',\n  mouseenter: 'hover',\n  mouseleave: 'hover',\n  pointerover: 'hover',\n  pointerout: 'hover',\n  pointerenter: 'hover',\n  pointerleave: 'hover',\n  dragstart: 'drag',\n  dragend: 'drag',\n  drag: 'drag',\n  dragenter: 'drag',\n  dragleave: 'drag',\n  dragover: 'drag',\n  drop: 'drag',\n  keydown: 'press',\n  keyup: 'press',\n  keypress: 'press',\n  input: 'press',\n};\n\n/** Starts tracking the Interaction to Next Paint on the current page. */\nfunction _trackINP(\n  interactionIdToRouteNameMapping: InteractionRouteNameMapping,\n  interactionsSampleRate: number,\n): () => void {\n  return addInpInstrumentationHandler(({ metric }) => {\n    if (metric.value === undefined) {\n      return;\n    }\n    const entry = metric.entries.find(\n      entry => entry.duration === metric.value && INP_ENTRY_MAP[entry.name] !== undefined,\n    );\n    const client = getClient();\n    if (!entry || !client) {\n      return;\n    }\n    const interactionType = INP_ENTRY_MAP[entry.name];\n    const options = client.getOptions();\n    /** Build the INP span, create an envelope from the span, and then send the envelope */\n    const startTime = msToSec((browserPerformanceTimeOrigin as number) + entry.startTime);\n    const duration = msToSec(metric.value);\n    const interaction =\n      entry.interactionId !== undefined ? interactionIdToRouteNameMapping[entry.interactionId] : undefined;\n    if (interaction === undefined) {\n      return;\n    }\n    const { routeName, parentContext, activeTransaction, user, replayId } = interaction;\n    const userDisplay = user !== undefined ? user.email || user.id || user.ip_address : undefined;\n    // eslint-disable-next-line deprecation/deprecation\n    const profileId = activeTransaction !== undefined ? activeTransaction.getProfileId() : undefined;\n    const span = new Span({\n      startTimestamp: startTime,\n      endTimestamp: startTime + duration,\n      op: `ui.interaction.${interactionType}`,\n      name: htmlTreeAsString(entry.target),\n      attributes: {\n        release: options.release,\n        environment: options.environment,\n        transaction: routeName,\n        ...(userDisplay !== undefined && userDisplay !== '' ? { user: userDisplay } : {}),\n        ...(profileId !== undefined ? { profile_id: profileId } : {}),\n        ...(replayId !== undefined ? { replay_id: replayId } : {}),\n      },\n      exclusiveTime: metric.value,\n      measurements: {\n        inp: { value: metric.value, unit: 'millisecond' },\n      },\n    });\n\n    /** Check to see if the span should be sampled */\n    const sampleRate = getSampleRate(parentContext, options, interactionsSampleRate);\n\n    if (!sampleRate) {\n      return;\n    }\n\n    if (Math.random() < (sampleRate as number | boolean)) {\n      const envelope = span ? createSpanEnvelope([span], client.getDsn()) : undefined;\n      const transport = client && client.getTransport();\n      if (transport && envelope) {\n        transport.send(envelope).then(null, reason => {\n          DEBUG_BUILD && logger.error('Error while sending interaction:', reason);\n        });\n      }\n      return;\n    }\n  });\n}\n\n/** Add performance related spans to a transaction */\nexport function addPerformanceEntries(transaction: Transaction): void {\n  const performance = getBrowserPerformanceAPI();\n  if (!performance || !WINDOW.performance.getEntries || !browserPerformanceTimeOrigin) {\n    // Gatekeeper if performance API not available\n    return;\n  }\n\n  DEBUG_BUILD && logger.log('[Tracing] Adding & adjusting spans using Performance API');\n  const timeOrigin = msToSec(browserPerformanceTimeOrigin);\n\n  const performanceEntries = performance.getEntries();\n\n  const { op, start_timestamp: transactionStartTime } = spanToJSON(transaction);\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  performanceEntries.slice(_performanceCursor).forEach((entry: Record<string, any>) => {\n    const startTime = msToSec(entry.startTime);\n    const duration = msToSec(entry.duration);\n\n    // eslint-disable-next-line deprecation/deprecation\n    if (transaction.op === 'navigation' && transactionStartTime && timeOrigin + startTime < transactionStartTime) {\n      return;\n    }\n\n    switch (entry.entryType) {\n      case 'navigation': {\n        _addNavigationSpans(transaction, entry, timeOrigin);\n        break;\n      }\n      case 'mark':\n      case 'paint':\n      case 'measure': {\n        _addMeasureSpans(transaction, entry, startTime, duration, timeOrigin);\n\n        // capture web vitals\n        const firstHidden = getVisibilityWatcher();\n        // Only report if the page wasn't hidden prior to the web vital.\n        const shouldRecord = entry.startTime < firstHidden.firstHiddenTime;\n\n        if (entry.name === 'first-paint' && shouldRecord) {\n          DEBUG_BUILD && logger.log('[Measurements] Adding FP');\n          _measurements['fp'] = { value: entry.startTime, unit: 'millisecond' };\n        }\n        if (entry.name === 'first-contentful-paint' && shouldRecord) {\n          DEBUG_BUILD && logger.log('[Measurements] Adding FCP');\n          _measurements['fcp'] = { value: entry.startTime, unit: 'millisecond' };\n        }\n        break;\n      }\n      case 'resource': {\n        _addResourceSpans(transaction, entry, entry.name as string, startTime, duration, timeOrigin);\n        break;\n      }\n      default:\n      // Ignore other entry types.\n    }\n  });\n\n  _performanceCursor = Math.max(performanceEntries.length - 1, 0);\n\n  _trackNavigator(transaction);\n\n  // Measurements are only available for pageload transactions\n  if (op === 'pageload') {\n    _addTtfbRequestTimeToMeasurements(_measurements);\n\n    ['fcp', 'fp', 'lcp'].forEach(name => {\n      if (!_measurements[name] || !transactionStartTime || timeOrigin >= transactionStartTime) {\n        return;\n      }\n      // The web vitals, fcp, fp, lcp, and ttfb, all measure relative to timeOrigin.\n      // Unfortunately, timeOrigin is not captured within the transaction span data, so these web vitals will need\n      // to be adjusted to be relative to transaction.startTimestamp.\n      const oldValue = _measurements[name].value;\n      const measurementTimestamp = timeOrigin + msToSec(oldValue);\n\n      // normalizedValue should be in milliseconds\n      const normalizedValue = Math.abs((measurementTimestamp - transactionStartTime) * 1000);\n      const delta = normalizedValue - oldValue;\n\n      DEBUG_BUILD && logger.log(`[Measurements] Normalized ${name} from ${oldValue} to ${normalizedValue} (${delta})`);\n      _measurements[name].value = normalizedValue;\n    });\n\n    const fidMark = _measurements['mark.fid'];\n    if (fidMark && _measurements['fid']) {\n      // create span for FID\n      _startChild(transaction, {\n        description: 'first input delay',\n        endTimestamp: fidMark.value + msToSec(_measurements['fid'].value),\n        op: 'ui.action',\n        origin: 'auto.ui.browser.metrics',\n        startTimestamp: fidMark.value,\n      });\n\n      // Delete mark.fid as we don't want it to be part of final payload\n      delete _measurements['mark.fid'];\n    }\n\n    // If FCP is not recorded we should not record the cls value\n    // according to the new definition of CLS.\n    if (!('fcp' in _measurements)) {\n      delete _measurements.cls;\n    }\n\n    Object.keys(_measurements).forEach(measurementName => {\n      setMeasurement(measurementName, _measurements[measurementName].value, _measurements[measurementName].unit);\n    });\n\n    _tagMetricInfo(transaction);\n  }\n\n  _lcpEntry = undefined;\n  _clsEntry = undefined;\n  _measurements = {};\n}\n\n/** Create measure related spans */\nexport function _addMeasureSpans(\n  transaction: Transaction,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  entry: Record<string, any>,\n  startTime: number,\n  duration: number,\n  timeOrigin: number,\n): number {\n  const measureStartTimestamp = timeOrigin + startTime;\n  const measureEndTimestamp = measureStartTimestamp + duration;\n\n  _startChild(transaction, {\n    description: entry.name as string,\n    endTimestamp: measureEndTimestamp,\n    op: entry.entryType as string,\n    origin: 'auto.resource.browser.metrics',\n    startTimestamp: measureStartTimestamp,\n  });\n\n  return measureStartTimestamp;\n}\n\n/** Instrument navigation entries */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction _addNavigationSpans(transaction: Transaction, entry: Record<string, any>, timeOrigin: number): void {\n  ['unloadEvent', 'redirect', 'domContentLoadedEvent', 'loadEvent', 'connect'].forEach(event => {\n    _addPerformanceNavigationTiming(transaction, entry, event, timeOrigin);\n  });\n  _addPerformanceNavigationTiming(transaction, entry, 'secureConnection', timeOrigin, 'TLS/SSL', 'connectEnd');\n  _addPerformanceNavigationTiming(transaction, entry, 'fetch', timeOrigin, 'cache', 'domainLookupStart');\n  _addPerformanceNavigationTiming(transaction, entry, 'domainLookup', timeOrigin, 'DNS');\n  _addRequest(transaction, entry, timeOrigin);\n}\n\n/** Create performance navigation related spans */\nfunction _addPerformanceNavigationTiming(\n  transaction: Transaction,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  entry: Record<string, any>,\n  event: string,\n  timeOrigin: number,\n  description?: string,\n  eventEnd?: string,\n): void {\n  const end = eventEnd ? (entry[eventEnd] as number | undefined) : (entry[`${event}End`] as number | undefined);\n  const start = entry[`${event}Start`] as number | undefined;\n  if (!start || !end) {\n    return;\n  }\n  _startChild(transaction, {\n    op: 'browser',\n    origin: 'auto.browser.browser.metrics',\n    description: description || event,\n    startTimestamp: timeOrigin + msToSec(start),\n    endTimestamp: timeOrigin + msToSec(end),\n  });\n}\n\n/** Create request and response related spans */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction _addRequest(transaction: Transaction, entry: Record<string, any>, timeOrigin: number): void {\n  if (entry.responseEnd) {\n    // It is possible that we are collecting these metrics when the page hasn't finished loading yet, for example when the HTML slowly streams in.\n    // In this case, ie. when the document request hasn't finished yet, `entry.responseEnd` will be 0.\n    // In order not to produce faulty spans, where the end timestamp is before the start timestamp, we will only collect\n    // these spans when the responseEnd value is available. The backend (Relay) would drop the entire transaction if it contained faulty spans.\n    _startChild(transaction, {\n      op: 'browser',\n      origin: 'auto.browser.browser.metrics',\n      description: 'request',\n      startTimestamp: timeOrigin + msToSec(entry.requestStart as number),\n      endTimestamp: timeOrigin + msToSec(entry.responseEnd as number),\n    });\n\n    _startChild(transaction, {\n      op: 'browser',\n      origin: 'auto.browser.browser.metrics',\n      description: 'response',\n      startTimestamp: timeOrigin + msToSec(entry.responseStart as number),\n      endTimestamp: timeOrigin + msToSec(entry.responseEnd as number),\n    });\n  }\n}\n\nexport interface ResourceEntry extends Record<string, unknown> {\n  initiatorType?: string;\n  transferSize?: number;\n  encodedBodySize?: number;\n  decodedBodySize?: number;\n  renderBlockingStatus?: string;\n}\n\n/** Create resource-related spans */\nexport function _addResourceSpans(\n  transaction: Transaction,\n  entry: ResourceEntry,\n  resourceUrl: string,\n  startTime: number,\n  duration: number,\n  timeOrigin: number,\n): void {\n  // we already instrument based on fetch and xhr, so we don't need to\n  // duplicate spans here.\n  if (entry.initiatorType === 'xmlhttprequest' || entry.initiatorType === 'fetch') {\n    return;\n  }\n\n  const parsedUrl = parseUrl(resourceUrl);\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const data: Record<string, any> = {};\n  setResourceEntrySizeData(data, entry, 'transferSize', 'http.response_transfer_size');\n  setResourceEntrySizeData(data, entry, 'encodedBodySize', 'http.response_content_length');\n  setResourceEntrySizeData(data, entry, 'decodedBodySize', 'http.decoded_response_content_length');\n\n  if ('renderBlockingStatus' in entry) {\n    data['resource.render_blocking_status'] = entry.renderBlockingStatus;\n  }\n  if (parsedUrl.protocol) {\n    data['url.scheme'] = parsedUrl.protocol.split(':').pop(); // the protocol returned by parseUrl includes a :, but OTEL spec does not, so we remove it.\n  }\n\n  if (parsedUrl.host) {\n    data['server.address'] = parsedUrl.host;\n  }\n\n  data['url.same_origin'] = resourceUrl.includes(WINDOW.location.origin);\n\n  const startTimestamp = timeOrigin + startTime;\n  const endTimestamp = startTimestamp + duration;\n\n  _startChild(transaction, {\n    description: resourceUrl.replace(WINDOW.location.origin, ''),\n    endTimestamp,\n    op: entry.initiatorType ? `resource.${entry.initiatorType}` : 'resource.other',\n    origin: 'auto.resource.browser.metrics',\n    startTimestamp,\n    data,\n  });\n}\n\n/**\n * Capture the information of the user agent.\n */\nfunction _trackNavigator(transaction: Transaction): void {\n  const navigator = WINDOW.navigator as null | (Navigator & NavigatorNetworkInformation & NavigatorDeviceMemory);\n  if (!navigator) {\n    return;\n  }\n\n  // track network connectivity\n  const connection = navigator.connection;\n  if (connection) {\n    if (connection.effectiveType) {\n      // TODO: Can we rewrite this to an attribute?\n      // eslint-disable-next-line deprecation/deprecation\n      transaction.setTag('effectiveConnectionType', connection.effectiveType);\n    }\n\n    if (connection.type) {\n      // TODO: Can we rewrite this to an attribute?\n      // eslint-disable-next-line deprecation/deprecation\n      transaction.setTag('connectionType', connection.type);\n    }\n\n    if (isMeasurementValue(connection.rtt)) {\n      _measurements['connection.rtt'] = { value: connection.rtt, unit: 'millisecond' };\n    }\n  }\n\n  if (isMeasurementValue(navigator.deviceMemory)) {\n    // TODO: Can we rewrite this to an attribute?\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.setTag('deviceMemory', `${navigator.deviceMemory} GB`);\n  }\n\n  if (isMeasurementValue(navigator.hardwareConcurrency)) {\n    // TODO: Can we rewrite this to an attribute?\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.setTag('hardwareConcurrency', String(navigator.hardwareConcurrency));\n  }\n}\n\n/** Add LCP / CLS data to transaction to allow debugging */\nfunction _tagMetricInfo(transaction: Transaction): void {\n  if (_lcpEntry) {\n    DEBUG_BUILD && logger.log('[Measurements] Adding LCP Data');\n\n    // Capture Properties of the LCP element that contributes to the LCP.\n\n    if (_lcpEntry.element) {\n      // TODO: Can we rewrite this to an attribute?\n      // eslint-disable-next-line deprecation/deprecation\n      transaction.setTag('lcp.element', htmlTreeAsString(_lcpEntry.element));\n    }\n\n    if (_lcpEntry.id) {\n      // TODO: Can we rewrite this to an attribute?\n      // eslint-disable-next-line deprecation/deprecation\n      transaction.setTag('lcp.id', _lcpEntry.id);\n    }\n\n    if (_lcpEntry.url) {\n      // Trim URL to the first 200 characters.\n      // TODO: Can we rewrite this to an attribute?\n      // eslint-disable-next-line deprecation/deprecation\n      transaction.setTag('lcp.url', _lcpEntry.url.trim().slice(0, 200));\n    }\n\n    // TODO: Can we rewrite this to an attribute?\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.setTag('lcp.size', _lcpEntry.size);\n  }\n\n  // See: https://developer.mozilla.org/en-US/docs/Web/API/LayoutShift\n  if (_clsEntry && _clsEntry.sources) {\n    DEBUG_BUILD && logger.log('[Measurements] Adding CLS Data');\n    _clsEntry.sources.forEach((source, index) =>\n      // TODO: Can we rewrite this to an attribute?\n      // eslint-disable-next-line deprecation/deprecation\n      transaction.setTag(`cls.source.${index + 1}`, htmlTreeAsString(source.node)),\n    );\n  }\n}\n\nfunction setResourceEntrySizeData(\n  data: Record<string, unknown>,\n  entry: ResourceEntry,\n  key: keyof Pick<ResourceEntry, 'transferSize' | 'encodedBodySize' | 'decodedBodySize'>,\n  dataKey: 'http.response_transfer_size' | 'http.response_content_length' | 'http.decoded_response_content_length',\n): void {\n  const entryVal = entry[key];\n  if (entryVal != null && entryVal < MAX_INT_AS_BYTES) {\n    data[dataKey] = entryVal;\n  }\n}\n\n/**\n * Add ttfb request time information to measurements.\n *\n * ttfb information is added via vendored web vitals library.\n */\nfunction _addTtfbRequestTimeToMeasurements(_measurements: Measurements): void {\n  const navEntry = getNavigationEntry();\n  if (!navEntry) {\n    return;\n  }\n\n  const { responseStart, requestStart } = navEntry;\n\n  if (requestStart <= responseStart) {\n    DEBUG_BUILD && logger.log('[Measurements] Adding TTFB Request Time');\n    _measurements['ttfb.requestTime'] = {\n      value: responseStart - requestStart,\n      unit: 'millisecond',\n    };\n  }\n}\n\n/** Taken from @sentry/core sampling.ts */\nfunction getSampleRate(\n  transactionContext: TransactionContext | undefined,\n  options: ClientOptions,\n  interactionsSampleRate: number,\n): number | boolean {\n  if (!hasTracingEnabled(options)) {\n    return false;\n  }\n  let sampleRate;\n  if (transactionContext !== undefined && typeof options.tracesSampler === 'function') {\n    sampleRate = options.tracesSampler({\n      transactionContext,\n      name: transactionContext.name,\n      parentSampled: transactionContext.parentSampled,\n      attributes: {\n        // eslint-disable-next-line deprecation/deprecation\n        ...transactionContext.data,\n        ...transactionContext.attributes,\n      },\n      location: WINDOW.location,\n    });\n  } else if (transactionContext !== undefined && transactionContext.sampled !== undefined) {\n    sampleRate = transactionContext.sampled;\n  } else if (typeof options.tracesSampleRate !== 'undefined') {\n    sampleRate = options.tracesSampleRate;\n  } else {\n    sampleRate = 1;\n  }\n  if (!isValidSampleRate(sampleRate)) {\n    DEBUG_BUILD && logger.warn('[Tracing] Discarding interaction span because of invalid sample rate.');\n    return false;\n  }\n  if (sampleRate === true) {\n    return interactionsSampleRate;\n  } else if (sampleRate === false) {\n    return 0;\n  }\n  return sampleRate * interactionsSampleRate;\n}\n"], "names": ["WINDOW", "browserPerformanceTimeOrigin", "addPerformanceInstrumentationHandler", "getActiveTransaction", "htmlTreeAsString", "getComponentName", "addClsInstrumentationHandler", "DEBUG_BUILD", "logger", "addLcpInstrumentationHandler", "addFidInstrumentationHandler", "addTtfbInstrumentationHandler", "addInpInstrumentationHandler", "getClient", "Span", "createSpanEnvelope", "spanToJSON", "getVisibilityWatcher", "_startChild", "setMeasurement", "parseUrl", "isMeasurementValue", "getNavigationEntry", "hasTracingEnabled", "isValidSampleRate"], "mappings": ";;;;;;;;;;;AAmCA,MAAM,gBAAA,GAAmB,UAAU,CAAA;AACnC;AACA;AACA;AACA;AACA;AACA,SAAS,OAAO,CAAC,IAAI,EAAkB;AACvC,EAAE,OAAO,IAAK,GAAE,IAAI,CAAA;AACpB,CAAA;AACA;AACA,SAAS,wBAAwB,GAA4B;AAC7D;AACA,EAAE,OAAOA,gBAAUA,YAAM,CAAC,gBAAiB,IAAGA,YAAM,CAAC,WAAW,CAAA;AAChE,CAAA;AACA;AACA,IAAI,kBAAkB,GAAW,CAAC,CAAA;AAClC;AACA,IAAI,aAAa,GAAiB,EAAE,CAAA;AACpC,IAAI,SAAS,CAAA;AACb,IAAI,SAAS,CAAA;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,GAAe;AACrD,EAAE,MAAM,WAAA,GAAc,wBAAwB,EAAE,CAAA;AAChD,EAAE,IAAI,WAAY,IAAGC,kCAA4B,EAAE;AACnD;AACA,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE;AAC1B,MAAMD,YAAM,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;AACpD,KAAI;AACJ,IAAI,MAAM,WAAA,GAAc,SAAS,EAAE,CAAA;AACnC,IAAI,MAAM,WAAA,GAAc,SAAS,EAAE,CAAA;AACnC,IAAI,MAAM,WAAA,GAAc,SAAS,EAAE,CAAA;AACnC,IAAI,MAAM,YAAA,GAAe,UAAU,EAAE,CAAA;AACrC;AACA,IAAI,OAAO,MAAY;AACvB,MAAM,WAAW,EAAE,CAAA;AACnB,MAAM,WAAW,EAAE,CAAA;AACnB,MAAM,WAAW,EAAE,CAAA;AACnB,MAAM,YAAY,EAAE,CAAA;AACpB,KAAK,CAAA;AACL,GAAE;AACF;AACA,EAAE,OAAO,MAAM,SAAS,CAAA;AACxB,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,GAAS;AAC/C,EAAEE,+CAAoC,CAAC,UAAU,EAAE,CAAC,EAAE,OAAA,EAAS,KAAK;AACpE,IAAI,KAAK,MAAM,KAAM,IAAG,OAAO,EAAE;AACjC;AACA,MAAM,MAAM,WAAA,GAAcC,yBAAoB,EAAG,EAAA;AACjD,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,OAAM;AACd,OAAM;AACN,MAAM,MAAM,SAAA,GAAY,OAAO,CAAC,CAACF,kCAA6B,KAAa,KAAK,CAAC,SAAS,CAAC,CAAA;AAC3F,MAAM,MAAM,WAAW,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;AAC9C;AACA;AACA,MAAM,WAAW,CAAC,UAAU,CAAC;AAC7B,QAAQ,WAAW,EAAE,wBAAwB;AAC7C,QAAQ,EAAE,EAAE,cAAc;AAC1B,QAAQ,MAAM,EAAE,yBAAyB;AACzC,QAAQ,cAAc,EAAE,SAAS;AACjC,QAAQ,YAAY,EAAE,SAAU,GAAE,QAAQ;AAC1C,OAAO,CAAC,CAAA;AACR,KAAI;AACJ,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB,GAAS;AAClD,EAAEC,+CAAoC,CAAC,OAAO,EAAE,CAAC,EAAE,OAAA,EAAS,KAAK;AACjE,IAAI,KAAK,MAAM,KAAM,IAAG,OAAO,EAAE;AACjC;AACA,MAAM,MAAM,WAAA,GAAcC,yBAAoB,EAAG,EAAA;AACjD,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,QAAQ,OAAM;AACd,OAAM;AACN;AACA,MAAM,IAAI,KAAK,CAAC,IAAK,KAAI,OAAO,EAAE;AAClC,QAAQ,MAAM,SAAA,GAAY,OAAO,CAAC,CAACF,kCAA6B,KAAa,KAAK,CAAC,SAAS,CAAC,CAAA;AAC7F,QAAQ,MAAM,WAAW,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;AAChD;AACA,QAAQ,MAAM,IAAI,GAAgB;AAClC,UAAU,WAAW,EAAEG,sBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC;AACrD,UAAU,EAAE,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;AACA,UAAA,MAAA,EAAA,yBAAA;AACA,UAAA,cAAA,EAAA,SAAA;AACA,UAAA,YAAA,EAAA,SAAA,GAAA,QAAA;AACA,SAAA,CAAA;AACA;AACA,QAAA,MAAA,aAAA,GAAAC,sBAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,QAAA,IAAA,aAAA,EAAA;AACA,UAAA,IAAA,CAAA,UAAA,GAAA,EAAA,mBAAA,EAAA,aAAA,EAAA,CAAA;AACA,SAAA;AACA;AACA;AACA,QAAA,WAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,gBAAA;AACA,EAAA,+BAAA;AACA,EAAA,sBAAA;AACA,EAAA;AACA,EAAA,MAAA,WAAA,GAAA,wBAAA,EAAA,CAAA;AACA,EAAA,IAAA,WAAA,IAAAJ,kCAAA,EAAA;AACA,IAAA,MAAA,WAAA,GAAA,SAAA,CAAA,+BAAA,EAAA,sBAAA,CAAA,CAAA;AACA;AACA,IAAA,OAAA,MAAA;AACA,MAAA,WAAA,EAAA,CAAA;AACA,KAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,MAAA,SAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,SAAA,GAAA;AACA,EAAA,OAAAK,uCAAA,CAAA,CAAA,EAAA,MAAA,EAAA,KAAA;AACA,IAAA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAA,KAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAAC,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,2BAAA,CAAA,CAAA;AACA,IAAA,aAAA,CAAA,KAAA,CAAA,GAAA,EAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,EAAA,EAAA,CAAA;AACA,IAAA,SAAA,GAAA,KAAA,EAAA;AACA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,SAAA,GAAA;AACA,EAAA,OAAAC,uCAAA,CAAA,CAAA,EAAA,MAAA,EAAA,KAAA;AACA,IAAA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAA,KAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAAF,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,2BAAA,CAAA,CAAA;AACA,IAAA,aAAA,CAAA,KAAA,CAAA,GAAA,EAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,aAAA,EAAA,CAAA;AACA,IAAA,SAAA,GAAA,KAAA,EAAA;AACA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,SAAA,GAAA;AACA,EAAA,OAAAE,uCAAA,CAAA,CAAA,EAAA,MAAA,EAAA,KAAA;AACA,IAAA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAA,KAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,UAAA,GAAA,OAAA,CAAAT,kCAAA,EAAA,CAAA;AACA,IAAA,MAAA,SAAA,GAAA,OAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACA,IAAAM,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,2BAAA,CAAA,CAAA;AACA,IAAA,aAAA,CAAA,KAAA,CAAA,GAAA,EAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,aAAA,EAAA,CAAA;AACA,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,EAAA,KAAA,EAAA,UAAA,GAAA,SAAA,EAAA,IAAA,EAAA,QAAA,EAAA,CAAA;AACA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,UAAA,GAAA;AACA,EAAA,OAAAG,wCAAA,CAAA,CAAA,EAAA,MAAA,EAAA,KAAA;AACA,IAAA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,OAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAA,KAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAAJ,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,4BAAA,CAAA,CAAA;AACA,IAAA,aAAA,CAAA,MAAA,CAAA,GAAA,EAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,aAAA,EAAA,CAAA;AACA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA,MAAA,aAAA,GAAA;AACA,EAAA,KAAA,EAAA,OAAA;AACA,EAAA,WAAA,EAAA,OAAA;AACA,EAAA,SAAA,EAAA,OAAA;AACA,EAAA,SAAA,EAAA,OAAA;AACA,EAAA,OAAA,EAAA,OAAA;AACA,EAAA,UAAA,EAAA,OAAA;AACA,EAAA,QAAA,EAAA,OAAA;AACA,EAAA,SAAA,EAAA,OAAA;AACA,EAAA,QAAA,EAAA,OAAA;AACA,EAAA,UAAA,EAAA,OAAA;AACA,EAAA,UAAA,EAAA,OAAA;AACA,EAAA,WAAA,EAAA,OAAA;AACA,EAAA,UAAA,EAAA,OAAA;AACA,EAAA,YAAA,EAAA,OAAA;AACA,EAAA,YAAA,EAAA,OAAA;AACA,EAAA,SAAA,EAAA,MAAA;AACA,EAAA,OAAA,EAAA,MAAA;AACA,EAAA,IAAA,EAAA,MAAA;AACA,EAAA,SAAA,EAAA,MAAA;AACA,EAAA,SAAA,EAAA,MAAA;AACA,EAAA,QAAA,EAAA,MAAA;AACA,EAAA,IAAA,EAAA,MAAA;AACA,EAAA,OAAA,EAAA,OAAA;AACA,EAAA,KAAA,EAAA,OAAA;AACA,EAAA,QAAA,EAAA,OAAA;AACA,EAAA,KAAA,EAAA,OAAA;AACA,CAAA,CAAA;AACA;AACA;AACA,SAAA,SAAA;AACA,EAAA,+BAAA;AACA,EAAA,sBAAA;AACA,EAAA;AACA,EAAA,OAAAI,uCAAA,CAAA,CAAA,EAAA,MAAA,EAAA,KAAA;AACA,IAAA,IAAA,MAAA,CAAA,KAAA,KAAA,SAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA,IAAA,MAAA,KAAA,GAAA,MAAA,CAAA,OAAA,CAAA,IAAA;AACA,MAAA,KAAA,IAAA,KAAA,CAAA,QAAA,KAAA,MAAA,CAAA,KAAA,IAAA,aAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,SAAA;AACA,KAAA,CAAA;AACA,IAAA,MAAA,MAAA,GAAAC,cAAA,EAAA,CAAA;AACA,IAAA,IAAA,CAAA,KAAA,IAAA,CAAA,MAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA,IAAA,MAAA,eAAA,GAAA,aAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACA,IAAA,MAAA,OAAA,GAAA,MAAA,CAAA,UAAA,EAAA,CAAA;AACA;AACA,IAAA,MAAA,SAAA,GAAA,OAAA,CAAA,CAAAZ,kCAAA,KAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACA,IAAA,MAAA,QAAA,GAAA,OAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,WAAA;AACA,MAAA,KAAA,CAAA,aAAA,KAAA,SAAA,GAAA,+BAAA,CAAA,KAAA,CAAA,aAAA,CAAA,GAAA,SAAA,CAAA;AACA,IAAA,IAAA,WAAA,KAAA,SAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA,IAAA,MAAA,EAAA,SAAA,EAAA,aAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,QAAA,EAAA,GAAA,WAAA,CAAA;AACA,IAAA,MAAA,WAAA,GAAA,IAAA,KAAA,SAAA,GAAA,IAAA,CAAA,KAAA,IAAA,IAAA,CAAA,EAAA,IAAA,IAAA,CAAA,UAAA,GAAA,SAAA,CAAA;AACA;AACA,IAAA,MAAA,SAAA,GAAA,iBAAA,KAAA,SAAA,GAAA,iBAAA,CAAA,YAAA,EAAA,GAAA,SAAA,CAAA;AACA,IAAA,MAAA,IAAA,GAAA,IAAAa,SAAA,CAAA;AACA,MAAA,cAAA,EAAA,SAAA;AACA,MAAA,YAAA,EAAA,SAAA,GAAA,QAAA;AACA,MAAA,EAAA,EAAA,CAAA,eAAA,EAAA,eAAA,CAAA,CAAA;AACA,MAAA,IAAA,EAAAV,sBAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AACA,MAAA,UAAA,EAAA;AACA,QAAA,OAAA,EAAA,OAAA,CAAA,OAAA;AACA,QAAA,WAAA,EAAA,OAAA,CAAA,WAAA;AACA,QAAA,WAAA,EAAA,SAAA;AACA,QAAA,IAAA,WAAA,KAAA,SAAA,IAAA,WAAA,KAAA,EAAA,GAAA,EAAA,IAAA,EAAA,WAAA,EAAA,GAAA,EAAA,CAAA;AACA,QAAA,IAAA,SAAA,KAAA,SAAA,GAAA,EAAA,UAAA,EAAA,SAAA,EAAA,GAAA,EAAA,CAAA;AACA,QAAA,IAAA,QAAA,KAAA,SAAA,GAAA,EAAA,SAAA,EAAA,QAAA,EAAA,GAAA,EAAA,CAAA;AACA,OAAA;AACA,MAAA,aAAA,EAAA,MAAA,CAAA,KAAA;AACA,MAAA,YAAA,EAAA;AACA,QAAA,GAAA,EAAA,EAAA,KAAA,EAAA,MAAA,CAAA,KAAA,EAAA,IAAA,EAAA,aAAA,EAAA;AACA,OAAA;AACA,KAAA,CAAA,CAAA;AACA;AACA;AACA,IAAA,MAAA,UAAA,GAAA,aAAA,CAAA,aAAA,EAAA,OAAA,EAAA,sBAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,CAAA,UAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,IAAA,CAAA,MAAA,EAAA,IAAA,UAAA,EAAA,EAAA;AACA,MAAA,MAAA,QAAA,GAAA,IAAA,GAAAW,uBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,MAAA,CAAA,MAAA,EAAA,CAAA,GAAA,SAAA,CAAA;AACA,MAAA,MAAA,SAAA,GAAA,MAAA,IAAA,MAAA,CAAA,YAAA,EAAA,CAAA;AACA,MAAA,IAAA,SAAA,IAAA,QAAA,EAAA;AACA,QAAA,SAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,MAAA,IAAA;AACA,UAAAR,sBAAA,IAAAC,YAAA,CAAA,KAAA,CAAA,kCAAA,EAAA,MAAA,CAAA,CAAA;AACA,SAAA,CAAA,CAAA;AACA,OAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,WAAA,EAAA;AACA,EAAA,MAAA,WAAA,GAAA,wBAAA,EAAA,CAAA;AACA,EAAA,IAAA,CAAA,WAAA,IAAA,CAAAR,YAAA,CAAA,WAAA,CAAA,UAAA,IAAA,CAAAC,kCAAA,EAAA;AACA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA,EAAAM,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,0DAAA,CAAA,CAAA;AACA,EAAA,MAAA,UAAA,GAAA,OAAA,CAAAP,kCAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,kBAAA,GAAA,WAAA,CAAA,UAAA,EAAA,CAAA;AACA;AACA,EAAA,MAAA,EAAA,EAAA,EAAA,eAAA,EAAA,oBAAA,EAAA,GAAAe,eAAA,CAAA,WAAA,CAAA,CAAA;AACA;AACA;AACA,EAAA,kBAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,CAAA,OAAA,CAAA,CAAA,KAAA,KAAA;AACA,IAAA,MAAA,SAAA,GAAA,OAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACA,IAAA,MAAA,QAAA,GAAA,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACA;AACA;AACA,IAAA,IAAA,WAAA,CAAA,EAAA,KAAA,YAAA,IAAA,oBAAA,IAAA,UAAA,GAAA,SAAA,GAAA,oBAAA,EAAA;AACA,MAAA,OAAA;AACA,KAAA;AACA;AACA,IAAA,QAAA,KAAA,CAAA,SAAA;AACA,MAAA,KAAA,YAAA,EAAA;AACA,QAAA,mBAAA,CAAA,WAAA,EAAA,KAAA,EAAA,UAAA,CAAA,CAAA;AACA,QAAA,MAAA;AACA,OAAA;AACA,MAAA,KAAA,MAAA,CAAA;AACA,MAAA,KAAA,OAAA,CAAA;AACA,MAAA,KAAA,SAAA,EAAA;AACA,QAAA,gBAAA,CAAA,WAAA,EAAA,KAAA,EAAA,SAAA,EAAA,QAAA,EAAA,UAAA,CAAA,CAAA;AACA;AACA;AACA,QAAA,MAAA,WAAA,GAAAC,yCAAA,EAAA,CAAA;AACA;AACA,QAAA,MAAA,YAAA,GAAA,KAAA,CAAA,SAAA,GAAA,WAAA,CAAA,eAAA,CAAA;AACA;AACA,QAAA,IAAA,KAAA,CAAA,IAAA,KAAA,aAAA,IAAA,YAAA,EAAA;AACA,UAAAV,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,0BAAA,CAAA,CAAA;AACA,UAAA,aAAA,CAAA,IAAA,CAAA,GAAA,EAAA,KAAA,EAAA,KAAA,CAAA,SAAA,EAAA,IAAA,EAAA,aAAA,EAAA,CAAA;AACA,SAAA;AACA,QAAA,IAAA,KAAA,CAAA,IAAA,KAAA,wBAAA,IAAA,YAAA,EAAA;AACA,UAAAD,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,2BAAA,CAAA,CAAA;AACA,UAAA,aAAA,CAAA,KAAA,CAAA,GAAA,EAAA,KAAA,EAAA,KAAA,CAAA,SAAA,EAAA,IAAA,EAAA,aAAA,EAAA,CAAA;AACA,SAAA;AACA,QAAA,MAAA;AACA,OAAA;AACA,MAAA,KAAA,UAAA,EAAA;AACA,QAAA,iBAAA,CAAA,WAAA,EAAA,KAAA,EAAA,KAAA,CAAA,IAAA,GAAA,SAAA,EAAA,QAAA,EAAA,UAAA,CAAA,CAAA;AACA,QAAA,MAAA;AACA,OAAA;AAEA;AACA,KAAA;AACA,GAAA,CAAA,CAAA;AACA;AACA,EAAA,kBAAA,GAAA,IAAA,CAAA,GAAA,CAAA,kBAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA;AACA,EAAA,eAAA,CAAA,WAAA,CAAA,CAAA;AACA;AACA;AACA,EAAA,IAAA,EAAA,KAAA,UAAA,EAAA;AACA,IAAA,iCAAA,CAAA,aAAA,CAAA,CAAA;AACA;AACA,IAAA,CAAA,KAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA,OAAA,CAAA,IAAA,IAAA;AACA,MAAA,IAAA,CAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,oBAAA,IAAA,UAAA,IAAA,oBAAA,EAAA;AACA,QAAA,OAAA;AACA,OAAA;AACA;AACA;AACA;AACA,MAAA,MAAA,QAAA,GAAA,aAAA,CAAA,IAAA,CAAA,CAAA,KAAA,CAAA;AACA,MAAA,MAAA,oBAAA,GAAA,UAAA,GAAA,OAAA,CAAA,QAAA,CAAA,CAAA;AACA;AACA;AACA,MAAA,MAAA,eAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA,oBAAA,GAAA,oBAAA,IAAA,IAAA,CAAA,CAAA;AACA,MAAA,MAAA,KAAA,GAAA,eAAA,GAAA,QAAA,CAAA;AACA;AACA,MAAAD,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,CAAA,0BAAA,EAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA,IAAA,EAAA,eAAA,CAAA,EAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,aAAA,CAAA,IAAA,CAAA,CAAA,KAAA,GAAA,eAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA;AACA,IAAA,MAAA,OAAA,GAAA,aAAA,CAAA,UAAA,CAAA,CAAA;AACA,IAAA,IAAA,OAAA,IAAA,aAAA,CAAA,KAAA,CAAA,EAAA;AACA;AACA,MAAAU,mBAAA,CAAA,WAAA,EAAA;AACA,QAAA,WAAA,EAAA,mBAAA;AACA,QAAA,YAAA,EAAA,OAAA,CAAA,KAAA,GAAA,OAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA,KAAA,CAAA;AACA,QAAA,EAAA,EAAA,WAAA;AACA,QAAA,MAAA,EAAA,yBAAA;AACA,QAAA,cAAA,EAAA,OAAA,CAAA,KAAA;AACA,OAAA,CAAA,CAAA;AACA;AACA;AACA,MAAA,OAAA,aAAA,CAAA,UAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA;AACA;AACA,IAAA,IAAA,EAAA,KAAA,IAAA,aAAA,CAAA,EAAA;AACA,MAAA,OAAA,aAAA,CAAA,GAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,OAAA,CAAA,eAAA,IAAA;AACA,MAAAC,mBAAA,CAAA,eAAA,EAAA,aAAA,CAAA,eAAA,CAAA,CAAA,KAAA,EAAA,aAAA,CAAA,eAAA,CAAA,CAAA,IAAA,CAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA;AACA,IAAA,cAAA,CAAA,WAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,SAAA,GAAA,SAAA,CAAA;AACA,EAAA,SAAA,GAAA,SAAA,CAAA;AACA,EAAA,aAAA,GAAA,EAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,gBAAA;AACA,EAAA,WAAA;AACA;AACA,EAAA,KAAA;AACA,EAAA,SAAA;AACA,EAAA,QAAA;AACA,EAAA,UAAA;AACA,EAAA;AACA,EAAA,MAAA,qBAAA,GAAA,UAAA,GAAA,SAAA,CAAA;AACA,EAAA,MAAA,mBAAA,GAAA,qBAAA,GAAA,QAAA,CAAA;AACA;AACA,EAAAD,mBAAA,CAAA,WAAA,EAAA;AACA,IAAA,WAAA,EAAA,KAAA,CAAA,IAAA;AACA,IAAA,YAAA,EAAA,mBAAA;AACA,IAAA,EAAA,EAAA,KAAA,CAAA,SAAA;AACA,IAAA,MAAA,EAAA,+BAAA;AACA,IAAA,cAAA,EAAA,qBAAA;AACA,GAAA,CAAA,CAAA;AACA;AACA,EAAA,OAAA,qBAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA,SAAA,mBAAA,CAAA,WAAA,EAAA,KAAA,EAAA,UAAA,EAAA;AACA,EAAA,CAAA,aAAA,EAAA,UAAA,EAAA,uBAAA,EAAA,WAAA,EAAA,SAAA,CAAA,CAAA,OAAA,CAAA,KAAA,IAAA;AACA,IAAA,+BAAA,CAAA,WAAA,EAAA,KAAA,EAAA,KAAA,EAAA,UAAA,CAAA,CAAA;AACA,GAAA,CAAA,CAAA;AACA,EAAA,+BAAA,CAAA,WAAA,EAAA,KAAA,EAAA,kBAAA,EAAA,UAAA,EAAA,SAAA,EAAA,YAAA,CAAA,CAAA;AACA,EAAA,+BAAA,CAAA,WAAA,EAAA,KAAA,EAAA,OAAA,EAAA,UAAA,EAAA,OAAA,EAAA,mBAAA,CAAA,CAAA;AACA,EAAA,+BAAA,CAAA,WAAA,EAAA,KAAA,EAAA,cAAA,EAAA,UAAA,EAAA,KAAA,CAAA,CAAA;AACA,EAAA,WAAA,CAAA,WAAA,EAAA,KAAA,EAAA,UAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,+BAAA;AACA,EAAA,WAAA;AACA;AACA,EAAA,KAAA;AACA,EAAA,KAAA;AACA,EAAA,UAAA;AACA,EAAA,WAAA;AACA,EAAA,QAAA;AACA,EAAA;AACA,EAAA,MAAA,GAAA,GAAA,QAAA,IAAA,KAAA,CAAA,QAAA,CAAA,MAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA;AACA,EAAA,MAAA,KAAA,GAAA,KAAA,CAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AACA,EAAA,IAAA,CAAA,KAAA,IAAA,CAAA,GAAA,EAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA,EAAAA,mBAAA,CAAA,WAAA,EAAA;AACA,IAAA,EAAA,EAAA,SAAA;AACA,IAAA,MAAA,EAAA,8BAAA;AACA,IAAA,WAAA,EAAA,WAAA,IAAA,KAAA;AACA,IAAA,cAAA,EAAA,UAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AACA,IAAA,YAAA,EAAA,UAAA,GAAA,OAAA,CAAA,GAAA,CAAA;AACA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA,SAAA,WAAA,CAAA,WAAA,EAAA,KAAA,EAAA,UAAA,EAAA;AACA,EAAA,IAAA,KAAA,CAAA,WAAA,EAAA;AACA;AACA;AACA;AACA;AACA,IAAAA,mBAAA,CAAA,WAAA,EAAA;AACA,MAAA,EAAA,EAAA,SAAA;AACA,MAAA,MAAA,EAAA,8BAAA;AACA,MAAA,WAAA,EAAA,SAAA;AACA,MAAA,cAAA,EAAA,UAAA,GAAA,OAAA,CAAA,KAAA,CAAA,YAAA,EAAA;AACA,MAAA,YAAA,EAAA,UAAA,GAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA;AACA,KAAA,CAAA,CAAA;AACA;AACA,IAAAA,mBAAA,CAAA,WAAA,EAAA;AACA,MAAA,EAAA,EAAA,SAAA;AACA,MAAA,MAAA,EAAA,8BAAA;AACA,MAAA,WAAA,EAAA,UAAA;AACA,MAAA,cAAA,EAAA,UAAA,GAAA,OAAA,CAAA,KAAA,CAAA,aAAA,EAAA;AACA,MAAA,YAAA,EAAA,UAAA,GAAA,OAAA,CAAA,KAAA,CAAA,WAAA,EAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA;AACA,CAAA;;AAUA;AACA,SAAA,iBAAA;AACA,EAAA,WAAA;AACA,EAAA,KAAA;AACA,EAAA,WAAA;AACA,EAAA,SAAA;AACA,EAAA,QAAA;AACA,EAAA,UAAA;AACA,EAAA;AACA;AACA;AACA,EAAA,IAAA,KAAA,CAAA,aAAA,KAAA,gBAAA,IAAA,KAAA,CAAA,aAAA,KAAA,OAAA,EAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,SAAA,GAAAE,cAAA,CAAA,WAAA,CAAA,CAAA;AACA;AACA;AACA,EAAA,MAAA,IAAA,GAAA,EAAA,CAAA;AACA,EAAA,wBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,cAAA,EAAA,6BAAA,CAAA,CAAA;AACA,EAAA,wBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,8BAAA,CAAA,CAAA;AACA,EAAA,wBAAA,CAAA,IAAA,EAAA,KAAA,EAAA,iBAAA,EAAA,sCAAA,CAAA,CAAA;AACA;AACA,EAAA,IAAA,sBAAA,IAAA,KAAA,EAAA;AACA,IAAA,IAAA,CAAA,iCAAA,CAAA,GAAA,KAAA,CAAA,oBAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,SAAA,CAAA,QAAA,EAAA;AACA,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,SAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,EAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,SAAA,CAAA,IAAA,EAAA;AACA,IAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,WAAA,CAAA,QAAA,CAAApB,YAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,cAAA,GAAA,UAAA,GAAA,SAAA,CAAA;AACA,EAAA,MAAA,YAAA,GAAA,cAAA,GAAA,QAAA,CAAA;AACA;AACA,EAAAkB,mBAAA,CAAA,WAAA,EAAA;AACA,IAAA,WAAA,EAAA,WAAA,CAAA,OAAA,CAAAlB,YAAA,CAAA,QAAA,CAAA,MAAA,EAAA,EAAA,CAAA;AACA,IAAA,YAAA;AACA,IAAA,EAAA,EAAA,KAAA,CAAA,aAAA,GAAA,CAAA,SAAA,EAAA,KAAA,CAAA,aAAA,CAAA,CAAA,GAAA,gBAAA;AACA,IAAA,MAAA,EAAA,+BAAA;AACA,IAAA,cAAA;AACA,IAAA,IAAA;AACA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,eAAA,CAAA,WAAA,EAAA;AACA,EAAA,MAAA,SAAA,GAAAA,YAAA,CAAA,SAAA,EAAA;AACA,EAAA,IAAA,CAAA,SAAA,EAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA;AACA,EAAA,MAAA,UAAA,GAAA,SAAA,CAAA,UAAA,CAAA;AACA,EAAA,IAAA,UAAA,EAAA;AACA,IAAA,IAAA,UAAA,CAAA,aAAA,EAAA;AACA;AACA;AACA,MAAA,WAAA,CAAA,MAAA,CAAA,yBAAA,EAAA,UAAA,CAAA,aAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,UAAA,CAAA,IAAA,EAAA;AACA;AACA;AACA,MAAA,WAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAAqB,0BAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,MAAA,aAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,KAAA,EAAA,UAAA,CAAA,GAAA,EAAA,IAAA,EAAA,aAAA,EAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,IAAAA,0BAAA,CAAA,SAAA,CAAA,YAAA,CAAA,EAAA;AACA;AACA;AACA,IAAA,WAAA,CAAA,MAAA,CAAA,cAAA,EAAA,CAAA,EAAA,SAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAAA,0BAAA,CAAA,SAAA,CAAA,mBAAA,CAAA,EAAA;AACA;AACA;AACA,IAAA,WAAA,CAAA,MAAA,CAAA,qBAAA,EAAA,MAAA,CAAA,SAAA,CAAA,mBAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,cAAA,CAAA,WAAA,EAAA;AACA,EAAA,IAAA,SAAA,EAAA;AACA,IAAAd,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,gCAAA,CAAA,CAAA;AACA;AACA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,OAAA,EAAA;AACA;AACA;AACA,MAAA,WAAA,CAAA,MAAA,CAAA,aAAA,EAAAJ,sBAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,EAAA,EAAA;AACA;AACA;AACA,MAAA,WAAA,CAAA,MAAA,CAAA,QAAA,EAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,SAAA,CAAA,GAAA,EAAA;AACA;AACA;AACA;AACA,MAAA,WAAA,CAAA,MAAA,CAAA,SAAA,EAAA,SAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA;AACA;AACA,IAAA,WAAA,CAAA,MAAA,CAAA,UAAA,EAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,EAAA,IAAA,SAAA,IAAA,SAAA,CAAA,OAAA,EAAA;AACA,IAAAG,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,gCAAA,CAAA,CAAA;AACA,IAAA,SAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,MAAA,EAAA,KAAA;AACA;AACA;AACA,MAAA,WAAA,CAAA,MAAA,CAAA,CAAA,WAAA,EAAA,KAAA,GAAA,CAAA,CAAA,CAAA,EAAAJ,sBAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA;AACA,KAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA,SAAA,wBAAA;AACA,EAAA,IAAA;AACA,EAAA,KAAA;AACA,EAAA,GAAA;AACA,EAAA,OAAA;AACA,EAAA;AACA,EAAA,MAAA,QAAA,GAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AACA,EAAA,IAAA,QAAA,IAAA,IAAA,IAAA,QAAA,GAAA,gBAAA,EAAA;AACA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,QAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,iCAAA,CAAA,aAAA,EAAA;AACA,EAAA,MAAA,QAAA,GAAAkB,qCAAA,EAAA,CAAA;AACA,EAAA,IAAA,CAAA,QAAA,EAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,EAAA,aAAA,EAAA,YAAA,EAAA,GAAA,QAAA,CAAA;AACA;AACA,EAAA,IAAA,YAAA,IAAA,aAAA,EAAA;AACA,IAAAf,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,yCAAA,CAAA,CAAA;AACA,IAAA,aAAA,CAAA,kBAAA,CAAA,GAAA;AACA,MAAA,KAAA,EAAA,aAAA,GAAA,YAAA;AACA,MAAA,IAAA,EAAA,aAAA;AACA,KAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,aAAA;AACA,EAAA,kBAAA;AACA,EAAA,OAAA;AACA,EAAA,sBAAA;AACA,EAAA;AACA,EAAA,IAAA,CAAAe,sBAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,UAAA,CAAA;AACA,EAAA,IAAA,kBAAA,KAAA,SAAA,IAAA,OAAA,OAAA,CAAA,aAAA,KAAA,UAAA,EAAA;AACA,IAAA,UAAA,GAAA,OAAA,CAAA,aAAA,CAAA;AACA,MAAA,kBAAA;AACA,MAAA,IAAA,EAAA,kBAAA,CAAA,IAAA;AACA,MAAA,aAAA,EAAA,kBAAA,CAAA,aAAA;AACA,MAAA,UAAA,EAAA;AACA;AACA,QAAA,GAAA,kBAAA,CAAA,IAAA;AACA,QAAA,GAAA,kBAAA,CAAA,UAAA;AACA,OAAA;AACA,MAAA,QAAA,EAAAvB,YAAA,CAAA,QAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA,MAAA,IAAA,kBAAA,KAAA,SAAA,IAAA,kBAAA,CAAA,OAAA,KAAA,SAAA,EAAA;AACA,IAAA,UAAA,GAAA,kBAAA,CAAA,OAAA,CAAA;AACA,GAAA,MAAA,IAAA,OAAA,OAAA,CAAA,gBAAA,KAAA,WAAA,EAAA;AACA,IAAA,UAAA,GAAA,OAAA,CAAA,gBAAA,CAAA;AACA,GAAA,MAAA;AACA,IAAA,UAAA,GAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,CAAAwB,sBAAA,CAAA,UAAA,CAAA,EAAA;AACA,IAAAjB,sBAAA,IAAAC,YAAA,CAAA,IAAA,CAAA,uEAAA,CAAA,CAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,UAAA,KAAA,IAAA,EAAA;AACA,IAAA,OAAA,sBAAA,CAAA;AACA,GAAA,MAAA,IAAA,UAAA,KAAA,KAAA,EAAA;AACA,IAAA,OAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,OAAA,UAAA,GAAA,sBAAA,CAAA;AACA;;;;;;;;;;"}