{"version": 3, "file": "node-stack-trace.js", "sources": ["../../src/node-stack-trace.ts"], "sourcesContent": ["// This code was originally forked from https://github.com/felixge/node-stack-trace\n// Since then it has been highly modified to fit our needs.\n\n// Copyright (c) 2011 <PERSON> (<EMAIL>)//\n//\n//  Permission is hereby granted, free of charge, to any person obtaining a copy\n//  of this software and associated documentation files (the \"Software\"), to deal\n//  in the Software without restriction, including without limitation the rights\n//  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n//  copies of the Software, and to permit persons to whom the Software is\n//  furnished to do so, subject to the following conditions://\n//\n//  The above copyright notice and this permission notice shall be included in\n//  all copies or substantial portions of the Software.//\n//\n//  THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n//  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n//  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n//  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n//  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n//  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n//  THE SOFTWARE.\n\nimport type { StackLineParserFn } from '@sentry/types';\n\nexport type GetModuleFn = (filename: string | undefined) => string | undefined;\n\n/**\n * Does this filename look like it's part of the app code?\n */\nexport function filenameIsInApp(filename: string, isNative: boolean = false): boolean {\n  const isInternal =\n    isNative ||\n    (filename &&\n      // It's not internal if it's an absolute linux path\n      !filename.startsWith('/') &&\n      // It's not internal if it's an absolute windows path\n      !filename.match(/^[A-Z]:/) &&\n      // It's not internal if the path is starting with a dot\n      !filename.startsWith('.') &&\n      // It's not internal if the frame has a protocol. In node, this is usually the case if the file got pre-processed with a bundler like webpack\n      !filename.match(/^[a-zA-Z]([a-zA-Z0-9.\\-+])*:\\/\\//)); // Schema from: https://stackoverflow.com/a/3641782\n\n  // in_app is all that's not an internal Node function or a module within node_modules\n  // note that isNative appears to return true even for node core libraries\n  // see https://github.com/getsentry/raven-node/issues/176\n\n  return !isInternal && filename !== undefined && !filename.includes('node_modules/');\n}\n\n/** Node Stack line parser */\n// eslint-disable-next-line complexity\nexport function node(getModule?: GetModuleFn): StackLineParserFn {\n  const FILENAME_MATCH = /^\\s*[-]{4,}$/;\n  const FULL_MATCH = /at (?:async )?(?:(.+?)\\s+\\()?(?:(.+):(\\d+):(\\d+)?|([^)]+))\\)?/;\n\n  // eslint-disable-next-line complexity\n  return (line: string) => {\n    const lineMatch = line.match(FULL_MATCH);\n\n    if (lineMatch) {\n      let object: string | undefined;\n      let method: string | undefined;\n      let functionName: string | undefined;\n      let typeName: string | undefined;\n      let methodName: string | undefined;\n\n      if (lineMatch[1]) {\n        functionName = lineMatch[1];\n\n        let methodStart = functionName.lastIndexOf('.');\n        if (functionName[methodStart - 1] === '.') {\n          methodStart--;\n        }\n\n        if (methodStart > 0) {\n          object = functionName.slice(0, methodStart);\n          method = functionName.slice(methodStart + 1);\n          const objectEnd = object.indexOf('.Module');\n          if (objectEnd > 0) {\n            functionName = functionName.slice(objectEnd + 1);\n            object = object.slice(0, objectEnd);\n          }\n        }\n        typeName = undefined;\n      }\n\n      if (method) {\n        typeName = object;\n        methodName = method;\n      }\n\n      if (method === '<anonymous>') {\n        methodName = undefined;\n        functionName = undefined;\n      }\n\n      if (functionName === undefined) {\n        methodName = methodName || '<anonymous>';\n        functionName = typeName ? `${typeName}.${methodName}` : methodName;\n      }\n\n      let filename = lineMatch[2] && lineMatch[2].startsWith('file://') ? lineMatch[2].slice(7) : lineMatch[2];\n      const isNative = lineMatch[5] === 'native';\n\n      // If it's a Windows path, trim the leading slash so that `/C:/foo` becomes `C:/foo`\n      if (filename && filename.match(/\\/[A-Z]:/)) {\n        filename = filename.slice(1);\n      }\n\n      if (!filename && lineMatch[5] && !isNative) {\n        filename = lineMatch[5];\n      }\n\n      return {\n        filename,\n        module: getModule ? getModule(filename) : undefined,\n        function: functionName,\n        lineno: parseInt(lineMatch[3], 10) || undefined,\n        colno: parseInt(lineMatch[4], 10) || undefined,\n        in_app: filenameIsInApp(filename, isNative),\n      };\n    }\n\n    if (line.match(FILENAME_MATCH)) {\n      return {\n        filename: line,\n      };\n    }\n\n    return undefined;\n  };\n}\n"], "names": [], "mappings": ";;AA2BA;AACA;AACA;AACO,SAAS,eAAe,CAAC,QAAQ,EAAU,QAAQ,GAAY,KAAK,EAAW;AACtF,EAAE,MAAM,UAAW;AACnB,IAAI,QAAS;AACb,KAAK,QAAS;AACd;AACA,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAE;AAChC;AACA,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAE;AACjC;AACA,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAE;AAChC;AACA,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC,CAAA;AAC1D;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,UAAW,IAAG,aAAa,SAAA,IAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAA;AACrF,CAAA;AACA;AACA;AACA;AACO,SAAS,IAAI,CAAC,SAAS,EAAmC;AACjE,EAAE,MAAM,cAAe,GAAE,cAAc,CAAA;AACvC,EAAE,MAAM,UAAW,GAAE,+DAA+D,CAAA;AACpF;AACA;AACA,EAAE,OAAO,CAAC,IAAI,KAAa;AAC3B,IAAI,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;AAC5C;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,MAAM,CAAA;AAChB,MAAM,IAAI,MAAM,CAAA;AAChB,MAAM,IAAI,YAAY,CAAA;AACtB,MAAM,IAAI,QAAQ,CAAA;AAClB,MAAM,IAAI,UAAU,CAAA;AACpB;AACA,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;AACxB,QAAQ,YAAa,GAAE,SAAS,CAAC,CAAC,CAAC,CAAA;AACnC;AACA,QAAQ,IAAI,cAAc,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;AACvD,QAAQ,IAAI,YAAY,CAAC,WAAA,GAAc,CAAC,CAAA,KAAM,GAAG,EAAE;AACnD,UAAU,WAAW,EAAE,CAAA;AACvB,SAAQ;AACR;AACA,QAAQ,IAAI,WAAY,GAAE,CAAC,EAAE;AAC7B,UAAU,MAAA,GAAS,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAA;AACrD,UAAU,MAAA,GAAS,YAAY,CAAC,KAAK,CAAC,WAAA,GAAc,CAAC,CAAC,CAAA;AACtD,UAAU,MAAM,YAAY,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;AACrD,UAAU,IAAI,SAAU,GAAE,CAAC,EAAE;AAC7B,YAAY,YAAA,GAAe,YAAY,CAAC,KAAK,CAAC,SAAA,GAAY,CAAC,CAAC,CAAA;AAC5D,YAAY,MAAA,GAAS,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAA;AAC/C,WAAU;AACV,SAAQ;AACR,QAAQ,QAAA,GAAW,SAAS,CAAA;AAC5B,OAAM;AACN;AACA,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,QAAA,GAAW,MAAM,CAAA;AACzB,QAAQ,UAAA,GAAa,MAAM,CAAA;AAC3B,OAAM;AACN;AACA,MAAM,IAAI,MAAO,KAAI,aAAa,EAAE;AACpC,QAAQ,UAAA,GAAa,SAAS,CAAA;AAC9B,QAAQ,YAAA,GAAe,SAAS,CAAA;AAChC,OAAM;AACN;AACA,MAAM,IAAI,YAAa,KAAI,SAAS,EAAE;AACtC,QAAQ,UAAW,GAAE,UAAW,IAAG,aAAa,CAAA;AAChD,QAAQ,YAAa,GAAE,QAAS,GAAE,CAAC,EAAA,QAAA,CAAA,CAAA,EAAA,UAAA,CAAA,CAAA,GAAA,UAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,IAAA,QAAA,GAAA,SAAA,CAAA,CAAA,CAAA,IAAA,SAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,MAAA,QAAA,GAAA,SAAA,CAAA,CAAA,CAAA,KAAA,QAAA,CAAA;AACA;AACA;AACA,MAAA,IAAA,QAAA,IAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA,EAAA;AACA,QAAA,QAAA,GAAA,QAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,IAAA,CAAA,QAAA,IAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,QAAA,EAAA;AACA,QAAA,QAAA,GAAA,SAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,OAAA;AACA,QAAA,QAAA;AACA,QAAA,MAAA,EAAA,SAAA,GAAA,SAAA,CAAA,QAAA,CAAA,GAAA,SAAA;AACA,QAAA,QAAA,EAAA,YAAA;AACA,QAAA,MAAA,EAAA,QAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,IAAA,SAAA;AACA,QAAA,KAAA,EAAA,QAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,IAAA,SAAA;AACA,QAAA,MAAA,EAAA,eAAA,CAAA,QAAA,EAAA,QAAA,CAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,IAAA,CAAA,KAAA,CAAA,cAAA,CAAA,EAAA;AACA,MAAA,OAAA;AACA,QAAA,QAAA,EAAA,IAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,OAAA,SAAA,CAAA;AACA,GAAA,CAAA;AACA;;;;;"}