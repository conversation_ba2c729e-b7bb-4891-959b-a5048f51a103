const { validateConfig, getConfig } = require('./config/cashfree');

console.log('🧪 Testing Cashfree Payment Gateway Setup...\n');

// Test 1: Environment Variables
console.log('1️⃣ Checking Environment Variables:');
const config = getConfig();
console.log(`   App ID: ${config.appId ? '✅ Set' : '❌ Missing'}`);
console.log(`   Secret Key: ${config.secretKey ? '✅ Set' : '❌ Missing'}`);
console.log(`   Environment: ${config.environment}`);
console.log(`   Base URL: ${config.baseUrl}`);
console.log(`   Return URL: ${config.returnUrl}`);
console.log(`   Notify URL: ${config.notifyUrl}\n`);

// Test 2: Configuration Validation
console.log('2️⃣ Validating Configuration:');
const isValid = validateConfig();
console.log(`   Configuration: ${isValid ? '✅ Valid' : '❌ Invalid'}\n`);

// Test 3: Dependencies Check
console.log('3️⃣ Checking Dependencies:');
try {
    require('express');
    console.log('   Express: ✅ Installed');
} catch (e) {
    console.log('   Express: ❌ Missing');
}

try {
    require('cashfree-pg');
    console.log('   Cashfree SDK: ✅ Installed');
} catch (e) {
    console.log('   Cashfree SDK: ❌ Missing');
}

try {
    require('dotenv');
    console.log('   Dotenv: ✅ Installed');
} catch (e) {
    console.log('   Dotenv: ❌ Missing');
}

try {
    require('uuid');
    console.log('   UUID: ✅ Installed');
} catch (e) {
    console.log('   UUID: ❌ Missing');
}

console.log('\n🎯 Setup Summary:');
if (isValid) {
    console.log('✅ Your Cashfree Payment Gateway is ready to use!');
    console.log('🚀 Run "npm start" to start the server');
    console.log('🌐 Open http://localhost:3000 in your browser');
} else {
    console.log('❌ Please fix the configuration issues above');
    console.log('📝 Check your .env file and ensure all required variables are set');
}

console.log('\n📚 Need help? Check the README.md file for detailed instructions.');
