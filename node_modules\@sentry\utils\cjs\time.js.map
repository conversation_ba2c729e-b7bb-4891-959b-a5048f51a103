{"version": 3, "file": "time.js", "sources": ["../../src/time.ts"], "sourcesContent": ["import { GLOBAL_OBJ } from './worldwide';\n\nconst ONE_SECOND_IN_MS = 1000;\n\n/**\n * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}\n * for accessing a high-resolution monotonic clock.\n */\ninterface Performance {\n  /**\n   * The millisecond timestamp at which measurement began, measured in Unix time.\n   */\n  timeOrigin: number;\n  /**\n   * Returns the current millisecond timestamp, where 0 represents the start of measurement.\n   */\n  now(): number;\n}\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using the Date API.\n *\n * TODO(v8): Return type should be rounded.\n */\nexport function dateTimestampInSeconds(): number {\n  return Date.now() / ONE_SECOND_IN_MS;\n}\n\n/**\n * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not\n * support the API.\n *\n * Wrapping the native API works around differences in behavior from different browsers.\n */\nfunction createUnixTimestampInSecondsFunc(): () => number {\n  const { performance } = GLOBAL_OBJ as typeof GLOBAL_OBJ & { performance?: Performance };\n  if (!performance || !performance.now) {\n    return dateTimestampInSeconds;\n  }\n\n  // Some browser and environments don't have a timeOrigin, so we fallback to\n  // using Date.now() to compute the starting time.\n  const approxStartingTimeOrigin = Date.now() - performance.now();\n  const timeOrigin = performance.timeOrigin == undefined ? approxStartingTimeOrigin : performance.timeOrigin;\n\n  // performance.now() is a monotonic clock, which means it starts at 0 when the process begins. To get the current\n  // wall clock time (actual UNIX timestamp), we need to add the starting time origin and the current time elapsed.\n  //\n  // TODO: This does not account for the case where the monotonic clock that powers performance.now() drifts from the\n  // wall clock time, which causes the returned timestamp to be inaccurate. We should investigate how to detect and\n  // correct for this.\n  // See: https://github.com/getsentry/sentry-javascript/issues/2590\n  // See: https://github.com/mdn/content/issues/4713\n  // See: https://dev.to/noamr/when-a-millisecond-is-not-a-millisecond-3h6\n  return () => {\n    return (timeOrigin + performance.now()) / ONE_SECOND_IN_MS;\n  };\n}\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the\n * availability of the Performance API.\n *\n * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is\n * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The\n * skew can grow to arbitrary amounts like days, weeks or months.\n * See https://github.com/getsentry/sentry-javascript/issues/2590.\n */\nexport const timestampInSeconds = createUnixTimestampInSecondsFunc();\n\n/**\n * Re-exported with an old name for backwards-compatibility.\n * TODO (v8): Remove this\n *\n * @deprecated Use `timestampInSeconds` instead.\n */\nexport const timestampWithMs = timestampInSeconds;\n\n/**\n * Internal helper to store what is the source of browserPerformanceTimeOrigin below. For debugging only.\n */\nexport let _browserPerformanceTimeOriginMode: string;\n\n/**\n * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the\n * performance API is available.\n */\nexport const browserPerformanceTimeOrigin = ((): number | undefined => {\n  // Unfortunately browsers may report an inaccurate time origin data, through either performance.timeOrigin or\n  // performance.timing.navigationStart, which results in poor results in performance data. We only treat time origin\n  // data as reliable if they are within a reasonable threshold of the current time.\n\n  const { performance } = GLOBAL_OBJ as typeof GLOBAL_OBJ & Window;\n  if (!performance || !performance.now) {\n    _browserPerformanceTimeOriginMode = 'none';\n    return undefined;\n  }\n\n  const threshold = 3600 * 1000;\n  const performanceNow = performance.now();\n  const dateNow = Date.now();\n\n  // if timeOrigin isn't available set delta to threshold so it isn't used\n  const timeOriginDelta = performance.timeOrigin\n    ? Math.abs(performance.timeOrigin + performanceNow - dateNow)\n    : threshold;\n  const timeOriginIsReliable = timeOriginDelta < threshold;\n\n  // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin\n  // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.\n  // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always\n  // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the\n  // Date API.\n  // eslint-disable-next-line deprecation/deprecation\n  const navigationStart = performance.timing && performance.timing.navigationStart;\n  const hasNavigationStart = typeof navigationStart === 'number';\n  // if navigationStart isn't available set delta to threshold so it isn't used\n  const navigationStartDelta = hasNavigationStart ? Math.abs(navigationStart + performanceNow - dateNow) : threshold;\n  const navigationStartIsReliable = navigationStartDelta < threshold;\n\n  if (timeOriginIsReliable || navigationStartIsReliable) {\n    // Use the more reliable time origin\n    if (timeOriginDelta <= navigationStartDelta) {\n      _browserPerformanceTimeOriginMode = 'timeOrigin';\n      return performance.timeOrigin;\n    } else {\n      _browserPerformanceTimeOriginMode = 'navigationStart';\n      return navigationStart;\n    }\n  }\n\n  // Either both timeOrigin and navigationStart are skewed or neither is available, fallback to Date.\n  _browserPerformanceTimeOriginMode = 'dateNow';\n  return dateNow;\n})();\n"], "names": ["GLOBAL_OBJ", "_browserPerformanceTimeOriginMode"], "mappings": ";;;;AAEA,MAAM,gBAAA,GAAmB,IAAI,CAAA;AAC7B;AACA;AACA;AACA;AACA;;AAYA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,GAAW;AACjD,EAAE,OAAO,IAAI,CAAC,GAAG,EAAC,GAAI,gBAAgB,CAAA;AACtC,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gCAAgC,GAAiB;AAC1D,EAAE,MAAM,EAAE,WAAY,EAAA,GAAIA,oBAAW,EAAA;AACrC,EAAE,IAAI,CAAC,WAAA,IAAe,CAAC,WAAW,CAAC,GAAG,EAAE;AACxC,IAAI,OAAO,sBAAsB,CAAA;AACjC,GAAE;AACF;AACA;AACA;AACA,EAAE,MAAM,wBAAA,GAA2B,IAAI,CAAC,GAAG,EAAC,GAAI,WAAW,CAAC,GAAG,EAAE,CAAA;AACjE,EAAE,MAAM,UAAA,GAAa,WAAW,CAAC,UAAA,IAAc,SAAA,GAAY,wBAAA,GAA2B,WAAW,CAAC,UAAU,CAAA;AAC5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,MAAM;AACf,IAAI,OAAO,CAAC,UAAA,GAAa,WAAW,CAAC,GAAG,EAAE,IAAI,gBAAgB,CAAA;AAC9D,GAAG,CAAA;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACa,MAAA,kBAAA,GAAqB,gCAAgC,GAAE;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,eAAgB,GAAE,mBAAkB;AACjD;AACA;AACA;AACA;AACWC,mDAAiC;AAC5C;AACA;AACA;AACA;AACA;AACa,MAAA,4BAAA,GAA+B,CAAC,MAA0B;AACvE;AACA;AACA;AACA;AACA,EAAE,MAAM,EAAE,WAAY,EAAA,GAAID,oBAAW,EAAA;AACrC,EAAE,IAAI,CAAC,WAAA,IAAe,CAAC,WAAW,CAAC,GAAG,EAAE;AACxC,IAAIC,yCAAA,GAAoC,MAAM,CAAA;AAC9C,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,SAAA,GAAY,IAAA,GAAO,IAAI,CAAA;AAC/B,EAAE,MAAM,cAAe,GAAE,WAAW,CAAC,GAAG,EAAE,CAAA;AAC1C,EAAE,MAAM,OAAQ,GAAE,IAAI,CAAC,GAAG,EAAE,CAAA;AAC5B;AACA;AACA,EAAE,MAAM,eAAA,GAAkB,WAAW,CAAC,UAAA;AACtC,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,UAAW,GAAE,cAAe,GAAE,OAAO,CAAA;AAChE,MAAM,SAAS,CAAA;AACf,EAAE,MAAM,oBAAA,GAAuB,eAAA,GAAkB,SAAS,CAAA;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,eAAgB,GAAE,WAAW,CAAC,MAAO,IAAG,WAAW,CAAC,MAAM,CAAC,eAAe,CAAA;AAClF,EAAE,MAAM,kBAAmB,GAAE,OAAO,eAAA,KAAoB,QAAQ,CAAA;AAChE;AACA,EAAE,MAAM,oBAAqB,GAAE,kBAAmB,GAAE,IAAI,CAAC,GAAG,CAAC,eAAA,GAAkB,cAAe,GAAE,OAAO,CAAA,GAAI,SAAS,CAAA;AACpH,EAAE,MAAM,yBAAA,GAA4B,oBAAA,GAAuB,SAAS,CAAA;AACpE;AACA,EAAE,IAAI,oBAAqB,IAAG,yBAAyB,EAAE;AACzD;AACA,IAAI,IAAI,eAAgB,IAAG,oBAAoB,EAAE;AACjD,MAAMA,yCAAA,GAAoC,YAAY,CAAA;AACtD,MAAM,OAAO,WAAW,CAAC,UAAU,CAAA;AACnC,WAAW;AACX,MAAMA,yCAAA,GAAoC,iBAAiB,CAAA;AAC3D,MAAM,OAAO,eAAe,CAAA;AAC5B,KAAI;AACJ,GAAE;AACF;AACA;AACA,EAAEA,yCAAA,GAAoC,SAAS,CAAA;AAC/C,EAAE,OAAO,OAAO,CAAA;AAChB,CAAC;;;;;;;"}