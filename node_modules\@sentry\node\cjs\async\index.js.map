{"version": 3, "file": "index.js", "sources": ["../../../src/async/index.ts"], "sourcesContent": ["import { NODE_VERSION } from '../nodeVersion';\nimport { setDomainAsyncContextStrategy } from './domain';\nimport { setHooksAsyncContextStrategy } from './hooks';\n\n/**\n * Sets the correct async context strategy for Node.js\n *\n * Node.js >= 14 uses AsyncLocalStorage\n * Node.js < 14 uses domains\n */\nexport function setNodeAsyncContextStrategy(): void {\n  if (NODE_VERSION.major >= 14) {\n    setHooksAsyncContextStrategy();\n  } else {\n    setDomainAsyncContextStrategy();\n  }\n}\n"], "names": ["NODE_VERSION", "setHooksAsyncContextStrategy", "setDomainAsyncContextStrategy"], "mappings": ";;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B,GAAS;AACpD,EAAE,IAAIA,wBAAY,CAAC,KAAM,IAAG,EAAE,EAAE;AAChC,IAAIC,kCAA4B,EAAE,CAAA;AAClC,SAAS;AACT,IAAIC,oCAA6B,EAAE,CAAA;AACnC,GAAE;AACF;;;;"}