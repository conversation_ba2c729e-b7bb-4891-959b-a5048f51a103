{"version": 3, "file": "envelope.d.ts", "sourceRoot": "", "sources": ["../../src/envelope.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,UAAU,EACV,cAAc,EAGd,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,gBAAgB,EAChB,KAAK,EACL,oBAAoB,EACpB,OAAO,EACP,WAAW,EACX,mBAAmB,EACpB,MAAM,eAAe,CAAC;AAMvB;;;;GAIG;AACH,wBAAgB,cAAc,CAAC,CAAC,SAAS,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,GAAE,CAAC,CAAC,CAAC,CAAM,GAAG,CAAC,CAErF;AAED;;;;GAIG;AACH,wBAAgB,iBAAiB,CAAC,CAAC,SAAS,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAG3F;AAED;;;;;GAKG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,SAAS,QAAQ,EACpD,QAAQ,EAAE,QAAQ,EAClB,QAAQ,EAAE,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,OAAO,GAAG,IAAI,GAClG,OAAO,CAaT;AAED;;GAEG;AACH,wBAAgB,wBAAwB,CAAC,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAE/F;AAUD;;GAEG;AACH,wBAAgB,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,CAAC,EAAE,mBAAmB,GAAG,MAAM,GAAG,UAAU,CAoC5G;AAeD,MAAM,WAAW,mBAAmB;IAClC,MAAM,CAAC,KAAK,CAAC,EAAE,UAAU,GAAG,MAAM,CAAC;CACpC;AAED;;GAEG;AACH,wBAAgB,aAAa,CAC3B,GAAG,EAAE,MAAM,GAAG,UAAU,EACxB,WAAW,EAAE,mBAAmB,EAChC,WAAW,EAAE,mBAAmB,GAC/B,QAAQ,CAgCV;AAED;;GAEG;AACH,wBAAgB,4BAA4B,CAC1C,UAAU,EAAE,UAAU,EACtB,WAAW,CAAC,EAAE,mBAAmB,GAChC,cAAc,CAahB;AAmBD;;GAEG;AACH,wBAAgB,8BAA8B,CAAC,IAAI,EAAE,gBAAgB,GAAG,YAAY,CAEnF;AAED,mEAAmE;AACnE,wBAAgB,+BAA+B,CAAC,eAAe,CAAC,EAAE,WAAW,GAAG,KAAK,GAAG,OAAO,GAAG,SAAS,CAM1G;AAED;;;GAGG;AACH,wBAAgB,0BAA0B,CACxC,KAAK,EAAE,KAAK,EACZ,OAAO,EAAE,OAAO,GAAG,SAAS,EAC5B,MAAM,EAAE,MAAM,GAAG,SAAS,EAC1B,GAAG,CAAC,EAAE,aAAa,GAClB,oBAAoB,CAWtB"}