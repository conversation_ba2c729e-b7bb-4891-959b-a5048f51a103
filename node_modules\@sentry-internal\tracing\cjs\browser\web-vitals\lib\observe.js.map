{"version": 3, "file": "observe.js", "sources": ["../../../../../src/browser/web-vitals/lib/observe.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { FirstInputPolyfillEntry, NavigationTimingPolyfillEntry, PerformancePaintTiming } from '../types';\n\nexport interface PerformanceEntryHandler {\n  (entry: PerformanceEntry): void;\n}\n\ninterface PerformanceEntryMap {\n  event: PerformanceEventTiming[];\n  paint: PerformancePaintTiming[];\n  'layout-shift': LayoutShift[];\n  'largest-contentful-paint': LargestContentfulPaint[];\n  'first-input': PerformanceEventTiming[] | FirstInputPolyfillEntry[];\n  navigation: PerformanceNavigationTiming[] | NavigationTimingPolyfillEntry[];\n  resource: PerformanceResourceTiming[];\n  longtask: PerformanceEntry[];\n}\n\n/**\n * Takes a performance entry type and a callback function, and creates a\n * `PerformanceObserver` instance that will observe the specified entry type\n * with buffering enabled and call the callback _for each entry_.\n *\n * This function also feature-detects entry support and wraps the logic in a\n * try/catch to avoid errors in unsupporting browsers.\n */\nexport const observe = <K extends keyof PerformanceEntryMap>(\n  type: K,\n  callback: (entries: PerformanceEntryMap[K]) => void,\n  opts?: PerformanceObserverInit,\n): PerformanceObserver | undefined => {\n  try {\n    if (PerformanceObserver.supportedEntryTypes.includes(type)) {\n      const po = new PerformanceObserver(list => {\n        callback(list.getEntries() as PerformanceEntryMap[K]);\n      });\n      po.observe({\n        type,\n        buffered: true,\n        ...opts,\n      } as PerformanceObserverInit);\n      return po;\n    }\n  } catch (e) {\n    // Do nothing.\n  }\n  return;\n};\n"], "names": [], "mappings": ";;AAiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,UAAU;AACvB,EAAE,IAAI;AACN,EAAE,QAAQ;AACV,EAAE,IAAI;AACN,KAAsC;AACtC,EAAE,IAAI;AACN,IAAI,IAAI,mBAAmB,CAAC,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAChE,MAAM,MAAM,EAAG,GAAE,IAAI,mBAAmB,CAAC,QAAQ;AACjD,QAAQ,QAAQ,CAAC,IAAI,CAAC,UAAU,IAA6B,CAAA;AAC7D,OAAO,CAAC,CAAA;AACR,MAAM,EAAE,CAAC,OAAO,CAAC;AACjB,QAAQ,IAAI;AACZ,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,GAAG,IAAI;AACf,SAAmC,CAAA;AACnC,MAAM,OAAO,EAAE,CAAA;AACf,KAAI;AACJ,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA,GAAE;AACF,EAAE,OAAM;AACR;;;;"}