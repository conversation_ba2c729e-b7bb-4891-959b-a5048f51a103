{"version": 3, "file": "types.js", "sources": ["../../../src/browser/types.ts"], "sourcesContent": ["import { G<PERSON><PERSON><PERSON><PERSON>_OBJ } from '@sentry/utils';\n\nexport const WINDOW = GLOBAL_OBJ as typeof GLOBAL_OBJ &\n  // document is not available in all browser environments (webworkers). We make it optional so you have to explicitly check for it\n  Omit<Window, 'document'> &\n  Partial<Pick<Window, 'document'>>;\n"], "names": [], "mappings": ";;AAEO,MAAM,MAAO,GAAE,UAAW;;;;;;"}