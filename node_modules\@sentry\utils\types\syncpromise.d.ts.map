{"version": 3, "file": "syncpromise.d.ts", "sourceRoot": "", "sources": ["../../src/syncpromise.ts"], "names": [], "mappings": "AAiBA,wBAAgB,mBAAmB,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACzD,wBAAgB,mBAAmB,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAclF;;;;;GAKG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,GAAG,KAAK,EAAE,MAAM,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAI3E;AAED;;;GAGG;AACH,cAAM,WAAW,CAAC,CAAC,CAAE,YAAW,WAAW,CAAC,CAAC,CAAC;IAC5C,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,SAAS,CAA6D;IAC9E,OAAO,CAAC,MAAM,CAAM;gBAGlB,QAAQ,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,IAAI,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,IAAI,KAAK,IAAI;IAY1G,YAAY;IACL,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,EACxC,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,EACrE,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,GACtE,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAiCnC,YAAY;IACL,KAAK,CAAC,OAAO,GAAG,KAAK,EAC1B,UAAU,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,KAAK,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,GACpE,WAAW,CAAC,CAAC,GAAG,OAAO,CAAC;IAI3B,YAAY;IACL,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC;IA+B9E,YAAY;IACZ,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAEvB;IAEF,YAAY;IACZ,OAAO,CAAC,QAAQ,CAAC,OAAO,CAEtB;IAEF,YAAY;IACZ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAczB;IAEF,YAAY;IACZ,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAwB/B;CACH;AAED,OAAO,EAAE,WAAW,EAAE,CAAC"}