{"version": 3, "file": "lazy.js", "sources": ["../../../../src/node/integrations/lazy.ts"], "sourcesContent": ["import type { Integration, IntegrationClass } from '@sentry/types';\nimport { dynamicRequire } from '@sentry/utils';\n\nexport interface LazyLoadedIntegration<T = object> extends Integration {\n  /**\n   * Loads the integration's dependency and caches it so it doesn't have to be loaded again.\n   *\n   * If this returns undefined, the dependency could not be loaded.\n   */\n  loadDependency(): T | undefined;\n}\n\nexport const lazyLoadedNodePerformanceMonitoringIntegrations: (() => LazyLoadedIntegration)[] = [\n  () => {\n    const integration = dynamicRequire(module, './apollo') as {\n      Apollo: IntegrationClass<LazyLoadedIntegration>;\n    };\n    return new integration.Apollo();\n  },\n  () => {\n    const integration = dynamicRequire(module, './apollo') as {\n      Apollo: IntegrationClass<LazyLoadedIntegration>;\n    };\n    return new integration.Apollo({ useNestjs: true });\n  },\n  () => {\n    const integration = dynamicRequire(module, './graphql') as {\n      GraphQL: IntegrationClass<LazyLoadedIntegration>;\n    };\n    return new integration.GraphQL();\n  },\n  () => {\n    const integration = dynamicRequire(module, './mongo') as {\n      Mongo: IntegrationClass<LazyLoadedIntegration>;\n    };\n    return new integration.Mongo();\n  },\n  () => {\n    const integration = dynamicRequire(module, './mongo') as {\n      Mongo: IntegrationClass<LazyLoadedIntegration>;\n    };\n    return new integration.Mongo({ mongoose: true });\n  },\n  () => {\n    const integration = dynamicRequire(module, './mysql') as {\n      Mysql: IntegrationClass<LazyLoadedIntegration>;\n    };\n    return new integration.Mysql();\n  },\n  () => {\n    const integration = dynamicRequire(module, './postgres') as {\n      Postgres: IntegrationClass<LazyLoadedIntegration>;\n    };\n    return new integration.Postgres();\n  },\n];\n"], "names": ["dynamicRequire"], "mappings": ";;;;AAYO,MAAM,+CAA+C,GAAoC;AAChG,EAAE,MAAM;AACR,IAAI,MAAM,cAAcA,oBAAc,CAAC,MAAM,EAAE,UAAU,CAAE;;AAEvD,CAAA;AACJ,IAAI,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,CAAA;AACnC,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM,cAAcA,oBAAc,CAAC,MAAM,EAAE,UAAU,CAAE;;AAEvD,CAAA;AACJ,IAAI,OAAO,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,IAAK,EAAC,CAAC,CAAA;AACtD,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM,cAAcA,oBAAc,CAAC,MAAM,EAAE,WAAW,CAAE;;AAExD,CAAA;AACJ,IAAI,OAAO,IAAI,WAAW,CAAC,OAAO,EAAE,CAAA;AACpC,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM,cAAcA,oBAAc,CAAC,MAAM,EAAE,SAAS,CAAE;;AAEtD,CAAA;AACJ,IAAI,OAAO,IAAI,WAAW,CAAC,KAAK,EAAE,CAAA;AAClC,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM,cAAcA,oBAAc,CAAC,MAAM,EAAE,SAAS,CAAE;;AAEtD,CAAA;AACJ,IAAI,OAAO,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAK,EAAC,CAAC,CAAA;AACpD,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM,cAAcA,oBAAc,CAAC,MAAM,EAAE,SAAS,CAAE;;AAEtD,CAAA;AACJ,IAAI,OAAO,IAAI,WAAW,CAAC,KAAK,EAAE,CAAA;AAClC,GAAG;AACH,EAAE,MAAM;AACR,IAAI,MAAM,cAAcA,oBAAc,CAAC,MAAM,EAAE,YAAY,CAAE;;AAEzD,CAAA;AACJ,IAAI,OAAO,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAA;AACrC,GAAG;AACH;;;;"}