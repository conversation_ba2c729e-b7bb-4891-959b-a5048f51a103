{"version": 3, "file": "node-schedule.js", "sources": ["../../../src/cron/node-schedule.ts"], "sourcesContent": ["import { with<PERSON>oni<PERSON> } from '@sentry/core';\nimport { replaceCronNames } from './common';\n\nexport interface NodeSchedule {\n  scheduleJob(\n    nameOrExpression: string | Date | object,\n    expressionOrCallback: string | Date | object | (() => void),\n    callback?: () => void,\n  ): unknown;\n}\n\n/**\n * Instruments the `node-schedule` library to send a check-in event to <PERSON>try for each job execution.\n *\n * ```ts\n * import * as Sentry from '@sentry/node';\n * import * as schedule from 'node-schedule';\n *\n * const scheduleWithCheckIn = Sentry.cron.instrumentNodeSchedule(schedule);\n *\n * const job = scheduleWithCheckIn.scheduleJob('my-cron-job', '* * * * *', () => {\n *  console.log('You will see this message every minute');\n * });\n * ```\n */\nexport function instrumentNodeSchedule<T>(lib: T & NodeSchedule): T {\n  return new Proxy(lib, {\n    get(target, prop: keyof NodeSchedule) {\n      if (prop === 'scheduleJob') {\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        return new Proxy(target.scheduleJob, {\n          apply(target, thisArg, argArray: Parameters<NodeSchedule['scheduleJob']>) {\n            const [nameOrExpression, expressionOrCallback] = argArray;\n\n            if (typeof nameOrExpression !== 'string' || typeof expressionOrCallback !== 'string') {\n              throw new Error(\n                \"Automatic instrumentation of 'node-schedule' requires the first parameter of 'scheduleJob' to be a job name string and the second parameter to be a crontab string\",\n              );\n            }\n\n            const monitorSlug = nameOrExpression;\n            const expression = expressionOrCallback;\n\n            return withMonitor(\n              monitorSlug,\n              () => {\n                return target.apply(thisArg, argArray);\n              },\n              {\n                schedule: { type: 'crontab', value: replaceCronNames(expression) },\n              },\n            );\n          },\n        });\n      }\n\n      return target[prop];\n    },\n  });\n}\n"], "names": ["withMonitor", "replaceCronNames"], "mappings": ";;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAI,GAAG,EAAuB;AACpE,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE;AACxB,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI,EAAsB;AAC1C,MAAM,IAAI,IAAK,KAAI,aAAa,EAAE;AAClC;AACA,QAAQ,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE;AAC7C,UAAU,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAA2C;AACpF,YAAY,MAAM,CAAC,gBAAgB,EAAE,oBAAoB,CAAA,GAAI,QAAQ,CAAA;AACrE;AACA,YAAY,IAAI,OAAO,gBAAiB,KAAI,QAAS,IAAG,OAAO,oBAAA,KAAyB,QAAQ,EAAE;AAClG,cAAc,MAAM,IAAI,KAAK;AAC7B,gBAAgB,oKAAoK;AACpL,eAAe,CAAA;AACf,aAAY;AACZ;AACA,YAAY,MAAM,WAAY,GAAE,gBAAgB,CAAA;AAChD,YAAY,MAAM,UAAW,GAAE,oBAAoB,CAAA;AACnD;AACA,YAAY,OAAOA,gBAAW;AAC9B,cAAc,WAAW;AACzB,cAAc,MAAM;AACpB,gBAAgB,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;AACtD,eAAe;AACf,cAAc;AACd,gBAAgB,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAEC,uBAAgB,CAAC,UAAU,GAAG;AAClF,eAAe;AACf,aAAa,CAAA;AACb,WAAW;AACX,SAAS,CAAC,CAAA;AACV,OAAM;AACN;AACA,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA;AACzB,KAAK;AACL,GAAG,CAAC,CAAA;AACJ;;;;"}