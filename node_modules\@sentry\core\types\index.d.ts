export type { ClientClass } from './sdk';
export type { AsyncContextStrategy, Carrier, Layer, RunWithAsyncContextOptions } from './hub';
export type { OfflineStore, OfflineTransportOptions } from './transports/offline';
export type { ServerRuntimeClientOptions } from './server-runtime-client';
export type { RequestDataIntegrationOptions } from './integrations/requestdata';
export type { IntegrationIndex } from './integration';
export * from './tracing';
export * from './semanticAttributes';
export { createEventEnvelope, createSessionEnvelope } from './envelope';
export { addBreadcrumb, captureCheckIn, withMonitor, captureException, captureEvent, captureMessage, close, configureScope, flush, lastEventId, startTransaction, setContext, setExtra, setExtras, setTag, setTags, setUser, withScope, withIsolationScope, getClient, isInitialized, getCurrentScope, startSession, endSession, captureSession, withActiveSpan, } from './exports';
export { getCurrentHub, getIsolationScope, getHubFromCarrier, Hub, makeMain, getMainCarrier, runWithAsyncContext, setHubOnCarrier, ensureHubOnCarrier, setAsyncContextStrategy, } from './hub';
export { makeSession, closeSession, updateSession } from './session';
export { SessionFlusher } from './sessionflusher';
export { Scope, getGlobalScope, setGlobalScope } from './scope';
export { notifyEventProcessors, addGlobalEventProcessor, } from './eventProcessors';
export { getEnvelopeEndpointWithUrlEncodedAuth, getReportDialogEndpoint } from './api';
export { BaseClient, addEventProcessor } from './baseclient';
export { ServerRuntimeClient } from './server-runtime-client';
export { initAndBind, setCurrentClient } from './sdk';
export { createTransport } from './transports/base';
export { makeOfflineTransport } from './transports/offline';
export { makeMultiplexedTransport } from './transports/multiplexed';
export { SDK_VERSION } from './version';
export { getIntegrationsToSetup, addIntegration, defineIntegration, convertIntegrationFnToClass, } from './integration';
export { applyScopeDataToEvent, mergeScopeData } from './utils/applyScopeDataToEvent';
export { prepareEvent } from './utils/prepareEvent';
export { createCheckInEnvelope } from './checkin';
export { createSpanEnvelope } from './span';
export { hasTracingEnabled } from './utils/hasTracingEnabled';
export { isSentryRequestUrl } from './utils/isSentryRequestUrl';
export { handleCallbackErrors } from './utils/handleCallbackErrors';
export { parameterize } from './utils/parameterize';
export { spanToTraceHeader, spanToJSON, spanIsSampled, spanToTraceContext } from './utils/spanUtils';
export { getRootSpan } from './utils/getRootSpan';
export { applySdkMetadata } from './utils/sdkMetadata';
export { DEFAULT_ENVIRONMENT } from './constants';
export { ModuleMetadata } from './integrations/metadata';
export { RequestData } from './integrations/requestdata';
export { InboundFilters } from './integrations/inboundfilters';
export { FunctionToString } from './integrations/functiontostring';
export { LinkedErrors } from './integrations/linkederrors';
import * as INTEGRATIONS from './integrations';
export { functionToStringIntegration } from './integrations/functiontostring';
export { inboundFiltersIntegration } from './integrations/inboundfilters';
export { linkedErrorsIntegration } from './integrations/linkederrors';
export { moduleMetadataIntegration } from './integrations/metadata';
export { requestDataIntegration } from './integrations/requestdata';
export { metrics } from './metrics/exports';
/** @deprecated Import the integration function directly, e.g. `inboundFiltersIntegration()` instead of `new Integrations.InboundFilter(). */
declare const Integrations: typeof INTEGRATIONS;
export { Integrations };
//# sourceMappingURL=index.d.ts.map