{"version": 3, "file": "globalUnhandledRejection.js", "sources": ["../../../src/instrument/globalUnhandledRejection.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { HandlerDataUnhandledRejection } from '@sentry/types';\n\nimport { GLOBAL_OBJ } from '../worldwide';\nimport { addHandler, maybeInstrument, triggerHandlers } from './_handlers';\n\nlet _oldOnUnhandledRejectionHandler: (typeof GLOBAL_OBJ)['onunhandledrejection'] | null = null;\n\n/**\n * Add an instrumentation handler for when an unhandled promise rejection is captured.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addGlobalUnhandledRejectionInstrumentationHandler(\n  handler: (data: HandlerDataUnhandledRejection) => void,\n): void {\n  const type = 'unhandledrejection';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentUnhandledRejection);\n}\n\nfunction instrumentUnhandledRejection(): void {\n  _oldOnUnhandledRejectionHandler = GLOBAL_OBJ.onunhandledrejection;\n\n  GLOBAL_OBJ.onunhandledrejection = function (e: any): boolean {\n    const handlerData: HandlerDataUnhandledRejection = e;\n    triggerHandlers('unhandledrejection', handlerData);\n\n    if (_oldOnUnhandledRejectionHandler && !_oldOnUnhandledRejectionHandler.__SENTRY_LOADER__) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnUnhandledRejectionHandler.apply(this, arguments);\n    }\n\n    return true;\n  };\n\n  GLOBAL_OBJ.onunhandledrejection.__SENTRY_INSTRUMENTED__ = true;\n}\n"], "names": ["add<PERSON><PERSON><PERSON>", "maybeInstrument", "GLOBAL_OBJ", "triggerHandlers"], "mappings": ";;;;;AAOA,IAAI,+BAA+B,GAAuD,IAAI,CAAA;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iDAAiD;AACjE,EAAE,OAAO;AACT,EAAQ;AACR,EAAE,MAAM,IAAK,GAAE,oBAAoB,CAAA;AACnC,EAAEA,oBAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAC3B,EAAEC,yBAAe,CAAC,IAAI,EAAE,4BAA4B,CAAC,CAAA;AACrD,CAAA;AACA;AACA,SAAS,4BAA4B,GAAS;AAC9C,EAAE,+BAAgC,GAAEC,oBAAU,CAAC,oBAAoB,CAAA;AACnE;AACA,EAAEA,oBAAU,CAAC,oBAAqB,GAAE,UAAU,CAAC,EAAgB;AAC/D,IAAI,MAAM,WAAW,GAAkC,CAAC,CAAA;AACxD,IAAIC,yBAAe,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAA;AACtD;AACA,IAAI,IAAI,+BAAgC,IAAG,CAAC,+BAA+B,CAAC,iBAAiB,EAAE;AAC/F;AACA,MAAM,OAAO,+BAA+B,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;AACnE,KAAI;AACJ;AACA,IAAI,OAAO,IAAI,CAAA;AACf,GAAG,CAAA;AACH;AACA,EAAED,oBAAU,CAAC,oBAAoB,CAAC,uBAAA,GAA0B,IAAI,CAAA;AAChE;;;;"}