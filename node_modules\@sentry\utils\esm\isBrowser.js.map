{"version": 3, "file": "isBrowser.js", "sources": ["../../src/isBrowser.ts"], "sourcesContent": ["import { isNodeEnv } from './node';\nimport { GLOBAL_OBJ } from './worldwide';\n\n/**\n * Returns true if we are in the browser.\n */\nexport function isBrowser(): boolean {\n  // eslint-disable-next-line no-restricted-globals\n  return typeof window !== 'undefined' && (!isNodeEnv() || isElectronNodeRenderer());\n}\n\ntype ElectronProcess = { type?: string };\n\n// Electron renderers with nodeIntegration enabled are detected as Node.js so we specifically test for them\nfunction isElectronNodeRenderer(): boolean {\n  return (\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any\n    (GLOBAL_OBJ as any).process !== undefined && ((GLOBAL_OBJ as any).process as ElectronProcess).type === 'renderer'\n  );\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;AACA;AACO,SAAS,SAAS,GAAY;AACrC;AACA,EAAE,OAAO,OAAO,MAAO,KAAI,gBAAgB,CAAC,SAAS,EAAG,IAAG,sBAAsB,EAAE,CAAC,CAAA;AACpF,CAAA;;AAIA;AACA,SAAS,sBAAsB,GAAY;AAC3C,EAAE;AACF;AACA,IAAI,CAAC,UAAW,GAAQ,OAAA,KAAY,SAAU,IAAG,CAAC,CAAC,aAAmB,UAA4B,SAAS,UAAA;AAC3G,IAAG;AACH;;;;"}