import { <PERSON><PERSON><PERSON><PERSON><PERSON>, StackParser } from '@sentry/types';
import { Debugger } from 'inspector';
export type Variables = Record<string, unknown>;
export type RateLimitIncrement = () => void;
/**
 * Creates a rate limiter that will call the disable callback when the rate limit is reached and the enable callback
 * when a timeout has occurred.
 * @param maxPerSecond Maximum number of calls per second
 * @param enable Callback to enable capture
 * @param disable Callback to disable capture
 * @returns A function to call to increment the rate limiter count
 */
export declare function createRateLimiter(maxPerSecond: number, enable: () => void, disable: (seconds: number) => void): RateLimitIncrement;
export type PausedExceptionEvent = Debugger.PausedEventDataType & {
    data: {
        description: string;
    };
};
/** Could this be an anonymous function? */
export declare function isAnonymous(name: string | undefined): boolean;
/** Do the function names appear to match? */
export declare function functionNamesMatch(a: string | undefined, b: string | undefined): boolean;
/** Creates a unique hash from stack frames */
export declare function hashFrames(frames: StackFrame[] | undefined): string | undefined;
/**
 * We use the stack parser to create a unique hash from the exception stack trace
 * This is used to lookup vars when the exception passes through the event processor
 */
export declare function hashFromStack(stackParser: StackParser, stack: string | undefined): string | undefined;
export interface FrameVariables {
    function: string;
    vars?: Variables;
}
export interface LocalVariablesIntegrationOptions {
    /**
     * Capture local variables for both caught and uncaught exceptions
     *
     * - When false, only uncaught exceptions will have local variables
     * - When true, both caught and uncaught exceptions will have local variables.
     *
     * Defaults to `true`.
     *
     * Capturing local variables for all exceptions can be expensive since the debugger pauses for every throw to collect
     * local variables.
     *
     * To reduce the likelihood of this feature impacting app performance or throughput, this feature is rate-limited.
     * Once the rate limit is reached, local variables will only be captured for uncaught exceptions until a timeout has
     * been reached.
     */
    captureAllExceptions?: boolean;
    /**
     * Maximum number of exceptions to capture local variables for per second before rate limiting is triggered.
     */
    maxExceptionsPerSecond?: number;
}
//# sourceMappingURL=common.d.ts.map
