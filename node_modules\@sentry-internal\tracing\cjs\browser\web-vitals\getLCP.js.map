{"version": 3, "file": "getLCP.js", "sources": ["../../../../src/browser/web-vitals/getLCP.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../types';\nimport { bindReporter } from './lib/bindReporter';\nimport { getActivationStart } from './lib/getActivationStart';\nimport { getVisibilityWatcher } from './lib/getVisibilityWatcher';\nimport { initMetric } from './lib/initMetric';\nimport { observe } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport type { LCPMetric, ReportCallback, StopListening } from './types';\n\nconst reportedMetricIDs: Record<string, boolean> = {};\n\n/**\n * Calculates the [LCP](https://web.dev/lcp/) value for the current page and\n * calls the `callback` function once the value is ready (along with the\n * relevant `largest-contentful-paint` performance entry used to determine the\n * value). The reported value is a `DOMHighResTimeStamp`.\n */\nexport const onLCP = (onReport: ReportCallback): StopListening | undefined => {\n  const visibilityWatcher = getVisibilityWatcher();\n  const metric = initMetric('LCP');\n  let report: ReturnType<typeof bindReporter>;\n\n  const handleEntries = (entries: LCPMetric['entries']): void => {\n    const lastEntry = entries[entries.length - 1] as LargestContentfulPaint;\n    if (lastEntry) {\n      // The startTime attribute returns the value of the renderTime if it is\n      // not 0, and the value of the loadTime otherwise. The activationStart\n      // reference is used because LCP should be relative to page activation\n      // rather than navigation start if the page was prerendered.\n      const value = Math.max(lastEntry.startTime - getActivationStart(), 0);\n\n      // Only report if the page wasn't hidden prior to LCP.\n      if (value < visibilityWatcher.firstHiddenTime) {\n        metric.value = value;\n        metric.entries = [lastEntry];\n        report();\n      }\n    }\n  };\n\n  const po = observe('largest-contentful-paint', handleEntries);\n\n  if (po) {\n    report = bindReporter(onReport, metric);\n\n    const stopListening = (): void => {\n      if (!reportedMetricIDs[metric.id]) {\n        handleEntries(po.takeRecords() as LCPMetric['entries']);\n        po.disconnect();\n        reportedMetricIDs[metric.id] = true;\n        report(true);\n      }\n    };\n\n    // Stop listening after input. Note: while scrolling is an input that\n    // stop LCP observation, it's unreliable since it can be programmatically\n    // generated. See: https://github.com/GoogleChrome/web-vitals/issues/75\n    ['keydown', 'click'].forEach(type => {\n      if (WINDOW.document) {\n        addEventListener(type, stopListening, { once: true, capture: true });\n      }\n    });\n\n    onHidden(stopListening, true);\n\n    return stopListening;\n  }\n\n  return;\n};\n"], "names": ["getVisibilityWatcher", "initMetric", "getActivationStart", "observe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WINDOW", "onHidden"], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAWA,MAAM,iBAAiB,GAA4B,EAAE,CAAA;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACa,MAAA,KAAA,GAAQ,CAAC,QAAQ,KAAgD;AAC9E,EAAE,MAAM,iBAAA,GAAoBA,yCAAoB,EAAE,CAAA;AAClD,EAAE,MAAM,MAAO,GAAEC,qBAAU,CAAC,KAAK,CAAC,CAAA;AAClC,EAAE,IAAI,MAAM,CAAA;AACZ;AACA,EAAE,MAAM,aAAA,GAAgB,CAAC,OAAO,KAAiC;AACjE,IAAI,MAAM,SAAU,GAAE,OAAO,CAAC,OAAO,CAAC,MAAA,GAAS,CAAC,CAAE,EAAA;AAClD,IAAI,IAAI,SAAS,EAAE;AACnB;AACA;AACA;AACA;AACA,MAAM,MAAM,KAAM,GAAE,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,SAAA,GAAYC,qCAAkB,EAAE,EAAE,CAAC,CAAC,CAAA;AAC3E;AACA;AACA,MAAM,IAAI,KAAA,GAAQ,iBAAiB,CAAC,eAAe,EAAE;AACrD,QAAQ,MAAM,CAAC,KAAM,GAAE,KAAK,CAAA;AAC5B,QAAQ,MAAM,CAAC,OAAA,GAAU,CAAC,SAAS,CAAC,CAAA;AACpC,QAAQ,MAAM,EAAE,CAAA;AAChB,OAAM;AACN,KAAI;AACJ,GAAG,CAAA;AACH;AACA,EAAE,MAAM,KAAKC,eAAO,CAAC,0BAA0B,EAAE,aAAa,CAAC,CAAA;AAC/D;AACA,EAAE,IAAI,EAAE,EAAE;AACV,IAAI,SAASC,yBAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;AAC3C;AACA,IAAI,MAAM,aAAA,GAAgB,MAAY;AACtC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;AACzC,QAAQ,aAAa,CAAC,EAAE,CAAC,WAAW,IAA2B,CAAA;AAC/D,QAAQ,EAAE,CAAC,UAAU,EAAE,CAAA;AACvB,QAAQ,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAA,GAAI,IAAI,CAAA;AAC3C,QAAQ,MAAM,CAAC,IAAI,CAAC,CAAA;AACpB,OAAM;AACN,KAAK,CAAA;AACL;AACA;AACA;AACA;AACA,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,IAAA,IAAQ;AACzC,MAAM,IAAIC,YAAM,CAAC,QAAQ,EAAE;AAC3B,QAAQ,gBAAgB,CAAC,IAAI,EAAE,aAAa,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAA,EAAM,CAAC,CAAA;AAC5E,OAAM;AACN,KAAK,CAAC,CAAA;AACN;AACA,IAAIC,iBAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;AACjC;AACA,IAAI,OAAO,aAAa,CAAA;AACxB,GAAE;AACF;AACA,EAAE,OAAM;AACR;;;;"}