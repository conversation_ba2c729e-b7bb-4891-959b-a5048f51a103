{"version": 3, "file": "index.js", "sources": ["../../../src/async/index.ts"], "sourcesContent": ["import { NODE_VERSION } from '../nodeVersion';\nimport { setDomainAsyncContextStrategy } from './domain';\nimport { setHooksAsyncContextStrategy } from './hooks';\n\n/**\n * Sets the correct async context strategy for Node.js\n *\n * Node.js >= 14 uses AsyncLocalStorage\n * Node.js < 14 uses domains\n */\nexport function setNodeAsyncContextStrategy(): void {\n  if (NODE_VERSION.major >= 14) {\n    setHooksAsyncContextStrategy();\n  } else {\n    setDomainAsyncContextStrategy();\n  }\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B,GAAS;AACpD,EAAE,IAAI,YAAY,CAAC,KAAM,IAAG,EAAE,EAAE;AAChC,IAAI,4BAA4B,EAAE,CAAA;AAClC,SAAS;AACT,IAAI,6BAA6B,EAAE,CAAA;AACnC,GAAE;AACF;;;;"}