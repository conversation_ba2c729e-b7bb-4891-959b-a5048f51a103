{"version": 3, "file": "baseclient.js", "sources": ["../../src/baseclient.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type {\n  Bread<PERSON>rumb,\n  BreadcrumbHint,\n  Client,\n  ClientOptions,\n  DataCategory,\n  DsnComponents,\n  DynamicSamplingContext,\n  Envelope,\n  ErrorEvent,\n  Event,\n  EventDropReason,\n  EventHint,\n  EventProcessor,\n  FeedbackEvent,\n  Integration,\n  IntegrationClass,\n  MetricBucketItem,\n  MetricsAggregator,\n  Outcome,\n  ParameterizedString,\n  SdkMetadata,\n  Session,\n  SessionAggregates,\n  Severity,\n  SeverityLevel,\n  StartSpanOptions,\n  Transaction,\n  TransactionEvent,\n  Transport,\n  TransportMakeRequestResponse,\n} from '@sentry/types';\nimport {\n  SentryError,\n  SyncPromise,\n  addItemToEnvelope,\n  checkOrSetAlreadyCaught,\n  createAttachmentEnvelopeItem,\n  isParameterizedString,\n  isPlainObject,\n  isPrimitive,\n  isThenable,\n  logger,\n  makeDsn,\n  rejectedSyncPromise,\n  resolvedSyncPromise,\n} from '@sentry/utils';\n\nimport { getEnvelopeEndpointWithUrlEncodedAuth } from './api';\nimport { DEBUG_BUILD } from './debug-build';\nimport { createEventEnvelope, createSessionEnvelope } from './envelope';\nimport { getClient } from './exports';\nimport { getIsolationScope } from './hub';\nimport type { IntegrationIndex } from './integration';\nimport { afterSetupIntegrations } from './integration';\nimport { setupIntegration, setupIntegrations } from './integration';\nimport { createMetricEnvelope } from './metrics/envelope';\nimport type { Scope } from './scope';\nimport { updateSession } from './session';\nimport { getDynamicSamplingContextFromClient } from './tracing/dynamicSamplingContext';\nimport { prepareEvent } from './utils/prepareEvent';\n\nconst ALREADY_SEEN_ERROR = \"Not capturing exception because it's already been captured.\";\n\n/**\n * Base implementation for all JavaScript SDK clients.\n *\n * Call the constructor with the corresponding options\n * specific to the client subclass. To access these options later, use\n * {@link Client.getOptions}.\n *\n * If a Dsn is specified in the options, it will be parsed and stored. Use\n * {@link Client.getDsn} to retrieve the Dsn at any moment. In case the Dsn is\n * invalid, the constructor will throw a {@link SentryException}. Note that\n * without a valid Dsn, the SDK will not send any events to Sentry.\n *\n * Before sending an event, it is passed through\n * {@link BaseClient._prepareEvent} to add SDK information and scope data\n * (breadcrumbs and context). To add more custom information, override this\n * method and extend the resulting prepared event.\n *\n * To issue automatically created events (e.g. via instrumentation), use\n * {@link Client.captureEvent}. It will prepare the event and pass it through\n * the callback lifecycle. To issue auto-breadcrumbs, use\n * {@link Client.addBreadcrumb}.\n *\n * @example\n * class NodeClient extends BaseClient<NodeOptions> {\n *   public constructor(options: NodeOptions) {\n *     super(options);\n *   }\n *\n *   // ...\n * }\n */\nexport abstract class BaseClient<O extends ClientOptions> implements Client<O> {\n  /**\n   * A reference to a metrics aggregator\n   *\n   * @experimental Note this is alpha API. It may experience breaking changes in the future.\n   */\n  public metricsAggregator?: MetricsAggregator;\n\n  /** Options passed to the SDK. */\n  protected readonly _options: O;\n\n  /** The client Dsn, if specified in options. Without this Dsn, the SDK will be disabled. */\n  protected readonly _dsn?: DsnComponents;\n\n  protected readonly _transport?: Transport;\n\n  /** Array of set up integrations. */\n  protected _integrations: IntegrationIndex;\n\n  /** Indicates whether this client's integrations have been set up. */\n  protected _integrationsInitialized: boolean;\n\n  /** Number of calls being processed */\n  protected _numProcessing: number;\n\n  protected _eventProcessors: EventProcessor[];\n\n  /** Holds flushable  */\n  private _outcomes: { [key: string]: number };\n\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  private _hooks: Record<string, Function[]>;\n\n  /**\n   * Initializes this client instance.\n   *\n   * @param options Options for the client.\n   */\n  protected constructor(options: O) {\n    this._options = options;\n    this._integrations = {};\n    this._integrationsInitialized = false;\n    this._numProcessing = 0;\n    this._outcomes = {};\n    this._hooks = {};\n    this._eventProcessors = [];\n\n    if (options.dsn) {\n      this._dsn = makeDsn(options.dsn);\n    } else {\n      DEBUG_BUILD && logger.warn('No DSN provided, client will not send events.');\n    }\n\n    if (this._dsn) {\n      const url = getEnvelopeEndpointWithUrlEncodedAuth(this._dsn, options);\n      this._transport = options.transport({\n        tunnel: this._options.tunnel,\n        recordDroppedEvent: this.recordDroppedEvent.bind(this),\n        ...options.transportOptions,\n        url,\n      });\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public captureException(exception: any, hint?: EventHint, scope?: Scope): string | undefined {\n    // ensure we haven't captured this very object before\n    if (checkOrSetAlreadyCaught(exception)) {\n      DEBUG_BUILD && logger.log(ALREADY_SEEN_ERROR);\n      return;\n    }\n\n    let eventId: string | undefined = hint && hint.event_id;\n\n    this._process(\n      this.eventFromException(exception, hint)\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureMessage(\n    message: ParameterizedString,\n    // eslint-disable-next-line deprecation/deprecation\n    level?: Severity | SeverityLevel,\n    hint?: EventHint,\n    scope?: Scope,\n  ): string | undefined {\n    let eventId: string | undefined = hint && hint.event_id;\n\n    const eventMessage = isParameterizedString(message) ? message : String(message);\n\n    const promisedEvent = isPrimitive(message)\n      ? this.eventFromMessage(eventMessage, level, hint)\n      : this.eventFromException(message, hint);\n\n    this._process(\n      promisedEvent\n        .then(event => this._captureEvent(event, hint, scope))\n        .then(result => {\n          eventId = result;\n        }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureEvent(event: Event, hint?: EventHint, scope?: Scope): string | undefined {\n    // ensure we haven't captured this very object before\n    if (hint && hint.originalException && checkOrSetAlreadyCaught(hint.originalException)) {\n      DEBUG_BUILD && logger.log(ALREADY_SEEN_ERROR);\n      return;\n    }\n\n    let eventId: string | undefined = hint && hint.event_id;\n\n    const sdkProcessingMetadata = event.sdkProcessingMetadata || {};\n    const capturedSpanScope: Scope | undefined = sdkProcessingMetadata.capturedSpanScope;\n\n    this._process(\n      this._captureEvent(event, hint, capturedSpanScope || scope).then(result => {\n        eventId = result;\n      }),\n    );\n\n    return eventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureSession(session: Session): void {\n    if (!(typeof session.release === 'string')) {\n      DEBUG_BUILD && logger.warn('Discarded session because of missing or non-string release');\n    } else {\n      this.sendSession(session);\n      // After sending, we set init false to indicate it's not the first occurrence\n      updateSession(session, { init: false });\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getDsn(): DsnComponents | undefined {\n    return this._dsn;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getOptions(): O {\n    return this._options;\n  }\n\n  /**\n   * @see SdkMetadata in @sentry/types\n   *\n   * @return The metadata of the SDK\n   */\n  public getSdkMetadata(): SdkMetadata | undefined {\n    return this._options._metadata;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public getTransport(): Transport | undefined {\n    return this._transport;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public flush(timeout?: number): PromiseLike<boolean> {\n    const transport = this._transport;\n    if (transport) {\n      if (this.metricsAggregator) {\n        this.metricsAggregator.flush();\n      }\n      return this._isClientDoneProcessing(timeout).then(clientFinished => {\n        return transport.flush(timeout).then(transportFlushed => clientFinished && transportFlushed);\n      });\n    } else {\n      return resolvedSyncPromise(true);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public close(timeout?: number): PromiseLike<boolean> {\n    return this.flush(timeout).then(result => {\n      this.getOptions().enabled = false;\n      if (this.metricsAggregator) {\n        this.metricsAggregator.close();\n      }\n      return result;\n    });\n  }\n\n  /** Get all installed event processors. */\n  public getEventProcessors(): EventProcessor[] {\n    return this._eventProcessors;\n  }\n\n  /** @inheritDoc */\n  public addEventProcessor(eventProcessor: EventProcessor): void {\n    this._eventProcessors.push(eventProcessor);\n  }\n\n  /**\n   * This is an internal function to setup all integrations that should run on the client.\n   * @deprecated Use `client.init()` instead.\n   */\n  public setupIntegrations(forceInitialize?: boolean): void {\n    if ((forceInitialize && !this._integrationsInitialized) || (this._isEnabled() && !this._integrationsInitialized)) {\n      this._setupIntegrations();\n    }\n  }\n\n  /** @inheritdoc */\n  public init(): void {\n    if (this._isEnabled()) {\n      this._setupIntegrations();\n    }\n  }\n\n  /**\n   * Gets an installed integration by its `id`.\n   *\n   * @returns The installed integration or `undefined` if no integration with that `id` was installed.\n   * @deprecated Use `getIntegrationByName()` instead.\n   */\n  public getIntegrationById(integrationId: string): Integration | undefined {\n    return this.getIntegrationByName(integrationId);\n  }\n\n  /**\n   * Gets an installed integration by its name.\n   *\n   * @returns The installed integration or `undefined` if no integration with that `name` was installed.\n   */\n  public getIntegrationByName<T extends Integration = Integration>(integrationName: string): T | undefined {\n    return this._integrations[integrationName] as T | undefined;\n  }\n\n  /**\n   * Returns the client's instance of the given integration class, it any.\n   * @deprecated Use `getIntegrationByName()` instead.\n   */\n  public getIntegration<T extends Integration>(integration: IntegrationClass<T>): T | null {\n    try {\n      return (this._integrations[integration.id] as T) || null;\n    } catch (_oO) {\n      DEBUG_BUILD && logger.warn(`Cannot retrieve integration ${integration.id} from the current Client`);\n      return null;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addIntegration(integration: Integration): void {\n    const isAlreadyInstalled = this._integrations[integration.name];\n\n    // This hook takes care of only installing if not already installed\n    setupIntegration(this, integration, this._integrations);\n    // Here we need to check manually to make sure to not run this multiple times\n    if (!isAlreadyInstalled) {\n      afterSetupIntegrations(this, [integration]);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendEvent(event: Event, hint: EventHint = {}): void {\n    this.emit('beforeSendEvent', event, hint);\n\n    let env = createEventEnvelope(event, this._dsn, this._options._metadata, this._options.tunnel);\n\n    for (const attachment of hint.attachments || []) {\n      env = addItemToEnvelope(\n        env,\n        createAttachmentEnvelopeItem(\n          attachment,\n          this._options.transportOptions && this._options.transportOptions.textEncoder,\n        ),\n      );\n    }\n\n    const promise = this._sendEnvelope(env);\n    if (promise) {\n      promise.then(sendResponse => this.emit('afterSendEvent', event, sendResponse), null);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public sendSession(session: Session | SessionAggregates): void {\n    const env = createSessionEnvelope(session, this._dsn, this._options._metadata, this._options.tunnel);\n\n    // _sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this._sendEnvelope(env);\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public recordDroppedEvent(reason: EventDropReason, category: DataCategory, eventOrCount?: Event | number): void {\n    if (this._options.sendClientReports) {\n      // TODO v9: We do not need the `event` passed as third argument anymore, and can possibly remove this overload\n      // If event is passed as third argument, we assume this is a count of 1\n      const count = typeof eventOrCount === 'number' ? eventOrCount : 1;\n\n      // We want to track each category (error, transaction, session, replay_event) separately\n      // but still keep the distinction between different type of outcomes.\n      // We could use nested maps, but it's much easier to read and type this way.\n      // A correct type for map-based implementation if we want to go that route\n      // would be `Partial<Record<SentryRequestType, Partial<Record<Outcome, number>>>>`\n      // With typescript 4.1 we could even use template literal types\n      const key = `${reason}:${category}`;\n      DEBUG_BUILD && logger.log(`Recording outcome: \"${key}\"${count > 1 ? ` (${count} times)` : ''}`);\n      this._outcomes[key] = (this._outcomes[key] || 0) + count;\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public captureAggregateMetrics(metricBucketItems: Array<MetricBucketItem>): void {\n    DEBUG_BUILD && logger.log(`Flushing aggregated metrics, number of metrics: ${metricBucketItems.length}`);\n    const metricsEnvelope = createMetricEnvelope(\n      metricBucketItems,\n      this._dsn,\n      this._options._metadata,\n      this._options.tunnel,\n    );\n\n    // _sendEnvelope should not throw\n    // eslint-disable-next-line @typescript-eslint/no-floating-promises\n    this._sendEnvelope(metricsEnvelope);\n  }\n\n  // Keep on() & emit() signatures in sync with types' client.ts interface\n  /* eslint-disable @typescript-eslint/unified-signatures */\n\n  /** @inheritdoc */\n  public on(hook: 'startTransaction', callback: (transaction: Transaction) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'finishTransaction', callback: (transaction: Transaction) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'beforeEnvelope', callback: (envelope: Envelope) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'beforeSendEvent', callback: (event: Event, hint?: EventHint) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'preprocessEvent', callback: (event: Event, hint?: EventHint) => void): void;\n\n  /** @inheritdoc */\n  public on(\n    hook: 'afterSendEvent',\n    callback: (event: Event, sendResponse: TransportMakeRequestResponse | void) => void,\n  ): void;\n\n  /** @inheritdoc */\n  public on(hook: 'beforeAddBreadcrumb', callback: (breadcrumb: Breadcrumb, hint?: BreadcrumbHint) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'createDsc', callback: (dsc: DynamicSamplingContext) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'otelSpanEnd', callback: (otelSpan: unknown, mutableOptions: { drop: boolean }) => void): void;\n\n  /** @inheritdoc */\n  public on(\n    hook: 'beforeSendFeedback',\n    callback: (feedback: FeedbackEvent, options?: { includeReplay: boolean }) => void,\n  ): void;\n\n  /** @inheritdoc */\n  public on(hook: 'startPageLoadSpan', callback: (options: StartSpanOptions) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: 'startNavigationSpan', callback: (options: StartSpanOptions) => void): void;\n\n  /** @inheritdoc */\n  public on(hook: string, callback: unknown): void {\n    if (!this._hooks[hook]) {\n      this._hooks[hook] = [];\n    }\n\n    // @ts-expect-error We assue the types are correct\n    this._hooks[hook].push(callback);\n  }\n\n  /** @inheritdoc */\n  public emit(hook: 'startTransaction', transaction: Transaction): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'finishTransaction', transaction: Transaction): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'beforeEnvelope', envelope: Envelope): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'beforeSendEvent', event: Event, hint?: EventHint): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'preprocessEvent', event: Event, hint?: EventHint): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'afterSendEvent', event: Event, sendResponse: TransportMakeRequestResponse | void): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'beforeAddBreadcrumb', breadcrumb: Breadcrumb, hint?: BreadcrumbHint): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'createDsc', dsc: DynamicSamplingContext): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'otelSpanEnd', otelSpan: unknown, mutableOptions: { drop: boolean }): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'beforeSendFeedback', feedback: FeedbackEvent, options?: { includeReplay: boolean }): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'startPageLoadSpan', options: StartSpanOptions): void;\n\n  /** @inheritdoc */\n  public emit(hook: 'startNavigationSpan', options: StartSpanOptions): void;\n\n  /** @inheritdoc */\n  public emit(hook: string, ...rest: unknown[]): void {\n    if (this._hooks[hook]) {\n      this._hooks[hook].forEach(callback => callback(...rest));\n    }\n  }\n\n  /* eslint-enable @typescript-eslint/unified-signatures */\n\n  /** Setup integrations for this client. */\n  protected _setupIntegrations(): void {\n    const { integrations } = this._options;\n    this._integrations = setupIntegrations(this, integrations);\n    afterSetupIntegrations(this, integrations);\n\n    // TODO v8: We don't need this flag anymore\n    this._integrationsInitialized = true;\n  }\n\n  /** Updates existing session based on the provided event */\n  protected _updateSessionFromEvent(session: Session, event: Event): void {\n    let crashed = false;\n    let errored = false;\n    const exceptions = event.exception && event.exception.values;\n\n    if (exceptions) {\n      errored = true;\n\n      for (const ex of exceptions) {\n        const mechanism = ex.mechanism;\n        if (mechanism && mechanism.handled === false) {\n          crashed = true;\n          break;\n        }\n      }\n    }\n\n    // A session is updated and that session update is sent in only one of the two following scenarios:\n    // 1. Session with non terminal status and 0 errors + an error occurred -> Will set error count to 1 and send update\n    // 2. Session with non terminal status and 1 error + a crash occurred -> Will set status crashed and send update\n    const sessionNonTerminal = session.status === 'ok';\n    const shouldUpdateAndSend = (sessionNonTerminal && session.errors === 0) || (sessionNonTerminal && crashed);\n\n    if (shouldUpdateAndSend) {\n      updateSession(session, {\n        ...(crashed && { status: 'crashed' }),\n        errors: session.errors || Number(errored || crashed),\n      });\n      this.captureSession(session);\n    }\n  }\n\n  /**\n   * Determine if the client is finished processing. Returns a promise because it will wait `timeout` ms before saying\n   * \"no\" (resolving to `false`) in order to give the client a chance to potentially finish first.\n   *\n   * @param timeout The time, in ms, after which to resolve to `false` if the client is still busy. Passing `0` (or not\n   * passing anything) will make the promise wait as long as it takes for processing to finish before resolving to\n   * `true`.\n   * @returns A promise which will resolve to `true` if processing is already done or finishes before the timeout, and\n   * `false` otherwise\n   */\n  protected _isClientDoneProcessing(timeout?: number): PromiseLike<boolean> {\n    return new SyncPromise(resolve => {\n      let ticked: number = 0;\n      const tick: number = 1;\n\n      const interval = setInterval(() => {\n        if (this._numProcessing == 0) {\n          clearInterval(interval);\n          resolve(true);\n        } else {\n          ticked += tick;\n          if (timeout && ticked >= timeout) {\n            clearInterval(interval);\n            resolve(false);\n          }\n        }\n      }, tick);\n    });\n  }\n\n  /** Determines whether this SDK is enabled and a transport is present. */\n  protected _isEnabled(): boolean {\n    return this.getOptions().enabled !== false && this._transport !== undefined;\n  }\n\n  /**\n   * Adds common information to events.\n   *\n   * The information includes release and environment from `options`,\n   * breadcrumbs and context (extra, tags and user) from the scope.\n   *\n   * Information that is already present in the event is never overwritten. For\n   * nested objects, such as the context, keys are merged.\n   *\n   * @param event The original event.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A new event with more information.\n   */\n  protected _prepareEvent(\n    event: Event,\n    hint: EventHint,\n    scope?: Scope,\n    isolationScope = getIsolationScope(),\n  ): PromiseLike<Event | null> {\n    const options = this.getOptions();\n    const integrations = Object.keys(this._integrations);\n    if (!hint.integrations && integrations.length > 0) {\n      hint.integrations = integrations;\n    }\n\n    this.emit('preprocessEvent', event, hint);\n\n    return prepareEvent(options, event, hint, scope, this, isolationScope).then(evt => {\n      if (evt === null) {\n        return evt;\n      }\n\n      const propagationContext = {\n        ...isolationScope.getPropagationContext(),\n        ...(scope ? scope.getPropagationContext() : undefined),\n      };\n\n      const trace = evt.contexts && evt.contexts.trace;\n      if (!trace && propagationContext) {\n        const { traceId: trace_id, spanId, parentSpanId, dsc } = propagationContext;\n        evt.contexts = {\n          trace: {\n            trace_id,\n            span_id: spanId,\n            parent_span_id: parentSpanId,\n          },\n          ...evt.contexts,\n        };\n\n        const dynamicSamplingContext = dsc ? dsc : getDynamicSamplingContextFromClient(trace_id, this, scope);\n\n        evt.sdkProcessingMetadata = {\n          dynamicSamplingContext,\n          ...evt.sdkProcessingMetadata,\n        };\n      }\n      return evt;\n    });\n  }\n\n  /**\n   * Processes the event and logs an error in case of rejection\n   * @param event\n   * @param hint\n   * @param scope\n   */\n  protected _captureEvent(event: Event, hint: EventHint = {}, scope?: Scope): PromiseLike<string | undefined> {\n    return this._processEvent(event, hint, scope).then(\n      finalEvent => {\n        return finalEvent.event_id;\n      },\n      reason => {\n        if (DEBUG_BUILD) {\n          // If something's gone wrong, log the error as a warning. If it's just us having used a `SentryError` for\n          // control flow, log just the message (no stack) as a log-level log.\n          const sentryError = reason as SentryError;\n          if (sentryError.logLevel === 'log') {\n            logger.log(sentryError.message);\n          } else {\n            logger.warn(sentryError);\n          }\n        }\n        return undefined;\n      },\n    );\n  }\n\n  /**\n   * Processes an event (either error or message) and sends it to Sentry.\n   *\n   * This also adds breadcrumbs and context information to the event. However,\n   * platform specific meta data (such as the User's IP address) must be added\n   * by the SDK implementor.\n   *\n   *\n   * @param event The event to send to Sentry.\n   * @param hint May contain additional information about the original exception.\n   * @param scope A scope containing event metadata.\n   * @returns A SyncPromise that resolves with the event or rejects in case event was/will not be send.\n   */\n  protected _processEvent(event: Event, hint: EventHint, scope?: Scope): PromiseLike<Event> {\n    const options = this.getOptions();\n    const { sampleRate } = options;\n\n    const isTransaction = isTransactionEvent(event);\n    const isError = isErrorEvent(event);\n    const eventType = event.type || 'error';\n    const beforeSendLabel = `before send for type \\`${eventType}\\``;\n\n    // 1.0 === 100% events are sent\n    // 0.0 === 0% events are sent\n    // Sampling for transaction happens somewhere else\n    if (isError && typeof sampleRate === 'number' && Math.random() > sampleRate) {\n      this.recordDroppedEvent('sample_rate', 'error', event);\n      return rejectedSyncPromise(\n        new SentryError(\n          `Discarding event because it's not included in the random sample (sampling rate = ${sampleRate})`,\n          'log',\n        ),\n      );\n    }\n\n    const dataCategory: DataCategory = eventType === 'replay_event' ? 'replay' : eventType;\n\n    const sdkProcessingMetadata = event.sdkProcessingMetadata || {};\n    const capturedSpanIsolationScope: Scope | undefined = sdkProcessingMetadata.capturedSpanIsolationScope;\n\n    return this._prepareEvent(event, hint, scope, capturedSpanIsolationScope)\n      .then(prepared => {\n        if (prepared === null) {\n          this.recordDroppedEvent('event_processor', dataCategory, event);\n          throw new SentryError('An event processor returned `null`, will not send event.', 'log');\n        }\n\n        const isInternalException = hint.data && (hint.data as { __sentry__: boolean }).__sentry__ === true;\n        if (isInternalException) {\n          return prepared;\n        }\n\n        const result = processBeforeSend(options, prepared, hint);\n        return _validateBeforeSendResult(result, beforeSendLabel);\n      })\n      .then(processedEvent => {\n        if (processedEvent === null) {\n          this.recordDroppedEvent('before_send', dataCategory, event);\n          if (isTransaction) {\n            const spans = event.spans || [];\n            // the transaction itself counts as one span, plus all the child spans that are added\n            const spanCount = 1 + spans.length;\n            this.recordDroppedEvent('before_send', 'span', spanCount);\n          }\n          throw new SentryError(`${beforeSendLabel} returned \\`null\\`, will not send event.`, 'log');\n        }\n\n        const session = scope && scope.getSession();\n        if (!isTransaction && session) {\n          this._updateSessionFromEvent(session, processedEvent);\n        }\n\n        if (isTransaction) {\n          const spanCountBefore =\n            (processedEvent.sdkProcessingMetadata && processedEvent.sdkProcessingMetadata.spanCountBeforeProcessing) ||\n            0;\n          const spanCountAfter = processedEvent.spans ? processedEvent.spans.length : 0;\n\n          const droppedSpanCount = spanCountBefore - spanCountAfter;\n          if (droppedSpanCount > 0) {\n            this.recordDroppedEvent('before_send', 'span', droppedSpanCount);\n          }\n        }\n\n        // None of the Sentry built event processor will update transaction name,\n        // so if the transaction name has been changed by an event processor, we know\n        // it has to come from custom event processor added by a user\n        const transactionInfo = processedEvent.transaction_info;\n        if (isTransaction && transactionInfo && processedEvent.transaction !== event.transaction) {\n          const source = 'custom';\n          processedEvent.transaction_info = {\n            ...transactionInfo,\n            source,\n          };\n        }\n\n        this.sendEvent(processedEvent, hint);\n        return processedEvent;\n      })\n      .then(null, reason => {\n        if (reason instanceof SentryError) {\n          throw reason;\n        }\n\n        this.captureException(reason, {\n          data: {\n            __sentry__: true,\n          },\n          originalException: reason,\n        });\n        throw new SentryError(\n          `Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.\\nReason: ${reason}`,\n        );\n      });\n  }\n\n  /**\n   * Occupies the client with processing and event\n   */\n  protected _process<T>(promise: PromiseLike<T>): void {\n    this._numProcessing++;\n    void promise.then(\n      value => {\n        this._numProcessing--;\n        return value;\n      },\n      reason => {\n        this._numProcessing--;\n        return reason;\n      },\n    );\n  }\n\n  /**\n   * @inheritdoc\n   */\n  protected _sendEnvelope(envelope: Envelope): PromiseLike<void | TransportMakeRequestResponse> | void {\n    this.emit('beforeEnvelope', envelope);\n\n    if (this._isEnabled() && this._transport) {\n      return this._transport.send(envelope).then(null, reason => {\n        DEBUG_BUILD && logger.error('Error while sending event:', reason);\n      });\n    } else {\n      DEBUG_BUILD && logger.error('Transport disabled');\n    }\n  }\n\n  /**\n   * Clears outcomes on this client and returns them.\n   */\n  protected _clearOutcomes(): Outcome[] {\n    const outcomes = this._outcomes;\n    this._outcomes = {};\n    return Object.keys(outcomes).map(key => {\n      const [reason, category] = key.split(':') as [EventDropReason, DataCategory];\n      return {\n        reason,\n        category,\n        quantity: outcomes[key],\n      };\n    });\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\n  public abstract eventFromException(_exception: any, _hint?: EventHint): PromiseLike<Event>;\n\n  /**\n   * @inheritDoc\n   */\n  public abstract eventFromMessage(\n    _message: ParameterizedString,\n    // eslint-disable-next-line deprecation/deprecation\n    _level?: Severity | SeverityLevel,\n    _hint?: EventHint,\n  ): PromiseLike<Event>;\n}\n\n/**\n * Verifies that return value of configured `beforeSend` or `beforeSendTransaction` is of expected type, and returns the value if so.\n */\nfunction _validateBeforeSendResult(\n  beforeSendResult: PromiseLike<Event | null> | Event | null,\n  beforeSendLabel: string,\n): PromiseLike<Event | null> | Event | null {\n  const invalidValueError = `${beforeSendLabel} must return \\`null\\` or a valid event.`;\n  if (isThenable(beforeSendResult)) {\n    return beforeSendResult.then(\n      event => {\n        if (!isPlainObject(event) && event !== null) {\n          throw new SentryError(invalidValueError);\n        }\n        return event;\n      },\n      e => {\n        throw new SentryError(`${beforeSendLabel} rejected with ${e}`);\n      },\n    );\n  } else if (!isPlainObject(beforeSendResult) && beforeSendResult !== null) {\n    throw new SentryError(invalidValueError);\n  }\n  return beforeSendResult;\n}\n\n/**\n * Process the matching `beforeSendXXX` callback.\n */\nfunction processBeforeSend(\n  options: ClientOptions,\n  event: Event,\n  hint: EventHint,\n): PromiseLike<Event | null> | Event | null {\n  const { beforeSend, beforeSendTransaction } = options;\n\n  if (isErrorEvent(event) && beforeSend) {\n    return beforeSend(event, hint);\n  }\n\n  if (isTransactionEvent(event) && beforeSendTransaction) {\n    if (event.spans) {\n      // We store the # of spans before processing in SDK metadata,\n      // so we can compare it afterwards to determine how many spans were dropped\n      const spanCountBefore = event.spans.length;\n      event.sdkProcessingMetadata = {\n        ...event.sdkProcessingMetadata,\n        spanCountBeforeProcessing: spanCountBefore,\n      };\n    }\n    return beforeSendTransaction(event, hint);\n  }\n\n  return event;\n}\n\nfunction isErrorEvent(event: Event): event is ErrorEvent {\n  return event.type === undefined;\n}\n\nfunction isTransactionEvent(event: Event): event is TransactionEvent {\n  return event.type === 'transaction';\n}\n\n/**\n * Add an event processor to the current client.\n * This event processor will run for all events processed by this client.\n */\nexport function addEventProcessor(callback: EventProcessor): void {\n  const client = getClient();\n\n  if (!client || !client.addEventProcessor) {\n    return;\n  }\n\n  client.addEventProcessor(callback);\n}\n"], "names": ["makeDsn", "DEBUG_BUILD", "logger", "getEnvelopeEndpointWithUrlEncodedAuth", "checkOrSetAlreadyCaught", "isParameterizedString", "isPrimitive", "session", "updateSession", "resolvedSyncPromise", "integration", "setupIntegration", "afterSetupIntegrations", "createEventEnvelope", "addItemToEnvelope", "createAttachmentEnvelopeItem", "createSessionEnvelope", "createMetricEnvelope", "setupIntegrations", "SyncPromise", "getIsolationScope", "prepareEvent", "dynamicSamplingContext", "getDynamicSamplingContextFromClient", "rejectedSyncPromise", "SentryError", "isThenable", "isPlainObject", "getClient"], "mappings": ";;;;;;;;;;;;;;AA+DA,MAAM,kBAAA,GAAqB,6DAA6D,CAAA;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACgB,MAAM,UAAU,CAA+C;AAC/E;AACA;AACA;AACA;AACA;;AAGA;;AAGA;;AAKA;;AAGA;;AAGA;;AAKA;;AAGA;;AAGA;AACA;AACA;AACA;AACA;AACA,GAAY,WAAW,CAAC,OAAO,EAAK;AACpC,IAAI,IAAI,CAAC,QAAS,GAAE,OAAO,CAAA;AAC3B,IAAI,IAAI,CAAC,aAAc,GAAE,EAAE,CAAA;AAC3B,IAAI,IAAI,CAAC,wBAAyB,GAAE,KAAK,CAAA;AACzC,IAAI,IAAI,CAAC,cAAe,GAAE,CAAC,CAAA;AAC3B,IAAI,IAAI,CAAC,SAAU,GAAE,EAAE,CAAA;AACvB,IAAI,IAAI,CAAC,MAAO,GAAE,EAAE,CAAA;AACpB,IAAI,IAAI,CAAC,gBAAiB,GAAE,EAAE,CAAA;AAC9B;AACA,IAAI,IAAI,OAAO,CAAC,GAAG,EAAE;AACrB,MAAM,IAAI,CAAC,IAAK,GAAEA,aAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;AACtC,WAAW;AACX,MAAMC,0BAAeC,YAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAA;AACjF,KAAI;AACJ;AACA,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;AACnB,MAAM,MAAM,GAAI,GAAEC,yCAAqC,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAC3E,MAAM,IAAI,CAAC,UAAA,GAAa,OAAO,CAAC,SAAS,CAAC;AAC1C,QAAQ,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;AACpC,QAAQ,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9D,QAAQ,GAAG,OAAO,CAAC,gBAAgB;AACnC,QAAQ,GAAG;AACX,OAAO,CAAC,CAAA;AACR,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,gBAAgB,CAAC,SAAS,EAAO,IAAI,EAAc,KAAK,EAA8B;AAC/F;AACA,IAAI,IAAIC,6BAAuB,CAAC,SAAS,CAAC,EAAE;AAC5C,MAAMH,0BAAeC,YAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAA;AACnD,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAI,IAAI,OAAO,GAAuB,QAAQ,IAAI,CAAC,QAAQ,CAAA;AAC3D;AACA,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,CAAA;AAC7C,SAAS,IAAI,CAAC,KAAA,IAAS,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;AAC7D,SAAS,IAAI,CAAC,MAAA,IAAU;AACxB,UAAU,OAAA,GAAU,MAAM,CAAA;AAC1B,SAAS,CAAC;AACV,KAAK,CAAA;AACL;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,cAAc;AACvB,IAAI,OAAO;AACX;AACA,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,KAAK;AACT,IAAwB;AACxB,IAAI,IAAI,OAAO,GAAuB,QAAQ,IAAI,CAAC,QAAQ,CAAA;AAC3D;AACA,IAAI,MAAM,YAAA,GAAeG,2BAAqB,CAAC,OAAO,CAAE,GAAE,OAAQ,GAAE,MAAM,CAAC,OAAO,CAAC,CAAA;AACnF;AACA,IAAI,MAAM,aAAA,GAAgBC,iBAAW,CAAC,OAAO,CAAA;AAC7C,QAAQ,IAAI,CAAC,gBAAgB,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAA;AACvD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AAC9C;AACA,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,aAAA;AACN,SAAS,IAAI,CAAC,KAAA,IAAS,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;AAC7D,SAAS,IAAI,CAAC,MAAA,IAAU;AACxB,UAAU,OAAA,GAAU,MAAM,CAAA;AAC1B,SAAS,CAAC;AACV,KAAK,CAAA;AACL;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAc,KAAK,EAA8B;AACzF;AACA,IAAI,IAAI,IAAK,IAAG,IAAI,CAAC,iBAAA,IAAqBF,6BAAuB,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;AAC3F,MAAMH,0BAAeC,YAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAA;AACnD,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAI,IAAI,OAAO,GAAuB,QAAQ,IAAI,CAAC,QAAQ,CAAA;AAC3D;AACA,IAAI,MAAM,wBAAwB,KAAK,CAAC,qBAAsB,IAAG,EAAE,CAAA;AACnE,IAAI,MAAM,iBAAiB,GAAsB,qBAAqB,CAAC,iBAAiB,CAAA;AACxF;AACA,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,iBAAkB,IAAG,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU;AACjF,QAAQ,OAAA,GAAU,MAAM,CAAA;AACxB,OAAO,CAAC;AACR,KAAK,CAAA;AACL;AACA,IAAI,OAAO,OAAO,CAAA;AAClB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,cAAc,CAACK,SAAO,EAAiB;AAChD,IAAI,IAAI,EAAE,OAAOA,SAAO,CAAC,OAAQ,KAAI,QAAQ,CAAC,EAAE;AAChD,MAAMN,0BAAeC,YAAM,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAA;AAC9F,WAAW;AACX,MAAM,IAAI,CAAC,WAAW,CAACK,SAAO,CAAC,CAAA;AAC/B;AACA,MAAMC,qBAAa,CAACD,SAAO,EAAE,EAAE,IAAI,EAAE,KAAM,EAAC,CAAC,CAAA;AAC7C,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,MAAM,GAA8B;AAC7C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAA;AACpB,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,UAAU,GAAM;AACzB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAA;AACxB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,GAA4B;AACnD,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAA;AAClC,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,YAAY,GAA0B;AAC/C,IAAI,OAAO,IAAI,CAAC,UAAU,CAAA;AAC1B,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,KAAK,CAAC,OAAO,EAAiC;AACvD,IAAI,MAAM,SAAA,GAAY,IAAI,CAAC,UAAU,CAAA;AACrC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAClC,QAAQ,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;AACtC,OAAM;AACN,MAAM,OAAO,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,cAAA,IAAkB;AAC1E,QAAQ,OAAO,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,gBAAiB,IAAG,cAAe,IAAG,gBAAgB,CAAC,CAAA;AACpG,OAAO,CAAC,CAAA;AACR,WAAW;AACX,MAAM,OAAOE,yBAAmB,CAAC,IAAI,CAAC,CAAA;AACtC,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,KAAK,CAAC,OAAO,EAAiC;AACvD,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAA,IAAU;AAC9C,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,OAAA,GAAU,KAAK,CAAA;AACvC,MAAM,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAClC,QAAQ,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;AACtC,OAAM;AACN,MAAM,OAAO,MAAM,CAAA;AACnB,KAAK,CAAC,CAAA;AACN,GAAE;AACF;AACA;AACA,GAAS,kBAAkB,GAAqB;AAChD,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAA;AAChC,GAAE;AACF;AACA;AACA,GAAS,iBAAiB,CAAC,cAAc,EAAwB;AACjE,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;AAC9C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,iBAAiB,CAAC,eAAe,EAAkB;AAC5D,IAAI,IAAI,CAAC,eAAA,IAAmB,CAAC,IAAI,CAAC,wBAAwB,MAAM,IAAI,CAAC,UAAU,EAAG,IAAG,CAAC,IAAI,CAAC,wBAAwB,CAAC,EAAE;AACtH,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;AAC/B,KAAI;AACJ,GAAE;AACF;AACA;AACA,GAAS,IAAI,GAAS;AACtB,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;AAC3B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;AAC/B,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,aAAa,EAAmC;AAC5E,IAAI,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAA;AACnD,GAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA,GAAS,oBAAoB,CAAsC,eAAe,EAAyB;AAC3G,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,eAAe,CAAE,EAAA;AAC/C,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,GAAS,cAAc,CAAwB,WAAW,EAAiC;AAC3F,IAAI,IAAI;AACR,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,CAAA,MAAW,IAAI,CAAA;AAC9D,KAAM,CAAA,OAAO,GAAG,EAAE;AAClB,MAAMR,sBAAY,IAAGC,YAAM,CAAC,IAAI,CAAC,CAAC,4BAA4B,EAAE,WAAW,CAAC,EAAE,CAAC,wBAAwB,CAAC,CAAC,CAAA;AACzG,MAAM,OAAO,IAAI,CAAA;AACjB,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,cAAc,CAACQ,aAAW,EAAqB;AACxD,IAAI,MAAM,kBAAmB,GAAE,IAAI,CAAC,aAAa,CAACA,aAAW,CAAC,IAAI,CAAC,CAAA;AACnE;AACA;AACA,IAAIC,4BAAgB,CAAC,IAAI,EAAED,aAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAA;AAC3D;AACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC7B,MAAME,kCAAsB,CAAC,IAAI,EAAE,CAACF,aAAW,CAAC,CAAC,CAAA;AACjD,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,SAAS,CAAC,KAAK,EAAS,IAAI,GAAc,EAAE,EAAQ;AAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;AAC7C;AACA,IAAI,IAAI,MAAMG,4BAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;AAClG;AACA,IAAI,KAAK,MAAM,UAAW,IAAG,IAAI,CAAC,WAAY,IAAG,EAAE,EAAE;AACrD,MAAM,GAAA,GAAMC,uBAAiB;AAC7B,QAAQ,GAAG;AACX,QAAQC,kCAA4B;AACpC,UAAU,UAAU;AACpB,UAAU,IAAI,CAAC,QAAQ,CAAC,gBAAiB,IAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,WAAW;AACtF,SAAS;AACT,OAAO,CAAA;AACP,KAAI;AACJ;AACA,IAAI,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;AAC3C,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,CAAC,IAAI,CAAC,YAAa,IAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,EAAE,YAAY,CAAC,EAAE,IAAI,CAAC,CAAA;AAC1F,KAAI;AACJ,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,WAAW,CAAC,OAAO,EAAqC;AACjE,IAAI,MAAM,MAAMC,8BAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;AACxG;AACA;AACA;AACA,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;AAC3B,GAAE;AACF;AACA;AACA;AACA;AACA,GAAS,kBAAkB,CAAC,MAAM,EAAmB,QAAQ,EAAgB,YAAY,EAAyB;AAClH,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE;AACzC;AACA;AACA,MAAM,MAAM,KAAM,GAAE,OAAO,YAAA,KAAiB,QAAS,GAAE,YAAa,GAAE,CAAC,CAAA;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAA,GAAM,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,QAAA,CAAA,CAAA,CAAA;AACA,MAAAf,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,CAAA,oBAAA,EAAA,GAAA,CAAA,CAAA,EAAA,KAAA,GAAA,CAAA,GAAA,CAAA,EAAA,EAAA,KAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,KAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA,GAAA,uBAAA,CAAA,iBAAA,EAAA;AACA,IAAAD,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,CAAA,gDAAA,EAAA,iBAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAA,eAAA,GAAAe,+BAAA;AACA,MAAA,iBAAA;AACA,MAAA,IAAA,CAAA,IAAA;AACA,MAAA,IAAA,CAAA,QAAA,CAAA,SAAA;AACA,MAAA,IAAA,CAAA,QAAA,CAAA,MAAA;AACA,KAAA,CAAA;AACA;AACA;AACA;AACA,IAAA,IAAA,CAAA,aAAA,CAAA,eAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA;;AA0CA;AACA,GAAA,EAAA,CAAA,IAAA,EAAA,QAAA,EAAA;AACA,IAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,MAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA;AACA,KAAA;AACA;AACA;AACA,IAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;;AAoCA;AACA,GAAA,IAAA,CAAA,IAAA,EAAA,GAAA,IAAA,EAAA;AACA,IAAA,IAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,EAAA;AACA,MAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,QAAA,IAAA,QAAA,CAAA,GAAA,IAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA,GAAA,kBAAA,GAAA;AACA,IAAA,MAAA,EAAA,YAAA,EAAA,GAAA,IAAA,CAAA,QAAA,CAAA;AACA,IAAA,IAAA,CAAA,aAAA,GAAAC,6BAAA,CAAA,IAAA,EAAA,YAAA,CAAA,CAAA;AACA,IAAAN,kCAAA,CAAA,IAAA,EAAA,YAAA,CAAA,CAAA;AACA;AACA;AACA,IAAA,IAAA,CAAA,wBAAA,GAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,uBAAA,CAAAL,SAAA,EAAA,KAAA,EAAA;AACA,IAAA,IAAA,OAAA,GAAA,KAAA,CAAA;AACA,IAAA,IAAA,OAAA,GAAA,KAAA,CAAA;AACA,IAAA,MAAA,UAAA,GAAA,KAAA,CAAA,SAAA,IAAA,KAAA,CAAA,SAAA,CAAA,MAAA,CAAA;AACA;AACA,IAAA,IAAA,UAAA,EAAA;AACA,MAAA,OAAA,GAAA,IAAA,CAAA;AACA;AACA,MAAA,KAAA,MAAA,EAAA,IAAA,UAAA,EAAA;AACA,QAAA,MAAA,SAAA,GAAA,EAAA,CAAA,SAAA,CAAA;AACA,QAAA,IAAA,SAAA,IAAA,SAAA,CAAA,OAAA,KAAA,KAAA,EAAA;AACA,UAAA,OAAA,GAAA,IAAA,CAAA;AACA,UAAA,MAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;AACA;AACA;AACA;AACA;AACA,IAAA,MAAA,kBAAA,GAAAA,SAAA,CAAA,MAAA,KAAA,IAAA,CAAA;AACA,IAAA,MAAA,mBAAA,GAAA,CAAA,kBAAA,IAAAA,SAAA,CAAA,MAAA,KAAA,CAAA,MAAA,kBAAA,IAAA,OAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,mBAAA,EAAA;AACA,MAAAC,qBAAA,CAAAD,SAAA,EAAA;AACA,QAAA,IAAA,OAAA,IAAA,EAAA,MAAA,EAAA,SAAA,EAAA,CAAA;AACA,QAAA,MAAA,EAAAA,SAAA,CAAA,MAAA,IAAA,MAAA,CAAA,OAAA,IAAA,OAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,MAAA,IAAA,CAAA,cAAA,CAAAA,SAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAA,uBAAA,CAAA,OAAA,EAAA;AACA,IAAA,OAAA,IAAAY,iBAAA,CAAA,OAAA,IAAA;AACA,MAAA,IAAA,MAAA,GAAA,CAAA,CAAA;AACA,MAAA,MAAA,IAAA,GAAA,CAAA,CAAA;AACA;AACA,MAAA,MAAA,QAAA,GAAA,WAAA,CAAA,MAAA;AACA,QAAA,IAAA,IAAA,CAAA,cAAA,IAAA,CAAA,EAAA;AACA,UAAA,aAAA,CAAA,QAAA,CAAA,CAAA;AACA,UAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AACA,SAAA,MAAA;AACA,UAAA,MAAA,IAAA,IAAA,CAAA;AACA,UAAA,IAAA,OAAA,IAAA,MAAA,IAAA,OAAA,EAAA;AACA,YAAA,aAAA,CAAA,QAAA,CAAA,CAAA;AACA,YAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AACA,WAAA;AACA,SAAA;AACA,OAAA,EAAA,IAAA,CAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,GAAA,UAAA,GAAA;AACA,IAAA,OAAA,IAAA,CAAA,UAAA,EAAA,CAAA,OAAA,KAAA,KAAA,IAAA,IAAA,CAAA,UAAA,KAAA,SAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAA,aAAA;AACA,IAAA,KAAA;AACA,IAAA,IAAA;AACA,IAAA,KAAA;AACA,IAAA,cAAA,GAAAC,qBAAA,EAAA;AACA,IAAA;AACA,IAAA,MAAA,OAAA,GAAA,IAAA,CAAA,UAAA,EAAA,CAAA;AACA,IAAA,MAAA,YAAA,GAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA;AACA,IAAA,IAAA,CAAA,IAAA,CAAA,YAAA,IAAA,YAAA,CAAA,MAAA,GAAA,CAAA,EAAA;AACA,MAAA,IAAA,CAAA,YAAA,GAAA,YAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,CAAA,IAAA,CAAA,iBAAA,EAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACA;AACA,IAAA,OAAAC,yBAAA,CAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA,IAAA,EAAA,cAAA,CAAA,CAAA,IAAA,CAAA,GAAA,IAAA;AACA,MAAA,IAAA,GAAA,KAAA,IAAA,EAAA;AACA,QAAA,OAAA,GAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,MAAA,kBAAA,GAAA;AACA,QAAA,GAAA,cAAA,CAAA,qBAAA,EAAA;AACA,QAAA,IAAA,KAAA,GAAA,KAAA,CAAA,qBAAA,EAAA,GAAA,SAAA,CAAA;AACA,OAAA,CAAA;AACA;AACA,MAAA,MAAA,KAAA,GAAA,GAAA,CAAA,QAAA,IAAA,GAAA,CAAA,QAAA,CAAA,KAAA,CAAA;AACA,MAAA,IAAA,CAAA,KAAA,IAAA,kBAAA,EAAA;AACA,QAAA,MAAA,EAAA,OAAA,EAAA,QAAA,EAAA,MAAA,EAAA,YAAA,EAAA,GAAA,EAAA,GAAA,kBAAA,CAAA;AACA,QAAA,GAAA,CAAA,QAAA,GAAA;AACA,UAAA,KAAA,EAAA;AACA,YAAA,QAAA;AACA,YAAA,OAAA,EAAA,MAAA;AACA,YAAA,cAAA,EAAA,YAAA;AACA,WAAA;AACA,UAAA,GAAA,GAAA,CAAA,QAAA;AACA,SAAA,CAAA;AACA;AACA,QAAA,MAAAC,wBAAA,GAAA,GAAA,GAAA,GAAA,GAAAC,0DAAA,CAAA,QAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AACA;AACA,QAAA,GAAA,CAAA,qBAAA,GAAA;AACA,kCAAAD,wBAAA;AACA,UAAA,GAAA,GAAA,CAAA,qBAAA;AACA,SAAA,CAAA;AACA,OAAA;AACA,MAAA,OAAA,GAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAA,aAAA,CAAA,KAAA,EAAA,IAAA,GAAA,EAAA,EAAA,KAAA,EAAA;AACA,IAAA,OAAA,IAAA,CAAA,aAAA,CAAA,KAAA,EAAA,IAAA,EAAA,KAAA,CAAA,CAAA,IAAA;AACA,MAAA,UAAA,IAAA;AACA,QAAA,OAAA,UAAA,CAAA,QAAA,CAAA;AACA,OAAA;AACA,MAAA,MAAA,IAAA;AACA,QAAA,IAAArB,sBAAA,EAAA;AACA;AACA;AACA,UAAA,MAAA,WAAA,GAAA,MAAA,EAAA;AACA,UAAA,IAAA,WAAA,CAAA,QAAA,KAAA,KAAA,EAAA;AACA,YAAAC,YAAA,CAAA,GAAA,CAAA,WAAA,CAAA,OAAA,CAAA,CAAA;AACA,WAAA,MAAA;AACA,YAAAA,YAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA;AACA,WAAA;AACA,SAAA;AACA,QAAA,OAAA,SAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAA,aAAA,CAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA;AACA,IAAA,MAAA,OAAA,GAAA,IAAA,CAAA,UAAA,EAAA,CAAA;AACA,IAAA,MAAA,EAAA,UAAA,EAAA,GAAA,OAAA,CAAA;AACA;AACA,IAAA,MAAA,aAAA,GAAA,kBAAA,CAAA,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,OAAA,GAAA,YAAA,CAAA,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,SAAA,GAAA,KAAA,CAAA,IAAA,IAAA,OAAA,CAAA;AACA,IAAA,MAAA,eAAA,GAAA,CAAA,uBAAA,EAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AACA;AACA;AACA;AACA;AACA,IAAA,IAAA,OAAA,IAAA,OAAA,UAAA,KAAA,QAAA,IAAA,IAAA,CAAA,MAAA,EAAA,GAAA,UAAA,EAAA;AACA,MAAA,IAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,OAAA,EAAA,KAAA,CAAA,CAAA;AACA,MAAA,OAAAsB,yBAAA;AACA,QAAA,IAAAC,iBAAA;AACA,UAAA,CAAA,iFAAA,EAAA,UAAA,CAAA,CAAA,CAAA;AACA,UAAA,KAAA;AACA,SAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,MAAA,YAAA,GAAA,SAAA,KAAA,cAAA,GAAA,QAAA,GAAA,SAAA,CAAA;AACA;AACA,IAAA,MAAA,qBAAA,GAAA,KAAA,CAAA,qBAAA,IAAA,EAAA,CAAA;AACA,IAAA,MAAA,0BAAA,GAAA,qBAAA,CAAA,0BAAA,CAAA;AACA;AACA,IAAA,OAAA,IAAA,CAAA,aAAA,CAAA,KAAA,EAAA,IAAA,EAAA,KAAA,EAAA,0BAAA,CAAA;AACA,OAAA,IAAA,CAAA,QAAA,IAAA;AACA,QAAA,IAAA,QAAA,KAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,kBAAA,CAAA,iBAAA,EAAA,YAAA,EAAA,KAAA,CAAA,CAAA;AACA,UAAA,MAAA,IAAAA,iBAAA,CAAA,0DAAA,EAAA,KAAA,CAAA,CAAA;AACA,SAAA;AACA;AACA,QAAA,MAAA,mBAAA,GAAA,IAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,IAAA,GAAA,UAAA,KAAA,IAAA,CAAA;AACA,QAAA,IAAA,mBAAA,EAAA;AACA,UAAA,OAAA,QAAA,CAAA;AACA,SAAA;AACA;AACA,QAAA,MAAA,MAAA,GAAA,iBAAA,CAAA,OAAA,EAAA,QAAA,EAAA,IAAA,CAAA,CAAA;AACA,QAAA,OAAA,yBAAA,CAAA,MAAA,EAAA,eAAA,CAAA,CAAA;AACA,OAAA,CAAA;AACA,OAAA,IAAA,CAAA,cAAA,IAAA;AACA,QAAA,IAAA,cAAA,KAAA,IAAA,EAAA;AACA,UAAA,IAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,YAAA,EAAA,KAAA,CAAA,CAAA;AACA,UAAA,IAAA,aAAA,EAAA;AACA,YAAA,MAAA,KAAA,GAAA,KAAA,CAAA,KAAA,IAAA,EAAA,CAAA;AACA;AACA,YAAA,MAAA,SAAA,GAAA,CAAA,GAAA,KAAA,CAAA,MAAA,CAAA;AACA,YAAA,IAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AACA,WAAA;AACA,UAAA,MAAA,IAAAA,iBAAA,CAAA,CAAA,EAAA,eAAA,CAAA,wCAAA,CAAA,EAAA,KAAA,CAAA,CAAA;AACA,SAAA;AACA;AACA,QAAA,MAAA,OAAA,GAAA,KAAA,IAAA,KAAA,CAAA,UAAA,EAAA,CAAA;AACA,QAAA,IAAA,CAAA,aAAA,IAAA,OAAA,EAAA;AACA,UAAA,IAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,cAAA,CAAA,CAAA;AACA,SAAA;AACA;AACA,QAAA,IAAA,aAAA,EAAA;AACA,UAAA,MAAA,eAAA;AACA,YAAA,CAAA,cAAA,CAAA,qBAAA,IAAA,cAAA,CAAA,qBAAA,CAAA,yBAAA;AACA,YAAA,CAAA,CAAA;AACA,UAAA,MAAA,cAAA,GAAA,cAAA,CAAA,KAAA,GAAA,cAAA,CAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA;AACA,UAAA,MAAA,gBAAA,GAAA,eAAA,GAAA,cAAA,CAAA;AACA,UAAA,IAAA,gBAAA,GAAA,CAAA,EAAA;AACA,YAAA,IAAA,CAAA,kBAAA,CAAA,aAAA,EAAA,MAAA,EAAA,gBAAA,CAAA,CAAA;AACA,WAAA;AACA,SAAA;AACA;AACA;AACA;AACA;AACA,QAAA,MAAA,eAAA,GAAA,cAAA,CAAA,gBAAA,CAAA;AACA,QAAA,IAAA,aAAA,IAAA,eAAA,IAAA,cAAA,CAAA,WAAA,KAAA,KAAA,CAAA,WAAA,EAAA;AACA,UAAA,MAAA,MAAA,GAAA,QAAA,CAAA;AACA,UAAA,cAAA,CAAA,gBAAA,GAAA;AACA,YAAA,GAAA,eAAA;AACA,YAAA,MAAA;AACA,WAAA,CAAA;AACA,SAAA;AACA;AACA,QAAA,IAAA,CAAA,SAAA,CAAA,cAAA,EAAA,IAAA,CAAA,CAAA;AACA,QAAA,OAAA,cAAA,CAAA;AACA,OAAA,CAAA;AACA,OAAA,IAAA,CAAA,IAAA,EAAA,MAAA,IAAA;AACA,QAAA,IAAA,MAAA,YAAAA,iBAAA,EAAA;AACA,UAAA,MAAA,MAAA,CAAA;AACA,SAAA;AACA;AACA,QAAA,IAAA,CAAA,gBAAA,CAAA,MAAA,EAAA;AACA,UAAA,IAAA,EAAA;AACA,YAAA,UAAA,EAAA,IAAA;AACA,WAAA;AACA,UAAA,iBAAA,EAAA,MAAA;AACA,SAAA,CAAA,CAAA;AACA,QAAA,MAAA,IAAAA,iBAAA;AACA,UAAA,CAAA,2HAAA,EAAA,MAAA,CAAA,CAAA;AACA,SAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA,GAAA,QAAA,CAAA,OAAA,EAAA;AACA,IAAA,IAAA,CAAA,cAAA,EAAA,CAAA;AACA,IAAA,KAAA,OAAA,CAAA,IAAA;AACA,MAAA,KAAA,IAAA;AACA,QAAA,IAAA,CAAA,cAAA,EAAA,CAAA;AACA,QAAA,OAAA,KAAA,CAAA;AACA,OAAA;AACA,MAAA,MAAA,IAAA;AACA,QAAA,IAAA,CAAA,cAAA,EAAA,CAAA;AACA,QAAA,OAAA,MAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA,GAAA,aAAA,CAAA,QAAA,EAAA;AACA,IAAA,IAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,QAAA,CAAA,CAAA;AACA;AACA,IAAA,IAAA,IAAA,CAAA,UAAA,EAAA,IAAA,IAAA,CAAA,UAAA,EAAA;AACA,MAAA,OAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,MAAA,IAAA;AACA,QAAAxB,sBAAA,IAAAC,YAAA,CAAA,KAAA,CAAA,4BAAA,EAAA,MAAA,CAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA,MAAA;AACA,MAAAD,sBAAA,IAAAC,YAAA,CAAA,KAAA,CAAA,oBAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA,GAAA,cAAA,GAAA;AACA,IAAA,MAAA,QAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,IAAA,IAAA,CAAA,SAAA,GAAA,EAAA,CAAA;AACA,IAAA,OAAA,MAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA;AACA,MAAA,MAAA,CAAA,MAAA,EAAA,QAAA,CAAA,GAAA,GAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AACA,MAAA,OAAA;AACA,QAAA,MAAA;AACA,QAAA,QAAA;AACA,QAAA,QAAA,EAAA,QAAA,CAAA,GAAA,CAAA;AACA,OAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA;;AAYA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,yBAAA;AACA,EAAA,gBAAA;AACA,EAAA,eAAA;AACA,EAAA;AACA,EAAA,MAAA,iBAAA,GAAA,CAAA,EAAA,eAAA,CAAA,uCAAA,CAAA,CAAA;AACA,EAAA,IAAAwB,gBAAA,CAAA,gBAAA,CAAA,EAAA;AACA,IAAA,OAAA,gBAAA,CAAA,IAAA;AACA,MAAA,KAAA,IAAA;AACA,QAAA,IAAA,CAAAC,mBAAA,CAAA,KAAA,CAAA,IAAA,KAAA,KAAA,IAAA,EAAA;AACA,UAAA,MAAA,IAAAF,iBAAA,CAAA,iBAAA,CAAA,CAAA;AACA,SAAA;AACA,QAAA,OAAA,KAAA,CAAA;AACA,OAAA;AACA,MAAA,CAAA,IAAA;AACA,QAAA,MAAA,IAAAA,iBAAA,CAAA,CAAA,EAAA,eAAA,CAAA,eAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA;AACA,GAAA,MAAA,IAAA,CAAAE,mBAAA,CAAA,gBAAA,CAAA,IAAA,gBAAA,KAAA,IAAA,EAAA;AACA,IAAA,MAAA,IAAAF,iBAAA,CAAA,iBAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,OAAA,gBAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,iBAAA;AACA,EAAA,OAAA;AACA,EAAA,KAAA;AACA,EAAA,IAAA;AACA,EAAA;AACA,EAAA,MAAA,EAAA,UAAA,EAAA,qBAAA,EAAA,GAAA,OAAA,CAAA;AACA;AACA,EAAA,IAAA,YAAA,CAAA,KAAA,CAAA,IAAA,UAAA,EAAA;AACA,IAAA,OAAA,UAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,kBAAA,CAAA,KAAA,CAAA,IAAA,qBAAA,EAAA;AACA,IAAA,IAAA,KAAA,CAAA,KAAA,EAAA;AACA;AACA;AACA,MAAA,MAAA,eAAA,GAAA,KAAA,CAAA,KAAA,CAAA,MAAA,CAAA;AACA,MAAA,KAAA,CAAA,qBAAA,GAAA;AACA,QAAA,GAAA,KAAA,CAAA,qBAAA;AACA,QAAA,yBAAA,EAAA,eAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA,IAAA,OAAA,qBAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,YAAA,CAAA,KAAA,EAAA;AACA,EAAA,OAAA,KAAA,CAAA,IAAA,KAAA,SAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,kBAAA,CAAA,KAAA,EAAA;AACA,EAAA,OAAA,KAAA,CAAA,IAAA,KAAA,aAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,iBAAA,CAAA,QAAA,EAAA;AACA,EAAA,MAAA,MAAA,GAAAG,mBAAA,EAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAA,MAAA,IAAA,CAAA,MAAA,CAAA,iBAAA,EAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,CAAA;AACA;;;;;"}