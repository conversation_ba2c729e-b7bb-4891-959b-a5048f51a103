{"version": 3, "file": "index.js", "sources": ["../../../../src/integrations/hapi/index.ts"], "sourcesContent": ["import {\n  SDK_VERSION,\n  captureException,\n  continueTrace,\n  convertIntegrationFnToClass,\n  defineIntegration,\n  getActiveTransaction,\n  getCurrentScope,\n  getDynamicSamplingContextFromSpan,\n  setHttpStatus,\n  spanToTraceHeader,\n  startTransaction,\n} from '@sentry/core';\nimport type { IntegrationFn } from '@sentry/types';\nimport { dynamicSamplingContextToSentryBaggageHeader, fill } from '@sentry/utils';\n\nimport type { Boom, RequestEvent, ResponseObject, Server } from './types';\n\nfunction isResponseObject(response: ResponseObject | Boom): response is ResponseObject {\n  return response && (response as ResponseObject).statusCode !== undefined;\n}\n\nfunction isErrorEvent(event: RequestEvent): event is RequestEvent {\n  return event && (event as RequestEvent).error !== undefined;\n}\n\nfunction sendErrorToSentry(errorData: object): void {\n  captureException(errorData, {\n    mechanism: {\n      type: 'hapi',\n      handled: false,\n      data: {\n        function: 'hapiErrorPlugin',\n      },\n    },\n  });\n}\n\nexport const hapiErrorPlugin = {\n  name: 'SentryHapiErrorPlugin',\n  version: SDK_VERSION,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  register: async function (serverArg: Record<any, any>) {\n    const server = serverArg as unknown as Server;\n\n    server.events.on('request', (request, event) => {\n      // eslint-disable-next-line deprecation/deprecation\n      const transaction = getActiveTransaction();\n\n      if (isErrorEvent(event)) {\n        sendErrorToSentry(event.error);\n      }\n\n      if (transaction) {\n        transaction.setStatus('internal_error');\n        transaction.end();\n      }\n    });\n  },\n};\n\nexport const hapiTracingPlugin = {\n  name: 'SentryHapiTracingPlugin',\n  version: SDK_VERSION,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  register: async function (serverArg: Record<any, any>) {\n    const server = serverArg as unknown as Server;\n\n    server.ext('onPreHandler', (request, h) => {\n      const transaction = continueTrace(\n        {\n          sentryTrace: request.headers['sentry-trace'] || undefined,\n          baggage: request.headers['baggage'] || undefined,\n        },\n        transactionContext => {\n          // eslint-disable-next-line deprecation/deprecation\n          return startTransaction({\n            ...transactionContext,\n            op: 'hapi.request',\n            name: request.route.path,\n            description: `${request.route.method} ${request.path}`,\n          });\n        },\n      );\n\n      // eslint-disable-next-line deprecation/deprecation\n      getCurrentScope().setSpan(transaction);\n\n      return h.continue;\n    });\n\n    server.ext('onPreResponse', (request, h) => {\n      // eslint-disable-next-line deprecation/deprecation\n      const transaction = getActiveTransaction();\n\n      if (request.response && isResponseObject(request.response) && transaction) {\n        const response = request.response as ResponseObject;\n        response.header('sentry-trace', spanToTraceHeader(transaction));\n\n        const dynamicSamplingContext = dynamicSamplingContextToSentryBaggageHeader(\n          getDynamicSamplingContextFromSpan(transaction),\n        );\n\n        if (dynamicSamplingContext) {\n          response.header('baggage', dynamicSamplingContext);\n        }\n      }\n\n      return h.continue;\n    });\n\n    server.ext('onPostHandler', (request, h) => {\n      // eslint-disable-next-line deprecation/deprecation\n      const transaction = getActiveTransaction();\n\n      if (transaction) {\n        if (request.response && isResponseObject(request.response)) {\n          setHttpStatus(transaction, request.response.statusCode);\n        }\n\n        transaction.end();\n      }\n\n      return h.continue;\n    });\n  },\n};\n\nexport type HapiOptions = {\n  /** Hapi server instance */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  server?: Record<any, any>;\n};\n\nconst INTEGRATION_NAME = 'Hapi';\n\nconst _hapiIntegration = ((options: HapiOptions = {}) => {\n  const server = options.server as undefined | Server;\n\n  return {\n    name: INTEGRATION_NAME,\n    setupOnce() {\n      if (!server) {\n        return;\n      }\n\n      fill(server, 'start', (originalStart: () => void) => {\n        return async function (this: Server) {\n          await this.register(hapiTracingPlugin);\n          await this.register(hapiErrorPlugin);\n          const result = originalStart.apply(this);\n          return result;\n        };\n      });\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const hapiIntegration = defineIntegration(_hapiIntegration);\n\n/**\n * Hapi Framework Integration.\n * @deprecated Use `hapiIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const Hapi = convertIntegrationFnToClass(INTEGRATION_NAME, hapiIntegration);\n\n// eslint-disable-next-line deprecation/deprecation\nexport type Hapi = typeof Hapi;\n"], "names": ["captureException", "SDK_VERSION", "getActiveTransaction", "continueTrace", "startTransaction", "getCurrentScope", "spanToTraceHeader", "dynamicSamplingContextToSentryBaggageHeader", "getDynamicSamplingContextFromSpan", "setHttpStatus", "fill", "defineIntegration", "convertIntegrationFnToClass"], "mappings": ";;;;;AAkBA,SAAS,gBAAgB,CAAC,QAAQ,EAAqD;AACvF,EAAE,OAAO,YAAY,CAAC,WAA4B,UAAW,KAAI,SAAS,CAAA;AAC1E,CAAA;AACA;AACA,SAAS,YAAY,CAAC,KAAK,EAAuC;AAClE,EAAE,OAAO,SAAS,CAAC,QAAuB,KAAM,KAAI,SAAS,CAAA;AAC7D,CAAA;AACA;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAgB;AACpD,EAAEA,qBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,SAAS,EAAE;AACf,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ,EAAE,iBAAiB;AACnC,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAA;AACJ,CAAA;AACA;AACO,MAAM,kBAAkB;AAC/B,EAAE,IAAI,EAAE,uBAAuB;AAC/B,EAAE,OAAO,EAAEC,gBAAW;AACtB;AACA,EAAE,QAAQ,EAAE,gBAAgB,SAAS,EAAoB;AACzD,IAAI,MAAM,MAAO,GAAE,SAAU,EAAA;AAC7B;AACA,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,KAAK,KAAK;AACpD;AACA,MAAM,MAAM,WAAA,GAAcC,yBAAoB,EAAE,CAAA;AAChD;AACA,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AAC/B,QAAQ,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;AACtC,OAAM;AACN;AACA,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAA;AAC/C,QAAQ,WAAW,CAAC,GAAG,EAAE,CAAA;AACzB,OAAM;AACN,KAAK,CAAC,CAAA;AACN,GAAG;AACH,EAAC;AACD;AACO,MAAM,oBAAoB;AACjC,EAAE,IAAI,EAAE,yBAAyB;AACjC,EAAE,OAAO,EAAED,gBAAW;AACtB;AACA,EAAE,QAAQ,EAAE,gBAAgB,SAAS,EAAoB;AACzD,IAAI,MAAM,MAAO,GAAE,SAAU,EAAA;AAC7B;AACA,IAAI,MAAM,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK;AAC/C,MAAM,MAAM,WAAY,GAAEE,kBAAa;AACvC,QAAQ;AACR,UAAU,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAE,IAAG,SAAS;AACnE,UAAU,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAE,IAAG,SAAS;AAC1D,SAAS;AACT,QAAQ,sBAAsB;AAC9B;AACA,UAAU,OAAOC,qBAAgB,CAAC;AAClC,YAAY,GAAG,kBAAkB;AACjC,YAAY,EAAE,EAAE,cAAc;AAC9B,YAAY,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI;AACpC,YAAY,WAAW,EAAE,CAAC,EAAA,OAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,EAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AACA,WAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA,CAAA;AACA;AACA;AACA,MAAAC,oBAAA,EAAA,CAAA,OAAA,CAAA,WAAA,CAAA,CAAA;AACA;AACA,MAAA,OAAA,CAAA,CAAA,QAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA;AACA,IAAA,MAAA,CAAA,GAAA,CAAA,eAAA,EAAA,CAAA,OAAA,EAAA,CAAA,KAAA;AACA;AACA,MAAA,MAAA,WAAA,GAAAH,yBAAA,EAAA,CAAA;AACA;AACA,MAAA,IAAA,OAAA,CAAA,QAAA,IAAA,gBAAA,CAAA,OAAA,CAAA,QAAA,CAAA,IAAA,WAAA,EAAA;AACA,QAAA,MAAA,QAAA,GAAA,OAAA,CAAA,QAAA,EAAA;AACA,QAAA,QAAA,CAAA,MAAA,CAAA,cAAA,EAAAI,sBAAA,CAAA,WAAA,CAAA,CAAA,CAAA;AACA;AACA,QAAA,MAAA,sBAAA,GAAAC,iDAAA;AACA,UAAAC,sCAAA,CAAA,WAAA,CAAA;AACA,SAAA,CAAA;AACA;AACA,QAAA,IAAA,sBAAA,EAAA;AACA,UAAA,QAAA,CAAA,MAAA,CAAA,SAAA,EAAA,sBAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA;AACA;AACA,MAAA,OAAA,CAAA,CAAA,QAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA;AACA,IAAA,MAAA,CAAA,GAAA,CAAA,eAAA,EAAA,CAAA,OAAA,EAAA,CAAA,KAAA;AACA;AACA,MAAA,MAAA,WAAA,GAAAN,yBAAA,EAAA,CAAA;AACA;AACA,MAAA,IAAA,WAAA,EAAA;AACA,QAAA,IAAA,OAAA,CAAA,QAAA,IAAA,gBAAA,CAAA,OAAA,CAAA,QAAA,CAAA,EAAA;AACA,UAAAO,kBAAA,CAAA,WAAA,EAAA,OAAA,CAAA,QAAA,CAAA,UAAA,CAAA,CAAA;AACA,SAAA;AACA;AACA,QAAA,WAAA,CAAA,GAAA,EAAA,CAAA;AACA,OAAA;AACA;AACA,MAAA,OAAA,CAAA,CAAA,QAAA,CAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA;;AAQA,MAAA,gBAAA,GAAA,MAAA,CAAA;AACA;AACA,MAAA,gBAAA,IAAA,CAAA,OAAA,GAAA,EAAA,KAAA;AACA,EAAA,MAAA,MAAA,GAAA,OAAA,CAAA,MAAA,EAAA;AACA;AACA,EAAA,OAAA;AACA,IAAA,IAAA,EAAA,gBAAA;AACA,IAAA,SAAA,GAAA;AACA,MAAA,IAAA,CAAA,MAAA,EAAA;AACA,QAAA,OAAA;AACA,OAAA;AACA;AACA,MAAAC,UAAA,CAAA,MAAA,EAAA,OAAA,EAAA,CAAA,aAAA,KAAA;AACA,QAAA,OAAA,kBAAA;AACA,UAAA,MAAA,IAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,CAAA;AACA,UAAA,MAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA;AACA,UAAA,MAAA,MAAA,GAAA,aAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACA,UAAA,OAAA,MAAA,CAAA;AACA,SAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA;AACA,CAAA,CAAA,EAAA;AACA;AACA,MAAA,eAAA,GAAAC,sBAAA,CAAA,gBAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,IAAA,GAAAC,gCAAA,CAAA,gBAAA,EAAA,eAAA,EAAA;AACA;AACA;;;;;;;"}