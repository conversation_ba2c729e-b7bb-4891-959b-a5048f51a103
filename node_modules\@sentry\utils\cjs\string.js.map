{"version": 3, "file": "string.js", "sources": ["../../src/string.ts"], "sourcesContent": ["import { isRegExp, isString, isVueViewModel } from './is';\n\nexport { escapeStringForRegex } from './vendor/escapeStringForRegex';\n\n/**\n * Truncates given string to the maximum characters count\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string (0 = unlimited)\n * @returns string Encoded\n */\nexport function truncate(str: string, max: number = 0): string {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.slice(0, max)}...`;\n}\n\n/**\n * This is basically just `trim_line` from\n * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string\n * @returns string Encoded\n */\nexport function snipLine(line: string, colno: number): string {\n  let newLine = line;\n  const lineLength = newLine.length;\n  if (lineLength <= 150) {\n    return newLine;\n  }\n  if (colno > lineLength) {\n    // eslint-disable-next-line no-param-reassign\n    colno = lineLength;\n  }\n\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n\n  let end = Math.min(start + 140, lineLength);\n  if (end > lineLength - 5) {\n    end = lineLength;\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0);\n  }\n\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `'{snip} ${newLine}`;\n  }\n  if (end < lineLength) {\n    newLine += ' {snip}';\n  }\n\n  return newLine;\n}\n\n/**\n * Join values in array\n * @param input array of values to be joined together\n * @param delimiter string to be placed in-between values\n * @returns Joined values\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function safeJoin(input: any[], delimiter?: string): string {\n  if (!Array.isArray(input)) {\n    return '';\n  }\n\n  const output = [];\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < input.length; i++) {\n    const value = input[i];\n    try {\n      // This is a hack to fix a Vue3-specific bug that causes an infinite loop of\n      // console warnings. This happens when a Vue template is rendered with\n      // an undeclared variable, which we try to stringify, ultimately causing\n      // Vue to issue another warning which repeats indefinitely.\n      // see: https://github.com/getsentry/sentry-javascript/pull/8981\n      if (isVueViewModel(value)) {\n        output.push('[VueViewModel]');\n      } else {\n        output.push(String(value));\n      }\n    } catch (e) {\n      output.push('[value cannot be serialized]');\n    }\n  }\n\n  return output.join(delimiter);\n}\n\n/**\n * Checks if the given value matches a regex or string\n *\n * @param value The string to test\n * @param pattern Either a regex or a string against which `value` will be matched\n * @param requireExactStringMatch If true, `value` must match `pattern` exactly. If false, `value` will match\n * `pattern` if it contains `pattern`. Only applies to string-type patterns.\n */\nexport function isMatchingPattern(\n  value: string,\n  pattern: RegExp | string,\n  requireExactStringMatch: boolean = false,\n): boolean {\n  if (!isString(value)) {\n    return false;\n  }\n\n  if (isRegExp(pattern)) {\n    return pattern.test(value);\n  }\n  if (isString(pattern)) {\n    return requireExactStringMatch ? value === pattern : value.includes(pattern);\n  }\n\n  return false;\n}\n\n/**\n * Test the given string against an array of strings and regexes. By default, string matching is done on a\n * substring-inclusion basis rather than a strict equality basis\n *\n * @param testString The string to test\n * @param patterns The patterns against which to test the string\n * @param requireExactStringMatch If true, `testString` must match one of the given string patterns exactly in order to\n * count. If false, `testString` will match a string pattern if it contains that pattern.\n * @returns\n */\nexport function stringMatchesSomePattern(\n  testString: string,\n  patterns: Array<string | RegExp> = [],\n  requireExactStringMatch: boolean = false,\n): boolean {\n  return patterns.some(pattern => isMatchingPattern(testString, pattern, requireExactStringMatch));\n}\n"], "names": ["isVueViewModel", "isString", "isRegExp"], "mappings": ";;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,QAAQ,CAAC,GAAG,EAAU,GAAG,GAAW,CAAC,EAAU;AAC/D,EAAE,IAAI,OAAO,GAAI,KAAI,YAAY,GAAA,KAAQ,CAAC,EAAE;AAC5C,IAAI,OAAO,GAAG,CAAA;AACd,GAAE;AACF,EAAE,OAAO,GAAG,CAAC,MAAO,IAAG,MAAM,GAAA,GAAM,CAAC,EAAA,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,QAAA,CAAA,IAAA,EAAA,KAAA,EAAA;AACA,EAAA,IAAA,OAAA,GAAA,IAAA,CAAA;AACA,EAAA,MAAA,UAAA,GAAA,OAAA,CAAA,MAAA,CAAA;AACA,EAAA,IAAA,UAAA,IAAA,GAAA,EAAA;AACA,IAAA,OAAA,OAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,KAAA,GAAA,UAAA,EAAA;AACA;AACA,IAAA,KAAA,GAAA,UAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,KAAA,GAAA,IAAA,CAAA,GAAA,CAAA,KAAA,GAAA,EAAA,EAAA,CAAA,CAAA,CAAA;AACA,EAAA,IAAA,KAAA,GAAA,CAAA,EAAA;AACA,IAAA,KAAA,GAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,KAAA,GAAA,GAAA,EAAA,UAAA,CAAA,CAAA;AACA,EAAA,IAAA,GAAA,GAAA,UAAA,GAAA,CAAA,EAAA;AACA,IAAA,GAAA,GAAA,UAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,GAAA,KAAA,UAAA,EAAA;AACA,IAAA,KAAA,GAAA,IAAA,CAAA,GAAA,CAAA,GAAA,GAAA,GAAA,EAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,GAAA,OAAA,CAAA,KAAA,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AACA,EAAA,IAAA,KAAA,GAAA,CAAA,EAAA;AACA,IAAA,OAAA,GAAA,CAAA,QAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,GAAA,GAAA,UAAA,EAAA;AACA,IAAA,OAAA,IAAA,SAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,OAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,QAAA,CAAA,KAAA,EAAA,SAAA,EAAA;AACA,EAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,EAAA;AACA,IAAA,OAAA,EAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,MAAA,GAAA,EAAA,CAAA;AACA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,MAAA,KAAA,GAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA;AACA;AACA;AACA;AACA;AACA;AACA,MAAA,IAAAA,iBAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA;AACA,OAAA,MAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA,OAAA,CAAA,EAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,8BAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,iBAAA;AACA,EAAA,KAAA;AACA,EAAA,OAAA;AACA,EAAA,uBAAA,GAAA,KAAA;AACA,EAAA;AACA,EAAA,IAAA,CAAAC,WAAA,CAAA,KAAA,CAAA,EAAA;AACA,IAAA,OAAA,KAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAAC,WAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,OAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAAD,WAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,OAAA,uBAAA,GAAA,KAAA,KAAA,OAAA,GAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,wBAAA;AACA,EAAA,UAAA;AACA,EAAA,QAAA,GAAA,EAAA;AACA,EAAA,uBAAA,GAAA,KAAA;AACA,EAAA;AACA,EAAA,OAAA,QAAA,CAAA,IAAA,CAAA,OAAA,IAAA,iBAAA,CAAA,UAAA,EAAA,OAAA,EAAA,uBAAA,CAAA,CAAA,CAAA;AACA;;;;;;;;"}