{"version": 3, "file": "index.js", "sources": ["../../../src/integrations/index.ts"], "sourcesContent": ["/* eslint-disable deprecation/deprecation */\nexport { Console } from './console';\nexport { Http } from './http';\nexport { OnUncaughtException } from './onuncaughtexception';\nexport { OnUnhandledRejection } from './onunhandledrejection';\nexport { Modules } from './modules';\nexport { ContextLines } from './contextlines';\nexport { Context } from './context';\nexport { RequestData } from '@sentry/core';\nexport { LocalVariables } from './local-variables';\nexport { Undici } from './undici';\nexport { Spotlight } from './spotlight';\nexport { Anr } from './anr';\nexport { Hapi } from './hapi';\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;"}