{"version": 3, "file": "envelope.js", "sources": ["../../src/envelope.ts"], "sourcesContent": ["import type {\n  Attachment,\n  AttachmentItem,\n  BaseEnvelopeHeaders,\n  BaseEnvelopeItemHeaders,\n  DataCategory,\n  DsnComponents,\n  Envelope,\n  EnvelopeItemType,\n  Event,\n  EventEnvelopeHeaders,\n  SdkInfo,\n  SdkMetadata,\n  TextEncoderInternal,\n} from '@sentry/types';\n\nimport { dsnToString } from './dsn';\nimport { normalize } from './normalize';\nimport { dropUndefinedKeys } from './object';\n\n/**\n * Creates an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function createEnvelope<E extends Envelope>(headers: E[0], items: E[1] = []): E {\n  return [headers, items] as E;\n}\n\n/**\n * Add an item to an envelope.\n * Make sure to always explicitly provide the generic to this function\n * so that the envelope types resolve correctly.\n */\nexport function addItemToEnvelope<E extends Envelope>(envelope: E, newItem: E[1][number]): E {\n  const [headers, items] = envelope;\n  return [headers, [...items, newItem]] as unknown as E;\n}\n\n/**\n * Convenience function to loop through the items and item types of an envelope.\n * (This function was mostly created because working with envelope types is painful at the moment)\n *\n * If the callback returns true, the rest of the items will be skipped.\n */\nexport function forEachEnvelopeItem<E extends Envelope>(\n  envelope: Envelope,\n  callback: (envelopeItem: E[1][number], envelopeItemType: E[1][number][0]['type']) => boolean | void,\n): boolean {\n  const envelopeItems = envelope[1];\n\n  for (const envelopeItem of envelopeItems) {\n    const envelopeItemType = envelopeItem[0].type;\n    const result = callback(envelopeItem, envelopeItemType);\n\n    if (result) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Returns true if the envelope contains any of the given envelope item types\n */\nexport function envelopeContainsItemType(envelope: Envelope, types: EnvelopeItemType[]): boolean {\n  return forEachEnvelopeItem(envelope, (_, type) => types.includes(type));\n}\n\n/**\n * Encode a string to UTF8.\n */\nfunction encodeUTF8(input: string, textEncoder?: TextEncoderInternal): Uint8Array {\n  const utf8 = textEncoder || new TextEncoder();\n  return utf8.encode(input);\n}\n\n/**\n * Serializes an envelope.\n */\nexport function serializeEnvelope(envelope: Envelope, textEncoder?: TextEncoderInternal): string | Uint8Array {\n  const [envHeaders, items] = envelope;\n\n  // Initially we construct our envelope as a string and only convert to binary chunks if we encounter binary data\n  let parts: string | Uint8Array[] = JSON.stringify(envHeaders);\n\n  function append(next: string | Uint8Array): void {\n    if (typeof parts === 'string') {\n      parts = typeof next === 'string' ? parts + next : [encodeUTF8(parts, textEncoder), next];\n    } else {\n      parts.push(typeof next === 'string' ? encodeUTF8(next, textEncoder) : next);\n    }\n  }\n\n  for (const item of items) {\n    const [itemHeaders, payload] = item;\n\n    append(`\\n${JSON.stringify(itemHeaders)}\\n`);\n\n    if (typeof payload === 'string' || payload instanceof Uint8Array) {\n      append(payload);\n    } else {\n      let stringifiedPayload: string;\n      try {\n        stringifiedPayload = JSON.stringify(payload);\n      } catch (e) {\n        // In case, despite all our efforts to keep `payload` circular-dependency-free, `JSON.strinify()` still\n        // fails, we try again after normalizing it again with infinite normalization depth. This of course has a\n        // performance impact but in this case a performance hit is better than throwing.\n        stringifiedPayload = JSON.stringify(normalize(payload));\n      }\n      append(stringifiedPayload);\n    }\n  }\n\n  return typeof parts === 'string' ? parts : concatBuffers(parts);\n}\n\nfunction concatBuffers(buffers: Uint8Array[]): Uint8Array {\n  const totalLength = buffers.reduce((acc, buf) => acc + buf.length, 0);\n\n  const merged = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const buffer of buffers) {\n    merged.set(buffer, offset);\n    offset += buffer.length;\n  }\n\n  return merged;\n}\n\nexport interface TextDecoderInternal {\n  decode(input?: Uint8Array): string;\n}\n\n/**\n * Parses an envelope\n */\nexport function parseEnvelope(\n  env: string | Uint8Array,\n  textEncoder: TextEncoderInternal,\n  textDecoder: TextDecoderInternal,\n): Envelope {\n  let buffer = typeof env === 'string' ? textEncoder.encode(env) : env;\n\n  function readBinary(length: number): Uint8Array {\n    const bin = buffer.subarray(0, length);\n    // Replace the buffer with the remaining data excluding trailing newline\n    buffer = buffer.subarray(length + 1);\n    return bin;\n  }\n\n  function readJson<T>(): T {\n    let i = buffer.indexOf(0xa);\n    // If we couldn't find a newline, we must have found the end of the buffer\n    if (i < 0) {\n      i = buffer.length;\n    }\n\n    return JSON.parse(textDecoder.decode(readBinary(i))) as T;\n  }\n\n  const envelopeHeader = readJson<BaseEnvelopeHeaders>();\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const items: [any, any][] = [];\n\n  while (buffer.length) {\n    const itemHeader = readJson<BaseEnvelopeItemHeaders>();\n    const binaryLength = typeof itemHeader.length === 'number' ? itemHeader.length : undefined;\n\n    items.push([itemHeader, binaryLength ? readBinary(binaryLength) : readJson()]);\n  }\n\n  return [envelopeHeader, items];\n}\n\n/**\n * Creates attachment envelope items\n */\nexport function createAttachmentEnvelopeItem(\n  attachment: Attachment,\n  textEncoder?: TextEncoderInternal,\n): AttachmentItem {\n  const buffer = typeof attachment.data === 'string' ? encodeUTF8(attachment.data, textEncoder) : attachment.data;\n\n  return [\n    dropUndefinedKeys({\n      type: 'attachment',\n      length: buffer.length,\n      filename: attachment.filename,\n      content_type: attachment.contentType,\n      attachment_type: attachment.attachmentType,\n    }),\n    buffer,\n  ];\n}\n\nconst ITEM_TYPE_TO_DATA_CATEGORY_MAP: Record<EnvelopeItemType, DataCategory> = {\n  session: 'session',\n  sessions: 'session',\n  attachment: 'attachment',\n  transaction: 'transaction',\n  event: 'error',\n  client_report: 'internal',\n  user_report: 'default',\n  profile: 'profile',\n  replay_event: 'replay',\n  replay_recording: 'replay',\n  check_in: 'monitor',\n  feedback: 'feedback',\n  span: 'span',\n  statsd: 'metric_bucket',\n};\n\n/**\n * Maps the type of an envelope item to a data category.\n */\nexport function envelopeItemTypeToDataCategory(type: EnvelopeItemType): DataCategory {\n  return ITEM_TYPE_TO_DATA_CATEGORY_MAP[type];\n}\n\n/** Extracts the minimal SDK info from the metadata or an events */\nexport function getSdkMetadataForEnvelopeHeader(metadataOrEvent?: SdkMetadata | Event): SdkInfo | undefined {\n  if (!metadataOrEvent || !metadataOrEvent.sdk) {\n    return;\n  }\n  const { name, version } = metadataOrEvent.sdk;\n  return { name, version };\n}\n\n/**\n * Creates event envelope headers, based on event, sdk info and tunnel\n * Note: This function was extracted from the core package to make it available in Replay\n */\nexport function createEventEnvelopeHeaders(\n  event: Event,\n  sdkInfo: SdkInfo | undefined,\n  tunnel: string | undefined,\n  dsn?: DsnComponents,\n): EventEnvelopeHeaders {\n  const dynamicSamplingContext = event.sdkProcessingMetadata && event.sdkProcessingMetadata.dynamicSamplingContext;\n  return {\n    event_id: event.event_id as string,\n    sent_at: new Date().toISOString(),\n    ...(sdkInfo && { sdk: sdkInfo }),\n    ...(!!tunnel && dsn && { dsn: dsnToString(dsn) }),\n    ...(dynamicSamplingContext && {\n      trace: dropUndefinedKeys({ ...dynamicSamplingContext }),\n    }),\n  };\n}\n"], "names": ["normalize", "dropUndefinedKeys", "dsn", "dsnToString"], "mappings": ";;;;;;AAoBA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAqB,OAAO,EAAQ,KAAK,GAAS,EAAE,EAAK;AACvF,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,CAAE,EAAA;AAC1B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAqB,QAAQ,EAAK,OAAO,EAAmB;AAC7F,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAA,GAAI,QAAQ,CAAA;AACnC,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,KAAK,EAAE,OAAO,CAAC,CAAE,EAAA;AACxC,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB;AACnC,EAAE,QAAQ;AACV,EAAE,QAAQ;AACV,EAAW;AACX,EAAE,MAAM,aAAc,GAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;AACnC;AACA,EAAE,KAAK,MAAM,YAAa,IAAG,aAAa,EAAE;AAC5C,IAAI,MAAM,mBAAmB,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AACjD,IAAI,MAAM,SAAS,QAAQ,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;AAC3D;AACA,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO,IAAI,CAAA;AACjB,KAAI;AACJ,GAAE;AACF;AACA,EAAE,OAAO,KAAK,CAAA;AACd,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,wBAAwB,CAAC,QAAQ,EAAY,KAAK,EAA+B;AACjG,EAAE,OAAO,mBAAmB,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;AACzE,CAAA;AACA;AACA;AACA;AACA;AACA,SAAS,UAAU,CAAC,KAAK,EAAU,WAAW,EAAoC;AAClF,EAAE,MAAM,OAAO,WAAA,IAAe,IAAI,WAAW,EAAE,CAAA;AAC/C,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;AAC3B,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,QAAQ,EAAY,WAAW,EAA6C;AAC9G,EAAE,MAAM,CAAC,UAAU,EAAE,KAAK,CAAA,GAAI,QAAQ,CAAA;AACtC;AACA;AACA,EAAE,IAAI,KAAK,GAA0B,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;AAC/D;AACA,EAAE,SAAS,MAAM,CAAC,IAAI,EAA6B;AACnD,IAAI,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;AACnC,MAAM,KAAA,GAAQ,OAAO,IAAA,KAAS,QAAS,GAAE,QAAQ,IAAA,GAAO,CAAC,UAAU,CAAC,KAAK,EAAE,WAAW,CAAC,EAAE,IAAI,CAAC,CAAA;AAC9F,WAAW;AACX,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,SAAS,QAAA,GAAW,UAAU,CAAC,IAAI,EAAE,WAAW,CAAE,GAAE,IAAI,CAAC,CAAA;AACjF,KAAI;AACJ,GAAE;AACF;AACA,EAAE,KAAK,MAAM,IAAK,IAAG,KAAK,EAAE;AAC5B,IAAI,MAAM,CAAC,WAAW,EAAE,OAAO,CAAA,GAAI,IAAI,CAAA;AACvC;AACA,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAChD;AACA,IAAI,IAAI,OAAO,OAAQ,KAAI,YAAY,OAAA,YAAmB,UAAU,EAAE;AACtE,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA;AACrB,WAAW;AACX,MAAM,IAAI,kBAAkB,CAAA;AAC5B,MAAM,IAAI;AACV,QAAQ,qBAAqB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;AACpD,OAAQ,CAAA,OAAO,CAAC,EAAE;AAClB;AACA;AACA;AACA,QAAQ,kBAAA,GAAqB,IAAI,CAAC,SAAS,CAACA,mBAAS,CAAC,OAAO,CAAC,CAAC,CAAA;AAC/D,OAAM;AACN,MAAM,MAAM,CAAC,kBAAkB,CAAC,CAAA;AAChC,KAAI;AACJ,GAAE;AACF;AACA,EAAE,OAAO,OAAO,KAAA,KAAU,QAAA,GAAW,KAAA,GAAQ,aAAa,CAAC,KAAK,CAAC,CAAA;AACjE,CAAA;AACA;AACA,SAAS,aAAa,CAAC,OAAO,EAA4B;AAC1D,EAAE,MAAM,cAAc,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,MAAM,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAA;AACvE;AACA,EAAE,MAAM,MAAO,GAAE,IAAI,UAAU,CAAC,WAAW,CAAC,CAAA;AAC5C,EAAE,IAAI,MAAO,GAAE,CAAC,CAAA;AAChB,EAAE,KAAK,MAAM,MAAO,IAAG,OAAO,EAAE;AAChC,IAAI,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;AAC9B,IAAI,MAAO,IAAG,MAAM,CAAC,MAAM,CAAA;AAC3B,GAAE;AACF;AACA,EAAE,OAAO,MAAM,CAAA;AACf,CAAA;;AAMA;AACA;AACA;AACO,SAAS,aAAa;AAC7B,EAAE,GAAG;AACL,EAAE,WAAW;AACb,EAAE,WAAW;AACb,EAAY;AACZ,EAAE,IAAI,MAAA,GAAS,OAAO,QAAQ,QAAA,GAAW,WAAW,CAAC,MAAM,CAAC,GAAG,CAAA,GAAI,GAAG,CAAA;AACtE;AACA,EAAE,SAAS,UAAU,CAAC,MAAM,EAAsB;AAClD,IAAI,MAAM,GAAI,GAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAAA;AAC1C;AACA,IAAI,MAAA,GAAS,MAAM,CAAC,QAAQ,CAAC,MAAA,GAAS,CAAC,CAAC,CAAA;AACxC,IAAI,OAAO,GAAG,CAAA;AACd,GAAE;AACF;AACA,EAAE,SAAS,QAAQ,GAAS;AAC5B,IAAI,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;AAC/B;AACA,IAAI,IAAI,CAAE,GAAE,CAAC,EAAE;AACf,MAAM,CAAE,GAAE,MAAM,CAAC,MAAM,CAAA;AACvB,KAAI;AACJ;AACA,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAE,EAAA;AACzD,GAAE;AACF;AACA,EAAE,MAAM,cAAA,GAAiB,QAAQ,EAAuB,CAAA;AACxD;AACA,EAAE,MAAM,KAAK,GAAiB,EAAE,CAAA;AAChC;AACA,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE;AACxB,IAAI,MAAM,UAAA,GAAa,QAAQ,EAA2B,CAAA;AAC1D,IAAI,MAAM,YAAA,GAAe,OAAO,UAAU,CAAC,MAAA,KAAW,QAAA,GAAW,UAAU,CAAC,MAAA,GAAS,SAAS,CAAA;AAC9F;AACA,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,YAAa,GAAE,UAAU,CAAC,YAAY,CAAE,GAAE,QAAQ,EAAE,CAAC,CAAC,CAAA;AAClF,GAAE;AACF;AACA,EAAE,OAAO,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;AAChC,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,4BAA4B;AAC5C,EAAE,UAAU;AACZ,EAAE,WAAW;AACb,EAAkB;AAClB,EAAE,MAAM,SAAS,OAAO,UAAU,CAAC,IAAA,KAAS,QAAS,GAAE,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,CAAE,GAAE,UAAU,CAAC,IAAI,CAAA;AACjH;AACA,EAAE,OAAO;AACT,IAAIC,wBAAiB,CAAC;AACtB,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,MAAM,EAAE,MAAM,CAAC,MAAM;AAC3B,MAAM,QAAQ,EAAE,UAAU,CAAC,QAAQ;AACnC,MAAM,YAAY,EAAE,UAAU,CAAC,WAAW;AAC1C,MAAM,eAAe,EAAE,UAAU,CAAC,cAAc;AAChD,KAAK,CAAC;AACN,IAAI,MAAM;AACV,GAAG,CAAA;AACH,CAAA;AACA;AACA,MAAM,8BAA8B,GAA2C;AAC/E,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,UAAU,EAAE,YAAY;AAC1B,EAAE,WAAW,EAAE,aAAa;AAC5B,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,aAAa,EAAE,UAAU;AAC3B,EAAE,WAAW,EAAE,SAAS;AACxB,EAAE,OAAO,EAAE,SAAS;AACpB,EAAE,YAAY,EAAE,QAAQ;AACxB,EAAE,gBAAgB,EAAE,QAAQ;AAC5B,EAAE,QAAQ,EAAE,SAAS;AACrB,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,MAAM,EAAE,eAAe;AACzB,CAAC,CAAA;AACD;AACA;AACA;AACA;AACO,SAAS,8BAA8B,CAAC,IAAI,EAAkC;AACrF,EAAE,OAAO,8BAA8B,CAAC,IAAI,CAAC,CAAA;AAC7C,CAAA;AACA;AACA;AACO,SAAS,+BAA+B,CAAC,eAAe,EAA6C;AAC5G,EAAE,IAAI,CAAC,eAAA,IAAmB,CAAC,eAAe,CAAC,GAAG,EAAE;AAChD,IAAI,OAAM;AACV,GAAE;AACF,EAAE,MAAM,EAAE,IAAI,EAAE,SAAU,GAAE,eAAe,CAAC,GAAG,CAAA;AAC/C,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAA;AAC1B,CAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,0BAA0B;AAC1C,EAAE,KAAK;AACP,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAEC,KAAG;AACL,EAAwB;AACxB,EAAE,MAAM,sBAAuB,GAAE,KAAK,CAAC,qBAAsB,IAAG,KAAK,CAAC,qBAAqB,CAAC,sBAAsB,CAAA;AAClH,EAAE,OAAO;AACT,IAAI,QAAQ,EAAE,KAAK,CAAC,QAAS;AAC7B,IAAI,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;AACrC,IAAI,IAAI,OAAQ,IAAG,EAAE,GAAG,EAAE,OAAQ,EAAC,CAAC;AACpC,IAAI,IAAI,CAAC,CAAC,MAAA,IAAUA,KAAI,IAAG,EAAE,GAAG,EAAEC,eAAW,CAACD,KAAG,CAAA,EAAG,CAAC;AACrD,IAAI,IAAI,sBAAA,IAA0B;AAClC,MAAM,KAAK,EAAED,wBAAiB,CAAC,EAAE,GAAG,sBAAA,EAAwB,CAAC;AAC7D,KAAK,CAAC;AACN,GAAG,CAAA;AACH;;;;;;;;;;;;;"}