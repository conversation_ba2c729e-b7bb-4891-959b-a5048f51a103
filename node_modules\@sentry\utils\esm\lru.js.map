{"version": 3, "file": "lru.js", "sources": ["../../src/lru.ts"], "sourcesContent": ["/** A simple Least Recently Used map */\nexport class LRUMap<K, V> {\n  private readonly _cache: Map<K, V>;\n\n  public constructor(private readonly _maxSize: number) {\n    this._cache = new Map<K, V>();\n  }\n\n  /** Get the current size of the cache */\n  public get size(): number {\n    return this._cache.size;\n  }\n\n  /** Get an entry or undefined if it was not in the cache. Re-inserts to update the recently used order */\n  public get(key: K): V | undefined {\n    const value = this._cache.get(key);\n    if (value === undefined) {\n      return undefined;\n    }\n    // Remove and re-insert to update the order\n    this._cache.delete(key);\n    this._cache.set(key, value);\n    return value;\n  }\n\n  /** Insert an entry and evict an older entry if we've reached maxSize */\n  public set(key: K, value: V): void {\n    if (this._cache.size >= this._maxSize) {\n      // keys() returns an iterator in insertion order so keys().next() gives us the oldest key\n      this._cache.delete(this._cache.keys().next().value);\n    }\n    this._cache.set(key, value);\n  }\n\n  /** Remove an entry and return the entry if it was in the cache */\n  public remove(key: K): V | undefined {\n    const value = this._cache.get(key);\n    if (value) {\n      this._cache.delete(key);\n    }\n    return value;\n  }\n\n  /** Clear all entries */\n  public clear(): void {\n    this._cache.clear();\n  }\n\n  /** Get all the keys */\n  public keys(): Array<K> {\n    return Array.from(this._cache.keys());\n  }\n\n  /** Get all the values */\n  public values(): Array<V> {\n    const values: V[] = [];\n    this._cache.forEach(value => values.push(value));\n    return values;\n  }\n}\n"], "names": [], "mappings": "AAAA;AACO,MAAM,MAAM,CAAO;;AAG1B,GAAS,WAAW,GAAkB,QAAQ,EAAU,CAAA,IAAA,CAAA,QAAA,GAAA,QAAA,CAAA;AACxD,IAAI,IAAI,CAAC,MAAA,GAAS,IAAI,GAAG,EAAQ,CAAA;AACjC,GAAE;AACF;AACA;AACA,GAAS,IAAI,IAAI,GAAW;AAC5B,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAA;AAC3B,GAAE;AACF;AACA;AACA,GAAS,GAAG,CAAC,GAAG,EAAoB;AACpC,IAAI,MAAM,KAAM,GAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACtC,IAAI,IAAI,KAAM,KAAI,SAAS,EAAE;AAC7B,MAAM,OAAO,SAAS,CAAA;AACtB,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AAC/B,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA;AACA,GAAS,GAAG,CAAC,GAAG,EAAK,KAAK,EAAW;AACrC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAA,IAAQ,IAAI,CAAC,QAAQ,EAAE;AAC3C;AACA,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAA;AACzD,KAAI;AACJ,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AAC/B,GAAE;AACF;AACA;AACA,GAAS,MAAM,CAAC,GAAG,EAAoB;AACvC,IAAI,MAAM,KAAM,GAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACtC,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AAC7B,KAAI;AACJ,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA;AACA,GAAS,KAAK,GAAS;AACvB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;AACvB,GAAE;AACF;AACA;AACA,GAAS,IAAI,GAAa;AAC1B,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;AACzC,GAAE;AACF;AACA;AACA,GAAS,MAAM,GAAa;AAC5B,IAAI,MAAM,MAAM,GAAQ,EAAE,CAAA;AAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAA,IAAS,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;AACpD,IAAI,OAAO,MAAM,CAAA;AACjB,GAAE;AACF;;;;"}