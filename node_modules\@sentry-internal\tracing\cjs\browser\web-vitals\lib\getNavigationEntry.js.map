{"version": 3, "file": "getNavigationEntry.js", "sources": ["../../../../../src/browser/web-vitals/lib/getNavigationEntry.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../types';\nimport type { NavigationTimingPolyfillEntry } from '../types';\n\nconst getNavigationEntryFromPerformanceTiming = (): NavigationTimingPolyfillEntry => {\n  // eslint-disable-next-line deprecation/deprecation\n  const timing = WINDOW.performance.timing;\n  // eslint-disable-next-line deprecation/deprecation\n  const type = WINDOW.performance.navigation.type;\n\n  const navigationEntry: { [key: string]: number | string } = {\n    entryType: 'navigation',\n    startTime: 0,\n    type: type == 2 ? 'back_forward' : type === 1 ? 'reload' : 'navigate',\n  };\n\n  for (const key in timing) {\n    if (key !== 'navigationStart' && key !== 'toJSON') {\n      // eslint-disable-next-line deprecation/deprecation\n      navigationEntry[key] = Math.max((timing[key as keyof PerformanceTiming] as number) - timing.navigationStart, 0);\n    }\n  }\n  return navigationEntry as unknown as NavigationTimingPolyfillEntry;\n};\n\nexport const getNavigationEntry = (): PerformanceNavigationTiming | NavigationTimingPolyfillEntry | undefined => {\n  if (WINDOW.__WEB_VITALS_POLYFILL__) {\n    return (\n      WINDOW.performance &&\n      ((performance.getEntriesByType && performance.getEntriesByType('navigation')[0]) ||\n        getNavigationEntryFromPerformanceTiming())\n    );\n  } else {\n    return WINDOW.performance && performance.getEntriesByType && performance.getEntriesByType('navigation')[0];\n  }\n};\n"], "names": ["WINDOW"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,MAAM,uCAAA,GAA0C,MAAqC;AACrF;AACA,EAAE,MAAM,MAAO,GAAEA,YAAM,CAAC,WAAW,CAAC,MAAM,CAAA;AAC1C;AACA,EAAE,MAAM,OAAOA,YAAM,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAA;AACjD;AACA,EAAE,MAAM,eAAe,GAAuC;AAC9D,IAAI,SAAS,EAAE,YAAY;AAC3B,IAAI,SAAS,EAAE,CAAC;AAChB,IAAI,IAAI,EAAE,IAAK,IAAG,IAAI,cAAA,GAAiB,IAAA,KAAS,CAAA,GAAI,QAAA,GAAW,UAAU;AACzE,GAAG,CAAA;AACH;AACA,EAAE,KAAK,MAAM,GAAI,IAAG,MAAM,EAAE;AAC5B,IAAI,IAAI,GAAI,KAAI,qBAAqB,GAAA,KAAQ,QAAQ,EAAE;AACvD;AACA,MAAM,eAAe,CAAC,GAAG,CAAA,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAA,OAA6C,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAA;AACrH,KAAI;AACJ,GAAE;AACF,EAAE,OAAO,eAAgB,EAAA;AACzB,CAAC,CAAA;AACD;AACa,MAAA,kBAAA,GAAqB,MAA+E;AACjH,EAAE,IAAIA,YAAM,CAAC,uBAAuB,EAAE;AACtC,IAAI;AACJ,MAAMA,YAAM,CAAC,WAAY;AACzB,OAAO,CAAC,WAAW,CAAC,oBAAoB,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;AACrF,QAAQ,uCAAuC,EAAE,CAAA;AACjD,MAAK;AACL,SAAS;AACT,IAAI,OAAOA,YAAM,CAAC,WAAY,IAAG,WAAW,CAAC,gBAAA,IAAoB,WAAW,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;AAC9G,GAAE;AACF;;;;"}