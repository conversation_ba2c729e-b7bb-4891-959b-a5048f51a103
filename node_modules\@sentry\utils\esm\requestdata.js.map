{"version": 3, "file": "requestdata.js", "sources": ["../../src/requestdata.ts"], "sourcesContent": ["import type {\n  Event,\n  ExtractedNodeRequestData,\n  PolymorphicRequest,\n  Transaction,\n  TransactionSource,\n  WebFetchHeaders,\n  WebFetchRequest,\n} from '@sentry/types';\n\nimport { parseCookie } from './cookie';\nimport { DEBUG_BUILD } from './debug-build';\nimport { isPlainObject, isString } from './is';\nimport { logger } from './logger';\nimport { normalize } from './normalize';\nimport { stripUrlQueryAndFragment } from './url';\n\nconst DEFAULT_INCLUDES = {\n  ip: false,\n  request: true,\n  transaction: true,\n  user: true,\n};\nconst DEFAULT_REQUEST_INCLUDES = ['cookies', 'data', 'headers', 'method', 'query_string', 'url'];\nexport const DEFAULT_USER_INCLUDES = ['id', 'username', 'email'];\n\ntype InjectedNodeDeps = {\n  cookie: {\n    parse: (cookieStr: string) => Record<string, string>;\n  };\n  url: {\n    parse: (urlStr: string) => {\n      query: string | null;\n    };\n  };\n};\n\n/**\n * Options deciding what parts of the request to use when enhancing an event\n */\nexport type AddRequestDataToEventOptions = {\n  /** Flags controlling whether each type of data should be added to the event */\n  include?: {\n    ip?: boolean;\n    request?: boolean | Array<(typeof DEFAULT_REQUEST_INCLUDES)[number]>;\n    transaction?: boolean | TransactionNamingScheme;\n    user?: boolean | Array<(typeof DEFAULT_USER_INCLUDES)[number]>;\n  };\n\n  /** Injected platform-specific dependencies */\n  deps?: {\n    cookie: {\n      parse: (cookieStr: string) => Record<string, string>;\n    };\n    url: {\n      parse: (urlStr: string) => {\n        query: string | null;\n      };\n    };\n  };\n};\n\nexport type TransactionNamingScheme = 'path' | 'methodPath' | 'handler';\n\n/**\n * Sets parameterized route as transaction name e.g.: `GET /users/:id`\n * Also adds more context data on the transaction from the request.\n *\n * @deprecated This utility will be removed in v8.\n */\nexport function addRequestDataToTransaction(\n  transaction: Transaction | undefined,\n  req: PolymorphicRequest,\n  deps?: InjectedNodeDeps,\n): void {\n  if (!transaction) return;\n  // eslint-disable-next-line deprecation/deprecation\n  if (!transaction.metadata.source || transaction.metadata.source === 'url') {\n    // Attempt to grab a parameterized route off of the request\n    const [name, source] = extractPathForTransaction(req, { path: true, method: true });\n    transaction.updateName(name);\n    // TODO: SEMANTIC_ATTRIBUTE_SENTRY_SOURCE is in core, align this once we merge utils & core\n    // eslint-disable-next-line deprecation/deprecation\n    transaction.setMetadata({ source });\n  }\n  transaction.setAttribute('url', req.originalUrl || req.url);\n  if (req.baseUrl) {\n    transaction.setAttribute('baseUrl', req.baseUrl);\n  }\n  // TODO: We need to rewrite this to a flat format?\n  // eslint-disable-next-line deprecation/deprecation\n  transaction.setData('query', extractQueryParams(req, deps));\n}\n\n/**\n * Extracts a complete and parameterized path from the request object and uses it to construct transaction name.\n * If the parameterized transaction name cannot be extracted, we fall back to the raw URL.\n *\n * Additionally, this function determines and returns the transaction name source\n *\n * eg. GET /mountpoint/user/:id\n *\n * @param req A request object\n * @param options What to include in the transaction name (method, path, or a custom route name to be\n *                used instead of the request's route)\n *\n * @returns A tuple of the fully constructed transaction name [0] and its source [1] (can be either 'route' or 'url')\n */\nexport function extractPathForTransaction(\n  req: PolymorphicRequest,\n  options: { path?: boolean; method?: boolean; customRoute?: string } = {},\n): [string, TransactionSource] {\n  const method = req.method && req.method.toUpperCase();\n\n  let path = '';\n  let source: TransactionSource = 'url';\n\n  // Check to see if there's a parameterized route we can use (as there is in Express)\n  if (options.customRoute || req.route) {\n    path = options.customRoute || `${req.baseUrl || ''}${req.route && req.route.path}`;\n    source = 'route';\n  }\n\n  // Otherwise, just take the original URL\n  else if (req.originalUrl || req.url) {\n    path = stripUrlQueryAndFragment(req.originalUrl || req.url || '');\n  }\n\n  let name = '';\n  if (options.method && method) {\n    name += method;\n  }\n  if (options.method && options.path) {\n    name += ' ';\n  }\n  if (options.path && path) {\n    name += path;\n  }\n\n  return [name, source];\n}\n\n/** JSDoc */\nfunction extractTransaction(req: PolymorphicRequest, type: boolean | TransactionNamingScheme): string {\n  switch (type) {\n    case 'path': {\n      return extractPathForTransaction(req, { path: true })[0];\n    }\n    case 'handler': {\n      return (req.route && req.route.stack && req.route.stack[0] && req.route.stack[0].name) || '<anonymous>';\n    }\n    case 'methodPath':\n    default: {\n      // if exist _reconstructedRoute return that path instead of route.path\n      const customRoute = req._reconstructedRoute ? req._reconstructedRoute : undefined;\n      return extractPathForTransaction(req, { path: true, method: true, customRoute })[0];\n    }\n  }\n}\n\n/** JSDoc */\nfunction extractUserData(\n  user: {\n    [key: string]: unknown;\n  },\n  keys: boolean | string[],\n): { [key: string]: unknown } {\n  const extractedUser: { [key: string]: unknown } = {};\n  const attributes = Array.isArray(keys) ? keys : DEFAULT_USER_INCLUDES;\n\n  attributes.forEach(key => {\n    if (user && key in user) {\n      extractedUser[key] = user[key];\n    }\n  });\n\n  return extractedUser;\n}\n\n/**\n * Normalize data from the request object, accounting for framework differences.\n *\n * @param req The request object from which to extract data\n * @param options.include An optional array of keys to include in the normalized data. Defaults to\n * DEFAULT_REQUEST_INCLUDES if not provided.\n * @param options.deps Injected, platform-specific dependencies\n * @returns An object containing normalized request data\n */\nexport function extractRequestData(\n  req: PolymorphicRequest,\n  options?: {\n    include?: string[];\n    deps?: InjectedNodeDeps;\n  },\n): ExtractedNodeRequestData {\n  const { include = DEFAULT_REQUEST_INCLUDES, deps } = options || {};\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const requestData: { [key: string]: any } = {};\n\n  // headers:\n  //   node, express, koa, nextjs: req.headers\n  const headers = (req.headers || {}) as {\n    host?: string;\n    cookie?: string;\n  };\n  // method:\n  //   node, express, koa, nextjs: req.method\n  const method = req.method;\n  // host:\n  //   express: req.hostname in > 4 and req.host in < 4\n  //   koa: req.host\n  //   node, nextjs: req.headers.host\n  // Express 4 mistakenly strips off port number from req.host / req.hostname so we can't rely on them\n  // See: https://github.com/expressjs/express/issues/3047#issuecomment-236653223\n  // Also: https://github.com/getsentry/sentry-javascript/issues/1917\n  const host = headers.host || req.hostname || req.host || '<no host>';\n  // protocol:\n  //   node, nextjs: <n/a>\n  //   express, koa: req.protocol\n  const protocol = req.protocol === 'https' || (req.socket && req.socket.encrypted) ? 'https' : 'http';\n  // url (including path and query string):\n  //   node, express: req.originalUrl\n  //   koa, nextjs: req.url\n  const originalUrl = req.originalUrl || req.url || '';\n  // absolute url\n  const absoluteUrl = originalUrl.startsWith(protocol) ? originalUrl : `${protocol}://${host}${originalUrl}`;\n  include.forEach(key => {\n    switch (key) {\n      case 'headers': {\n        requestData.headers = headers;\n\n        // Remove the Cookie header in case cookie data should not be included in the event\n        if (!include.includes('cookies')) {\n          delete (requestData.headers as { cookie?: string }).cookie;\n        }\n\n        break;\n      }\n      case 'method': {\n        requestData.method = method;\n        break;\n      }\n      case 'url': {\n        requestData.url = absoluteUrl;\n        break;\n      }\n      case 'cookies': {\n        // cookies:\n        //   node, express, koa: req.headers.cookie\n        //   vercel, sails.js, express (w/ cookie middleware), nextjs: req.cookies\n        requestData.cookies =\n          // TODO (v8 / #5257): We're only sending the empty object for backwards compatibility, so the last bit can\n          // come off in v8\n          req.cookies || (headers.cookie && parseCookie(headers.cookie)) || {};\n        break;\n      }\n      case 'query_string': {\n        // query string:\n        //   node: req.url (raw)\n        //   express, koa, nextjs: req.query\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n        requestData.query_string = extractQueryParams(req, deps);\n        break;\n      }\n      case 'data': {\n        if (method === 'GET' || method === 'HEAD') {\n          break;\n        }\n        // body data:\n        //   express, koa, nextjs: req.body\n        //\n        //   when using node by itself, you have to read the incoming stream(see\n        //   https://nodejs.dev/learn/get-http-request-body-data-using-nodejs); if a user is doing that, we can't know\n        //   where they're going to store the final result, so they'll have to capture this data themselves\n        if (req.body !== undefined) {\n          requestData.data = isString(req.body) ? req.body : JSON.stringify(normalize(req.body));\n        }\n        break;\n      }\n      default: {\n        if ({}.hasOwnProperty.call(req, key)) {\n          requestData[key] = (req as { [key: string]: unknown })[key];\n        }\n      }\n    }\n  });\n\n  return requestData;\n}\n\n/**\n * Add data from the given request to the given event\n *\n * @param event The event to which the request data will be added\n * @param req Request object\n * @param options.include Flags to control what data is included\n * @param options.deps Injected platform-specific dependencies\n * @returns The mutated `Event` object\n */\nexport function addRequestDataToEvent(\n  event: Event,\n  req: PolymorphicRequest,\n  options?: AddRequestDataToEventOptions,\n): Event {\n  const include = {\n    ...DEFAULT_INCLUDES,\n    ...(options && options.include),\n  };\n\n  if (include.request) {\n    const extractedRequestData = Array.isArray(include.request)\n      ? extractRequestData(req, { include: include.request, deps: options && options.deps })\n      : extractRequestData(req, { deps: options && options.deps });\n\n    event.request = {\n      ...event.request,\n      ...extractedRequestData,\n    };\n  }\n\n  if (include.user) {\n    const extractedUser = req.user && isPlainObject(req.user) ? extractUserData(req.user, include.user) : {};\n\n    if (Object.keys(extractedUser).length) {\n      event.user = {\n        ...event.user,\n        ...extractedUser,\n      };\n    }\n  }\n\n  // client ip:\n  //   node, nextjs: req.socket.remoteAddress\n  //   express, koa: req.ip\n  if (include.ip) {\n    const ip = req.ip || (req.socket && req.socket.remoteAddress);\n    if (ip) {\n      event.user = {\n        ...event.user,\n        ip_address: ip,\n      };\n    }\n  }\n\n  if (include.transaction && !event.transaction) {\n    // TODO do we even need this anymore?\n    // TODO make this work for nextjs\n    event.transaction = extractTransaction(req, include.transaction);\n  }\n\n  return event;\n}\n\nfunction extractQueryParams(\n  req: PolymorphicRequest,\n  deps?: InjectedNodeDeps,\n): string | Record<string, unknown> | undefined {\n  // url (including path and query string):\n  //   node, express: req.originalUrl\n  //   koa, nextjs: req.url\n  let originalUrl = req.originalUrl || req.url || '';\n\n  if (!originalUrl) {\n    return;\n  }\n\n  // The `URL` constructor can't handle internal URLs of the form `/some/path/here`, so stick a dummy protocol and\n  // hostname on the beginning. Since the point here is just to grab the query string, it doesn't matter what we use.\n  if (originalUrl.startsWith('/')) {\n    originalUrl = `http://dogs.are.great${originalUrl}`;\n  }\n\n  try {\n    return (\n      req.query ||\n      (typeof URL !== 'undefined' && new URL(originalUrl).search.slice(1)) ||\n      // In Node 8, `URL` isn't in the global scope, so we have to use the built-in module from Node\n      (deps && deps.url && deps.url.parse(originalUrl).query) ||\n      undefined\n    );\n  } catch {\n    return undefined;\n  }\n}\n\n/**\n * Transforms a `Headers` object that implements the `Web Fetch API` (https://developer.mozilla.org/en-US/docs/Web/API/Headers) into a simple key-value dict.\n * The header keys will be lower case: e.g. A \"Content-Type\" header will be stored as \"content-type\".\n */\n// TODO(v8): Make this function return undefined when the extraction fails.\nexport function winterCGHeadersToDict(winterCGHeaders: WebFetchHeaders): Record<string, string> {\n  const headers: Record<string, string> = {};\n  try {\n    winterCGHeaders.forEach((value, key) => {\n      if (typeof value === 'string') {\n        // We check that value is a string even though it might be redundant to make sure prototype pollution is not possible.\n        headers[key] = value;\n      }\n    });\n  } catch (e) {\n    DEBUG_BUILD &&\n      logger.warn('Sentry failed extracting headers from a request object. If you see this, please file an issue.');\n  }\n\n  return headers;\n}\n\n/**\n * Converts a `Request` object that implements the `Web Fetch API` (https://developer.mozilla.org/en-US/docs/Web/API/Headers) into the format that the `RequestData` integration understands.\n */\nexport function winterCGRequestToRequestData(req: WebFetchRequest): PolymorphicRequest {\n  const headers = winterCGHeadersToDict(req.headers);\n  return {\n    method: req.method,\n    url: req.url,\n    headers,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAiBA,MAAM,mBAAmB;AACzB,EAAE,EAAE,EAAE,KAAK;AACX,EAAE,OAAO,EAAE,IAAI;AACf,EAAE,WAAW,EAAE,IAAI;AACnB,EAAE,IAAI,EAAE,IAAI;AACZ,CAAC,CAAA;AACD,MAAM,wBAAyB,GAAE,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,CAAC,CAAA;AACzF,MAAM,wBAAwB,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAC;;AAwChE;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B;AAC3C,EAAE,WAAW;AACb,EAAE,GAAG;AACL,EAAE,IAAI;AACN,EAAQ;AACR,EAAE,IAAI,CAAC,WAAW,EAAE,OAAM;AAC1B;AACA,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAA,IAAU,WAAW,CAAC,QAAQ,CAAC,MAAO,KAAI,KAAK,EAAE;AAC7E;AACA,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,CAAE,GAAE,yBAAyB,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAA,EAAM,CAAC,CAAA;AACvF,IAAI,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;AAChC;AACA;AACA,IAAI,WAAW,CAAC,WAAW,CAAC,EAAE,MAAA,EAAQ,CAAC,CAAA;AACvC,GAAE;AACF,EAAE,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,WAAY,IAAG,GAAG,CAAC,GAAG,CAAC,CAAA;AAC7D,EAAE,IAAI,GAAG,CAAC,OAAO,EAAE;AACnB,IAAI,WAAW,CAAC,YAAY,CAAC,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;AACpD,GAAE;AACF;AACA;AACA,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAA;AAC7D,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB;AACzC,EAAE,GAAG;AACL,EAAE,OAAO,GAA+D,EAAE;AAC1E,EAA+B;AAC/B,EAAE,MAAM,MAAA,GAAS,GAAG,CAAC,MAAA,IAAU,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,CAAA;AACvD;AACA,EAAE,IAAI,IAAK,GAAE,EAAE,CAAA;AACf,EAAE,IAAI,MAAM,GAAsB,KAAK,CAAA;AACvC;AACA;AACA,EAAE,IAAI,OAAO,CAAC,eAAe,GAAG,CAAC,KAAK,EAAE;AACxC,IAAI,OAAO,OAAO,CAAC,WAAY,IAAG,CAAC,EAAA,GAAA,CAAA,OAAA,IAAA,EAAA,CAAA,EAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAA,GAAA,OAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,OAAA,IAAA,GAAA,CAAA,WAAA,IAAA,GAAA,CAAA,GAAA,EAAA;AACA,IAAA,IAAA,GAAA,wBAAA,CAAA,GAAA,CAAA,WAAA,IAAA,GAAA,CAAA,GAAA,IAAA,EAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,IAAA,GAAA,EAAA,CAAA;AACA,EAAA,IAAA,OAAA,CAAA,MAAA,IAAA,MAAA,EAAA;AACA,IAAA,IAAA,IAAA,MAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,OAAA,CAAA,MAAA,IAAA,OAAA,CAAA,IAAA,EAAA;AACA,IAAA,IAAA,IAAA,GAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,OAAA,CAAA,IAAA,IAAA,IAAA,EAAA;AACA,IAAA,IAAA,IAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,CAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,kBAAA,CAAA,GAAA,EAAA,IAAA,EAAA;AACA,EAAA,QAAA,IAAA;AACA,IAAA,KAAA,MAAA,EAAA;AACA,MAAA,OAAA,yBAAA,CAAA,GAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA,IAAA,KAAA,SAAA,EAAA;AACA,MAAA,OAAA,CAAA,GAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,CAAA,KAAA,IAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,GAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,KAAA,aAAA,CAAA;AACA,KAAA;AACA,IAAA,KAAA,YAAA,CAAA;AACA,IAAA,SAAA;AACA;AACA,MAAA,MAAA,WAAA,GAAA,GAAA,CAAA,mBAAA,GAAA,GAAA,CAAA,mBAAA,GAAA,SAAA,CAAA;AACA,MAAA,OAAA,yBAAA,CAAA,GAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,WAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,eAAA;AACA,EAAA,IAAA;AACA;AACA;AACA,EAAA,IAAA;AACA,EAAA;AACA,EAAA,MAAA,aAAA,GAAA,EAAA,CAAA;AACA,EAAA,MAAA,UAAA,GAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,GAAA,IAAA,GAAA,qBAAA,CAAA;AACA;AACA,EAAA,UAAA,CAAA,OAAA,CAAA,GAAA,IAAA;AACA,IAAA,IAAA,IAAA,IAAA,GAAA,IAAA,IAAA,EAAA;AACA,MAAA,aAAA,CAAA,GAAA,CAAA,GAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA,CAAA,CAAA;AACA;AACA,EAAA,OAAA,aAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,kBAAA;AACA,EAAA,GAAA;AACA,EAAA,OAAA;;AAGA;AACA,EAAA;AACA,EAAA,MAAA,EAAA,OAAA,GAAA,wBAAA,EAAA,IAAA,EAAA,GAAA,OAAA,IAAA,EAAA,CAAA;AACA;AACA,EAAA,MAAA,WAAA,GAAA,EAAA,CAAA;AACA;AACA;AACA;AACA,EAAA,MAAA,OAAA,IAAA,GAAA,CAAA,OAAA,IAAA,EAAA,CAAA;;AAGA,CAAA;AACA;AACA;AACA,EAAA,MAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,MAAA,IAAA,GAAA,OAAA,CAAA,IAAA,IAAA,GAAA,CAAA,QAAA,IAAA,GAAA,CAAA,IAAA,IAAA,WAAA,CAAA;AACA;AACA;AACA;AACA,EAAA,MAAA,QAAA,GAAA,GAAA,CAAA,QAAA,KAAA,OAAA,KAAA,GAAA,CAAA,MAAA,IAAA,GAAA,CAAA,MAAA,CAAA,SAAA,CAAA,GAAA,OAAA,GAAA,MAAA,CAAA;AACA;AACA;AACA;AACA,EAAA,MAAA,WAAA,GAAA,GAAA,CAAA,WAAA,IAAA,GAAA,CAAA,GAAA,IAAA,EAAA,CAAA;AACA;AACA,EAAA,MAAA,WAAA,GAAA,WAAA,CAAA,UAAA,CAAA,QAAA,CAAA,GAAA,WAAA,GAAA,CAAA,EAAA,QAAA,CAAA,GAAA,EAAA,IAAA,CAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AACA,EAAA,OAAA,CAAA,OAAA,CAAA,GAAA,IAAA;AACA,IAAA,QAAA,GAAA;AACA,MAAA,KAAA,SAAA,EAAA;AACA,QAAA,WAAA,CAAA,OAAA,GAAA,OAAA,CAAA;AACA;AACA;AACA,QAAA,IAAA,CAAA,OAAA,CAAA,QAAA,CAAA,SAAA,CAAA,EAAA;AACA,UAAA,OAAA,CAAA,WAAA,CAAA,OAAA,GAAA,MAAA,CAAA;AACA,SAAA;AACA;AACA,QAAA,MAAA;AACA,OAAA;AACA,MAAA,KAAA,QAAA,EAAA;AACA,QAAA,WAAA,CAAA,MAAA,GAAA,MAAA,CAAA;AACA,QAAA,MAAA;AACA,OAAA;AACA,MAAA,KAAA,KAAA,EAAA;AACA,QAAA,WAAA,CAAA,GAAA,GAAA,WAAA,CAAA;AACA,QAAA,MAAA;AACA,OAAA;AACA,MAAA,KAAA,SAAA,EAAA;AACA;AACA;AACA;AACA,QAAA,WAAA,CAAA,OAAA;AACA;AACA;AACA,UAAA,GAAA,CAAA,OAAA,KAAA,OAAA,CAAA,MAAA,IAAA,WAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,IAAA,EAAA,CAAA;AACA,QAAA,MAAA;AACA,OAAA;AACA,MAAA,KAAA,cAAA,EAAA;AACA;AACA;AACA;AACA;AACA,QAAA,WAAA,CAAA,YAAA,GAAA,kBAAA,CAAA,GAAA,EAAA,IAAA,CAAA,CAAA;AACA,QAAA,MAAA;AACA,OAAA;AACA,MAAA,KAAA,MAAA,EAAA;AACA,QAAA,IAAA,MAAA,KAAA,KAAA,IAAA,MAAA,KAAA,MAAA,EAAA;AACA,UAAA,MAAA;AACA,SAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAA,IAAA,GAAA,CAAA,IAAA,KAAA,SAAA,EAAA;AACA,UAAA,WAAA,CAAA,IAAA,GAAA,QAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,IAAA,GAAA,IAAA,CAAA,SAAA,CAAA,SAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AACA,SAAA;AACA,QAAA,MAAA;AACA,OAAA;AACA,MAAA,SAAA;AACA,QAAA,IAAA,EAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA,EAAA,GAAA,CAAA,EAAA;AACA,UAAA,WAAA,CAAA,GAAA,CAAA,GAAA,CAAA,GAAA,GAAA,GAAA,CAAA,CAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,CAAA,CAAA;AACA;AACA,EAAA,OAAA,WAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA;AACA,EAAA,KAAA;AACA,EAAA,GAAA;AACA,EAAA,OAAA;AACA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA;AACA,IAAA,GAAA,gBAAA;AACA,IAAA,IAAA,OAAA,IAAA,OAAA,CAAA,OAAA,CAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,IAAA,OAAA,CAAA,OAAA,EAAA;AACA,IAAA,MAAA,oBAAA,GAAA,KAAA,CAAA,OAAA,CAAA,OAAA,CAAA,OAAA,CAAA;AACA,QAAA,kBAAA,CAAA,GAAA,EAAA,EAAA,OAAA,EAAA,OAAA,CAAA,OAAA,EAAA,IAAA,EAAA,OAAA,IAAA,OAAA,CAAA,IAAA,EAAA,CAAA;AACA,QAAA,kBAAA,CAAA,GAAA,EAAA,EAAA,IAAA,EAAA,OAAA,IAAA,OAAA,CAAA,IAAA,EAAA,CAAA,CAAA;AACA;AACA,IAAA,KAAA,CAAA,OAAA,GAAA;AACA,MAAA,GAAA,KAAA,CAAA,OAAA;AACA,MAAA,GAAA,oBAAA;AACA,KAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,OAAA,CAAA,IAAA,EAAA;AACA,IAAA,MAAA,aAAA,GAAA,GAAA,CAAA,IAAA,IAAA,aAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,eAAA,CAAA,GAAA,CAAA,IAAA,EAAA,OAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA;AACA;AACA,IAAA,IAAA,MAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,MAAA,EAAA;AACA,MAAA,KAAA,CAAA,IAAA,GAAA;AACA,QAAA,GAAA,KAAA,CAAA,IAAA;AACA,QAAA,GAAA,aAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA,EAAA,IAAA,OAAA,CAAA,EAAA,EAAA;AACA,IAAA,MAAA,EAAA,GAAA,GAAA,CAAA,EAAA,KAAA,GAAA,CAAA,MAAA,IAAA,GAAA,CAAA,MAAA,CAAA,aAAA,CAAA,CAAA;AACA,IAAA,IAAA,EAAA,EAAA;AACA,MAAA,KAAA,CAAA,IAAA,GAAA;AACA,QAAA,GAAA,KAAA,CAAA,IAAA;AACA,QAAA,UAAA,EAAA,EAAA;AACA,OAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,OAAA,CAAA,WAAA,IAAA,CAAA,KAAA,CAAA,WAAA,EAAA;AACA;AACA;AACA,IAAA,KAAA,CAAA,WAAA,GAAA,kBAAA,CAAA,GAAA,EAAA,OAAA,CAAA,WAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,KAAA,CAAA;AACA,CAAA;AACA;AACA,SAAA,kBAAA;AACA,EAAA,GAAA;AACA,EAAA,IAAA;AACA,EAAA;AACA;AACA;AACA;AACA,EAAA,IAAA,WAAA,GAAA,GAAA,CAAA,WAAA,IAAA,GAAA,CAAA,GAAA,IAAA,EAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAA,WAAA,EAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA;AACA;AACA,EAAA,IAAA,WAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA;AACA,IAAA,WAAA,GAAA,CAAA,qBAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA;AACA,IAAA;AACA,MAAA,GAAA,CAAA,KAAA;AACA,OAAA,OAAA,GAAA,KAAA,WAAA,IAAA,IAAA,GAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA,OAAA,IAAA,IAAA,IAAA,CAAA,GAAA,IAAA,IAAA,CAAA,GAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,KAAA,CAAA;AACA,MAAA,SAAA;AACA,MAAA;AACA,GAAA,CAAA,OAAA,EAAA,EAAA;AACA,IAAA,OAAA,SAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,eAAA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA,EAAA,CAAA;AACA,EAAA,IAAA;AACA,IAAA,eAAA,CAAA,OAAA,CAAA,CAAA,KAAA,EAAA,GAAA,KAAA;AACA,MAAA,IAAA,OAAA,KAAA,KAAA,QAAA,EAAA;AACA;AACA,QAAA,OAAA,CAAA,GAAA,CAAA,GAAA,KAAA,CAAA;AACA,OAAA;AACA,KAAA,CAAA,CAAA;AACA,GAAA,CAAA,OAAA,CAAA,EAAA;AACA,IAAA,WAAA;AACA,MAAA,MAAA,CAAA,IAAA,CAAA,gGAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,OAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,4BAAA,CAAA,GAAA,EAAA;AACA,EAAA,MAAA,OAAA,GAAA,qBAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA;AACA,EAAA,OAAA;AACA,IAAA,MAAA,EAAA,GAAA,CAAA,MAAA;AACA,IAAA,GAAA,EAAA,GAAA,CAAA,GAAA;AACA,IAAA,OAAA;AACA,GAAA,CAAA;AACA;;;;"}