{"version": 3, "file": "tracing.js", "sources": ["../../src/tracing.ts"], "sourcesContent": ["import type { PropagationContext, TraceparentData } from '@sentry/types';\n\nimport { baggageHeaderToDynamicSamplingContext } from './baggage';\nimport { uuid4 } from './misc';\n\n// eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor -- RegExp is used for readability here\nexport const TRACEPARENT_REGEXP = new RegExp(\n  '^[ \\\\t]*' + // whitespace\n    '([0-9a-f]{32})?' + // trace_id\n    '-?([0-9a-f]{16})?' + // span_id\n    '-?([01])?' + // sampled\n    '[ \\\\t]*$', // whitespace\n);\n\n/**\n * Extract transaction context data from a `sentry-trace` header.\n *\n * @param traceparent Traceparent string\n *\n * @returns Object containing data from the header, or undefined if traceparent string is malformed\n */\nexport function extractTraceparentData(traceparent?: string): TraceparentData | undefined {\n  if (!traceparent) {\n    return undefined;\n  }\n\n  const matches = traceparent.match(TRACEPARENT_REGEXP);\n  if (!matches) {\n    return undefined;\n  }\n\n  let parentSampled: boolean | undefined;\n  if (matches[3] === '1') {\n    parentSampled = true;\n  } else if (matches[3] === '0') {\n    parentSampled = false;\n  }\n\n  return {\n    traceId: matches[1],\n    parentSampled,\n    parentSpanId: matches[2],\n  };\n}\n\n/**\n * Create tracing context from incoming headers.\n *\n * @deprecated Use `propagationContextFromHeaders` instead.\n */\n// TODO(v8): Remove this function\nexport function tracingContextFromHeaders(\n  sentryTrace: Parameters<typeof extractTraceparentData>[0],\n  baggage: Parameters<typeof baggageHeaderToDynamicSamplingContext>[0],\n): {\n  traceparentData: ReturnType<typeof extractTraceparentData>;\n  dynamicSamplingContext: ReturnType<typeof baggageHeaderToDynamicSamplingContext>;\n  propagationContext: PropagationContext;\n} {\n  const traceparentData = extractTraceparentData(sentryTrace);\n  const dynamicSamplingContext = baggageHeaderToDynamicSamplingContext(baggage);\n\n  const { traceId, parentSpanId, parentSampled } = traceparentData || {};\n\n  if (!traceparentData) {\n    return {\n      traceparentData,\n      dynamicSamplingContext: undefined,\n      propagationContext: {\n        traceId: traceId || uuid4(),\n        spanId: uuid4().substring(16),\n      },\n    };\n  } else {\n    return {\n      traceparentData,\n      dynamicSamplingContext: dynamicSamplingContext || {}, // If we have traceparent data but no DSC it means we are not head of trace and we must freeze it\n      propagationContext: {\n        traceId: traceId || uuid4(),\n        parentSpanId: parentSpanId || uuid4().substring(16),\n        spanId: uuid4().substring(16),\n        sampled: parentSampled,\n        dsc: dynamicSamplingContext || {}, // If we have traceparent data but no DSC it means we are not head of trace and we must freeze it\n      },\n    };\n  }\n}\n\n/**\n * Create a propagation context from incoming headers.\n */\nexport function propagationContextFromHeaders(\n  sentryTrace: string | undefined,\n  baggage: string | number | boolean | string[] | null | undefined,\n): PropagationContext {\n  const traceparentData = extractTraceparentData(sentryTrace);\n  const dynamicSamplingContext = baggageHeaderToDynamicSamplingContext(baggage);\n\n  const { traceId, parentSpanId, parentSampled } = traceparentData || {};\n\n  if (!traceparentData) {\n    return {\n      traceId: traceId || uuid4(),\n      spanId: uuid4().substring(16),\n    };\n  } else {\n    return {\n      traceId: traceId || uuid4(),\n      parentSpanId: parentSpanId || uuid4().substring(16),\n      spanId: uuid4().substring(16),\n      sampled: parentSampled,\n      dsc: dynamicSamplingContext || {}, // If we have traceparent data but no DSC it means we are not head of trace and we must freeze it\n    };\n  }\n}\n\n/**\n * Create sentry-trace header from span context values.\n */\nexport function generateSentryTraceHeader(\n  traceId: string = uuid4(),\n  spanId: string = uuid4().substring(16),\n  sampled?: boolean,\n): string {\n  let sampledString = '';\n  if (sampled !== undefined) {\n    sampledString = sampled ? '-1' : '-0';\n  }\n  return `${traceId}-${spanId}${sampledString}`;\n}\n"], "names": [], "mappings": ";;;AAKA;AACa,MAAA,kBAAA,GAAqB,IAAI,MAAM;AAC5C,EAAE,UAAW;AACb,IAAI,iBAAkB;AACtB,IAAI,mBAAoB;AACxB,IAAI,WAAY;AAChB,IAAI,UAAU;AACd,EAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,WAAW,EAAwC;AAC1F,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,MAAM,UAAU,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;AACvD,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA,EAAE,IAAI,aAAa,CAAA;AACnB,EAAE,IAAI,OAAO,CAAC,CAAC,CAAE,KAAI,GAAG,EAAE;AAC1B,IAAI,aAAA,GAAgB,IAAI,CAAA;AACxB,GAAE,MAAO,IAAI,OAAO,CAAC,CAAC,CAAA,KAAM,GAAG,EAAE;AACjC,IAAI,aAAA,GAAgB,KAAK,CAAA;AACzB,GAAE;AACF;AACA,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AACvB,IAAI,aAAa;AACjB,IAAI,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5B,GAAG,CAAA;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB;AACzC,EAAE,WAAW;AACb,EAAE,OAAO;AACT;;AAIA,CAAE;AACF,EAAE,MAAM,eAAgB,GAAE,sBAAsB,CAAC,WAAW,CAAC,CAAA;AAC7D,EAAE,MAAM,sBAAuB,GAAE,qCAAqC,CAAC,OAAO,CAAC,CAAA;AAC/E;AACA,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,aAAc,EAAA,GAAI,eAAA,IAAmB,EAAE,CAAA;AACxE;AACA,EAAE,IAAI,CAAC,eAAe,EAAE;AACxB,IAAI,OAAO;AACX,MAAM,eAAe;AACrB,MAAM,sBAAsB,EAAE,SAAS;AACvC,MAAM,kBAAkB,EAAE;AAC1B,QAAQ,OAAO,EAAE,OAAA,IAAW,KAAK,EAAE;AACnC,QAAQ,MAAM,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AACrC,OAAO;AACP,KAAK,CAAA;AACL,SAAS;AACT,IAAI,OAAO;AACX,MAAM,eAAe;AACrB,MAAM,sBAAsB,EAAE,sBAAuB,IAAG,EAAE;AAC1D,MAAM,kBAAkB,EAAE;AAC1B,QAAQ,OAAO,EAAE,OAAA,IAAW,KAAK,EAAE;AACnC,QAAQ,YAAY,EAAE,YAAA,IAAgB,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AAC3D,QAAQ,MAAM,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AACrC,QAAQ,OAAO,EAAE,aAAa;AAC9B,QAAQ,GAAG,EAAE,sBAAuB,IAAG,EAAE;AACzC,OAAO;AACP,KAAK,CAAA;AACL,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,6BAA6B;AAC7C,EAAE,WAAW;AACb,EAAE,OAAO;AACT,EAAsB;AACtB,EAAE,MAAM,eAAgB,GAAE,sBAAsB,CAAC,WAAW,CAAC,CAAA;AAC7D,EAAE,MAAM,sBAAuB,GAAE,qCAAqC,CAAC,OAAO,CAAC,CAAA;AAC/E;AACA,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,aAAc,EAAA,GAAI,eAAA,IAAmB,EAAE,CAAA;AACxE;AACA,EAAE,IAAI,CAAC,eAAe,EAAE;AACxB,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,OAAA,IAAW,KAAK,EAAE;AACjC,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AACnC,KAAK,CAAA;AACL,SAAS;AACT,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,OAAA,IAAW,KAAK,EAAE;AACjC,MAAM,YAAY,EAAE,YAAA,IAAgB,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AACzD,MAAM,MAAM,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AACnC,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,GAAG,EAAE,sBAAuB,IAAG,EAAE;AACvC,KAAK,CAAA;AACL,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB;AACzC,EAAE,OAAO,GAAW,KAAK,EAAE;AAC3B,EAAE,MAAM,GAAW,KAAK,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC;AACxC,EAAE,OAAO;AACT,EAAU;AACV,EAAE,IAAI,aAAc,GAAE,EAAE,CAAA;AACxB,EAAE,IAAI,OAAQ,KAAI,SAAS,EAAE;AAC7B,IAAI,gBAAgB,OAAA,GAAU,IAAA,GAAO,IAAI,CAAA;AACzC,GAAE;AACF,EAAE,OAAO,CAAC,EAAA,OAAA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,aAAA,CAAA,CAAA,CAAA;AACA;;;;"}