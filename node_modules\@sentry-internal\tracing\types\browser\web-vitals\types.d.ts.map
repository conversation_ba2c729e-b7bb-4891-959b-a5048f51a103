{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../../src/browser/web-vitals/types.ts"], "names": [], "mappings": "AAgBA,OAAO,KAAK,EAAE,WAAW,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,eAAe,CAAC;AAC3E,OAAO,KAAK,EAAE,0BAA0B,EAAE,MAAM,mBAAmB,CAAC;AAEpE,cAAc,cAAc,CAAC;AAC7B,cAAc,mBAAmB,CAAC;AAElC,cAAc,aAAa,CAAC;AAC5B,cAAc,aAAa,CAAC;AAC5B,cAAc,aAAa,CAAC;AAM5B,MAAM,WAAW,eAAe;IAC9B,kBAAkB,EAAE,CAAC,YAAY,EAAE,0BAA0B,KAAK,IAAI,CAAC;IACvE,uBAAuB,EAAE,MAAM,IAAI,CAAC;IACpC,eAAe,EAAE,MAAM,CAAC;CACzB;AAED,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,MAAM;QACd,SAAS,EAAE,eAAe,CAAC;QAG3B,uBAAuB,EAAE,OAAO,CAAC;KAClC;CACF;AAED,MAAM,MAAM,sBAAsB,GAAG,gBAAgB,CAAC;AACtD,MAAM,WAAW,sBAAuB,SAAQ,gBAAgB;IAC9D,eAAe,EAAE,mBAAmB,CAAC;IACrC,aAAa,EAAE,mBAAmB,CAAC;IACnC,QAAQ,EAAE,mBAAmB,CAAC;IAC9B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAMD,UAAU,mBAAmB;IAC3B,UAAU,EAAE,2BAA2B,CAAC;IACxC,QAAQ,EAAE,yBAAyB,CAAC;IACpC,KAAK,EAAE,sBAAsB,CAAC;CAC/B;AAED,MAAM,WAAW,2BAA2B;IAC1C,QAAQ,CAAC,UAAU,CAAC,EAAE,kBAAkB,CAAC;CAC1C;AAGD,KAAK,cAAc,GAAG,WAAW,GAAG,UAAU,GAAG,UAAU,GAAG,OAAO,GAAG,MAAM,GAAG,OAAO,GAAG,SAAS,GAAG,MAAM,GAAG,OAAO,CAAC;AAGxH,KAAK,uBAAuB,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC;AAG9D,KAAK,OAAO,GAAG,MAAM,CAAC;AAEtB,KAAK,WAAW,GAAG,MAAM,CAAC;AAG1B,UAAU,kBAAmB,SAAQ,WAAW;IAE9C,QAAQ,CAAC,IAAI,CAAC,EAAE,cAAc,CAAC;IAE/B,QAAQ,CAAC,aAAa,CAAC,EAAE,uBAAuB,CAAC;IAEjD,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC;IAE/B,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IAE5B,QAAQ,CAAC,GAAG,CAAC,EAAE,WAAW,CAAC;IAE3B,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;IAE5B,QAAQ,CAAC,EAAE,aAAa,CAAC;CAC1B;AAGD,MAAM,WAAW,qBAAqB;IACpC,QAAQ,CAAC,YAAY,CAAC,EAAE,MAAM,CAAC;CAChC;AAED,MAAM,MAAM,6BAA6B,GAAG,IAAI,CAC9C,2BAA2B,EACzB,eAAe,GACf,iBAAiB,GACjB,eAAe,GACf,cAAc,GACd,iBAAiB,GACjB,iBAAiB,GACjB,QAAQ,CACX,CAAC;AAGF,OAAO,CAAC,MAAM,CAAC;IAEb,UAAU,QAAQ;QAChB,YAAY,CAAC,EAAE,OAAO,CAAC;KACxB;IAED,UAAU,WAAW;QACnB,gBAAgB,CAAC,CAAC,SAAS,MAAM,mBAAmB,EAAE,IAAI,EAAE,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC,EAAE,CAAC;KAC1F;IAGD,UAAU,uBAAuB;QAC/B,iBAAiB,CAAC,EAAE,MAAM,CAAC;KAC5B;IAGD,UAAU,2BAA2B;QACnC,eAAe,CAAC,EAAE,MAAM,CAAC;KAC1B;IAGD,UAAU,sBAAuB,SAAQ,gBAAgB;QACvD,QAAQ,EAAE,mBAAmB,CAAC;QAC9B,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,QAAQ,CAAC,MAAM,EAAE,IAAI,GAAG,IAAI,CAAC;KAC9B;IAGD,UAAU,sBAAsB;QAC9B,IAAI,CAAC,EAAE,IAAI,CAAC;QACZ,YAAY,EAAE,eAAe,CAAC;QAC9B,WAAW,EAAE,eAAe,CAAC;KAC9B;IAGD,UAAU,WAAY,SAAQ,gBAAgB;QAC5C,KAAK,EAAE,MAAM,CAAC;QACd,OAAO,EAAE,sBAAsB,EAAE,CAAC;QAClC,cAAc,EAAE,OAAO,CAAC;KACzB;IAGD,UAAU,sBAAuB,SAAQ,gBAAgB;QACvD,UAAU,EAAE,mBAAmB,CAAC;QAChC,QAAQ,EAAE,mBAAmB,CAAC;QAC9B,IAAI,EAAE,MAAM,CAAC;QACb,EAAE,EAAE,MAAM,CAAC;QACX,GAAG,EAAE,MAAM,CAAC;QACZ,OAAO,CAAC,EAAE,OAAO,CAAC;KACnB;CACF;AAED,MAAM,MAAM,2BAA2B,GAAG;IACxC,CAAC,GAAG,EAAE,MAAM,GAAG;QACb,SAAS,EAAE,MAAM,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,aAAa,EAAE,kBAAkB,CAAC;QAClC,IAAI,CAAC,EAAE,IAAI,CAAC;QACZ,iBAAiB,CAAC,EAAE,WAAW,CAAC;QAChC,QAAQ,CAAC,EAAE,MAAM,CAAC;QAClB,SAAS,EAAE,MAAM,CAAC;KACnB,CAAC;CACH,CAAC"}