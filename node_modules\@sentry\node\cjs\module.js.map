{"version": 3, "file": "module.js", "sources": ["../../src/module.ts"], "sourcesContent": ["import { posix, sep } from 'path';\nimport { dirname } from '@sentry/utils';\n\n/** normalizes Windows paths */\nfunction normalizeWindowsPath(path: string): string {\n  return path\n    .replace(/^[A-Z]:/, '') // remove Windows-style prefix\n    .replace(/\\\\/g, '/'); // replace all `\\` instances with `/`\n}\n\n/** Creates a function that gets the module name from a filename */\nexport function createGetModuleFromFilename(\n  basePath: string = process.argv[1] ? dirname(process.argv[1]) : process.cwd(),\n  isWindows: boolean = sep === '\\\\',\n): (filename: string | undefined) => string | undefined {\n  const normalizedBase = isWindows ? normalizeWindowsPath(basePath) : basePath;\n\n  return (filename: string | undefined) => {\n    if (!filename) {\n      return;\n    }\n\n    const normalizedFilename = isWindows ? normalizeWindowsPath(filename) : filename;\n\n    // eslint-disable-next-line prefer-const\n    let { dir, base: file, ext } = posix.parse(normalizedFilename);\n\n    if (ext === '.js' || ext === '.mjs' || ext === '.cjs') {\n      file = file.slice(0, ext.length * -1);\n    }\n\n    if (!dir) {\n      // No dirname whatsoever\n      dir = '.';\n    }\n\n    const n = dir.lastIndexOf('/node_modules');\n    if (n > -1) {\n      return `${dir.slice(n + 14).replace(/\\//g, '.')}:${file}`;\n    }\n\n    // Let's see if it's a part of the main module\n    // To be a part of main module, it has to share the same base\n    if (dir.startsWith(normalizedBase)) {\n      let moduleName = dir.slice(normalizedBase.length + 1).replace(/\\//g, '.');\n\n      if (moduleName) {\n        moduleName += ':';\n      }\n      moduleName += file;\n\n      return moduleName;\n    }\n\n    return file;\n  };\n}\n"], "names": ["dirname", "sep", "posix"], "mappings": ";;;;;AAGA;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAkB;AACpD,EAAE,OAAO,IAAA;AACT,KAAK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAA;AAC1B,KAAK,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;AACxB,CAAA;AACA;AACA;AACO,SAAS,2BAA2B;AAC3C,EAAE,QAAQ,GAAW,OAAO,CAAC,IAAI,CAAC,CAAC,CAAE,GAAEA,aAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA,GAAI,OAAO,CAAC,GAAG,EAAE;AAC/E,EAAE,SAAS,GAAYC,QAAA,KAAQ,IAAI;AACnC,EAAwD;AACxD,EAAE,MAAM,cAAe,GAAE,SAAU,GAAE,oBAAoB,CAAC,QAAQ,CAAE,GAAE,QAAQ,CAAA;AAC9E;AACA,EAAE,OAAO,CAAC,QAAQ,KAAyB;AAC3C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAI,MAAM,kBAAmB,GAAE,SAAU,GAAE,oBAAoB,CAAC,QAAQ,CAAE,GAAE,QAAQ,CAAA;AACpF;AACA;AACA,IAAI,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAI,EAAA,GAAIC,UAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;AAClE;AACA,IAAI,IAAI,GAAI,KAAI,KAAM,IAAG,GAAI,KAAI,MAAO,IAAG,GAAI,KAAI,MAAM,EAAE;AAC3D,MAAM,IAAK,GAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,MAAA,GAAS,CAAC,CAAC,CAAC,CAAA;AAC3C,KAAI;AACJ;AACA,IAAI,IAAI,CAAC,GAAG,EAAE;AACd;AACA,MAAM,GAAA,GAAM,GAAG,CAAA;AACf,KAAI;AACJ;AACA,IAAI,MAAM,IAAI,GAAG,CAAC,WAAW,CAAC,eAAe,CAAC,CAAA;AAC9C,IAAI,IAAI,CAAA,GAAI,CAAC,CAAC,EAAE;AAChB,MAAM,OAAO,CAAC,EAAA,GAAA,CAAA,KAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,OAAA,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA;AACA;AACA,IAAA,IAAA,GAAA,CAAA,UAAA,CAAA,cAAA,CAAA,EAAA;AACA,MAAA,IAAA,UAAA,GAAA,GAAA,CAAA,KAAA,CAAA,cAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,KAAA,EAAA,GAAA,CAAA,CAAA;AACA;AACA,MAAA,IAAA,UAAA,EAAA;AACA,QAAA,UAAA,IAAA,GAAA,CAAA;AACA,OAAA;AACA,MAAA,UAAA,IAAA,IAAA,CAAA;AACA;AACA,MAAA,OAAA,UAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA,CAAA;AACA;;;;"}