/**
 * Recursively read the contents of a directory.
 *
 * @param targetDir Absolute or relative path of the directory to scan. All returned paths will be relative to this
 * directory.
 * @returns Array holding all relative paths
 * @deprecated This function will be removed in the next major version.
 */
export declare function deepReadDirSync(targetDir: string): string[];
//# sourceMappingURL=utils.d.ts.map
