{"version": 3, "file": "getFID.js", "sources": ["../../../../src/browser/web-vitals/getFID.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { getVisibilityWatcher } from './lib/getVisibilityWatcher';\nimport { initMetric } from './lib/initMetric';\nimport { observe } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport type { FIDMetric, PerformanceEventTiming, ReportCallback } from './types';\n\n/**\n * Calculates the [FID](https://web.dev/fid/) value for the current page and\n * calls the `callback` function once the value is ready, along with the\n * relevant `first-input` performance entry used to determine the value. The\n * reported value is a `DOMHighResTimeStamp`.\n *\n * _**Important:** since FID is only reported after the user interacts with the\n * page, it's possible that it will not be reported for some page loads._\n */\nexport const onFID = (onReport: ReportCallback): void => {\n  const visibilityWatcher = getVisibilityWatcher();\n  const metric = initMetric('FID');\n  // eslint-disable-next-line prefer-const\n  let report: ReturnType<typeof bindReporter>;\n\n  const handleEntry = (entry: PerformanceEventTiming): void => {\n    // Only report if the page wasn't hidden prior to the first input.\n    if (entry.startTime < visibilityWatcher.firstHiddenTime) {\n      metric.value = entry.processingStart - entry.startTime;\n      metric.entries.push(entry);\n      report(true);\n    }\n  };\n\n  const handleEntries = (entries: FIDMetric['entries']): void => {\n    (entries as PerformanceEventTiming[]).forEach(handleEntry);\n  };\n\n  const po = observe('first-input', handleEntries);\n  report = bindReporter(onReport, metric);\n\n  if (po) {\n    onHidden(() => {\n      handleEntries(po.takeRecords() as FIDMetric['entries']);\n      po.disconnect();\n    }, true);\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACa,MAAA,KAAA,GAAQ,CAAC,QAAQ,KAA2B;AACzD,EAAE,MAAM,iBAAA,GAAoB,oBAAoB,EAAE,CAAA;AAClD,EAAE,MAAM,MAAO,GAAE,UAAU,CAAC,KAAK,CAAC,CAAA;AAClC;AACA,EAAE,IAAI,MAAM,CAAA;AACZ;AACA,EAAE,MAAM,WAAA,GAAc,CAAC,KAAK,KAAmC;AAC/D;AACA,IAAI,IAAI,KAAK,CAAC,YAAY,iBAAiB,CAAC,eAAe,EAAE;AAC7D,MAAM,MAAM,CAAC,KAAA,GAAQ,KAAK,CAAC,eAAgB,GAAE,KAAK,CAAC,SAAS,CAAA;AAC5D,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAChC,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA;AAClB,KAAI;AACJ,GAAG,CAAA;AACH;AACA,EAAE,MAAM,aAAA,GAAgB,CAAC,OAAO,KAAiC;AACjE,IAAI,CAAC,OAAQ,GAA6B,OAAO,CAAC,WAAW,CAAC,CAAA;AAC9D,GAAG,CAAA;AACH;AACA,EAAE,MAAM,KAAK,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,CAAA;AAClD,EAAE,SAAS,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;AACzC;AACA,EAAE,IAAI,EAAE,EAAE;AACV,IAAI,QAAQ,CAAC,MAAM;AACnB,MAAM,aAAa,CAAC,EAAE,CAAC,WAAW,IAA2B,CAAA;AAC7D,MAAM,EAAE,CAAC,UAAU,EAAE,CAAA;AACrB,KAAK,EAAE,IAAI,CAAC,CAAA;AACZ,GAAE;AACF;;;;"}