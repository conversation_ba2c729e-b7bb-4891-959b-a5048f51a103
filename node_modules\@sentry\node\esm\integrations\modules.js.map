{"version": 3, "file": "modules.js", "sources": ["../../../src/integrations/modules.ts"], "sourcesContent": ["import { existsSync, readFileSync } from 'fs';\nimport { dirname, join } from 'path';\nimport { convertIntegrationFnToClass, defineIntegration } from '@sentry/core';\nimport type { Event, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\n\nlet moduleCache: { [key: string]: string };\n\nconst INTEGRATION_NAME = 'Modules';\n\n/** Extract information about paths */\nfunction getPaths(): string[] {\n  try {\n    return require.cache ? Object.keys(require.cache as Record<string, unknown>) : [];\n  } catch (e) {\n    return [];\n  }\n}\n\n/** Extract information about package.json modules */\nfunction collectModules(): {\n  [name: string]: string;\n} {\n  const mainPaths = (require.main && require.main.paths) || [];\n  const paths = getPaths();\n  const infos: {\n    [name: string]: string;\n  } = {};\n  const seen: {\n    [path: string]: boolean;\n  } = {};\n\n  paths.forEach(path => {\n    let dir = path;\n\n    /** Traverse directories upward in the search of package.json file */\n    const updir = (): void | (() => void) => {\n      const orig = dir;\n      dir = dirname(orig);\n\n      if (!dir || orig === dir || seen[orig]) {\n        return undefined;\n      }\n      if (mainPaths.indexOf(dir) < 0) {\n        return updir();\n      }\n\n      const pkgfile = join(orig, 'package.json');\n      seen[orig] = true;\n\n      if (!existsSync(pkgfile)) {\n        return updir();\n      }\n\n      try {\n        const info = JSON.parse(readFileSync(pkgfile, 'utf8')) as {\n          name: string;\n          version: string;\n        };\n        infos[info.name] = info.version;\n      } catch (_oO) {\n        // no-empty\n      }\n    };\n\n    updir();\n  });\n\n  return infos;\n}\n\n/** Fetches the list of modules and the versions loaded by the entry file for your node.js app. */\nfunction _getModules(): { [key: string]: string } {\n  if (!moduleCache) {\n    moduleCache = collectModules();\n  }\n  return moduleCache;\n}\n\nconst _modulesIntegration = (() => {\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    processEvent(event) {\n      event.modules = {\n        ...event.modules,\n        ..._getModules(),\n      };\n\n      return event;\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const modulesIntegration = defineIntegration(_modulesIntegration);\n\n/**\n * Add node modules / packages to the event.\n * @deprecated Use `modulesIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const Modules = convertIntegrationFnToClass(INTEGRATION_NAME, modulesIntegration) as IntegrationClass<\n  Integration & { processEvent: (event: Event) => Event }\n>;\n\n// eslint-disable-next-line deprecation/deprecation\nexport type Modules = typeof Modules;\n"], "names": [], "mappings": ";;;;AAKA,IAAI,WAAW,CAAA;AACf;AACA,MAAM,gBAAA,GAAmB,SAAS,CAAA;AAClC;AACA;AACA,SAAS,QAAQ,GAAa;AAC9B,EAAE,IAAI;AACN,IAAI,OAAO,OAAO,CAAC,KAAA,GAAQ,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAA,EAAkC,GAAE,EAAE,CAAA;AACrF,GAAI,CAAA,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,EAAE,CAAA;AACb,GAAE;AACF,CAAA;AACA;AACA;AACA,SAAS,cAAc;AACrB;AACF,CAAE;AACF,EAAE,MAAM,SAAU,GAAE,CAAC,OAAO,CAAC,IAAK,IAAG,OAAO,CAAC,IAAI,CAAC,KAAK,KAAK,EAAE,CAAA;AAC9D,EAAE,MAAM,KAAA,GAAQ,QAAQ,EAAE,CAAA;AAC1B,EAAE,MAAM,KAAK;AACT;AACF,GAAI,EAAE,CAAA;AACR,EAAE,MAAM,IAAI;AACR;AACF,GAAI,EAAE,CAAA;AACR;AACA,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ;AACxB,IAAI,IAAI,GAAI,GAAE,IAAI,CAAA;AAClB;AACA;AACA,IAAI,MAAM,KAAA,GAAQ,MAA2B;AAC7C,MAAM,MAAM,IAAK,GAAE,GAAG,CAAA;AACtB,MAAM,GAAI,GAAE,OAAO,CAAC,IAAI,CAAC,CAAA;AACzB;AACA,MAAM,IAAI,CAAC,GAAA,IAAO,IAAA,KAAS,GAAA,IAAO,IAAI,CAAC,IAAI,CAAC,EAAE;AAC9C,QAAQ,OAAO,SAAS,CAAA;AACxB,OAAM;AACN,MAAM,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAA,GAAI,CAAC,EAAE;AACtC,QAAQ,OAAO,KAAK,EAAE,CAAA;AACtB,OAAM;AACN;AACA,MAAM,MAAM,UAAU,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;AAChD,MAAM,IAAI,CAAC,IAAI,CAAA,GAAI,IAAI,CAAA;AACvB;AACA,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;AAChC,QAAQ,OAAO,KAAK,EAAE,CAAA;AACtB,OAAM;AACN;AACA,MAAM,IAAI;AACV,QAAQ,MAAM,IAAA,GAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAE;;AAGvD,CAAA;AACR,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAE,GAAE,IAAI,CAAC,OAAO,CAAA;AACvC,OAAQ,CAAA,OAAO,GAAG,EAAE;AACpB;AACA,OAAM;AACN,KAAK,CAAA;AACL;AACA,IAAI,KAAK,EAAE,CAAA;AACX,GAAG,CAAC,CAAA;AACJ;AACA,EAAE,OAAO,KAAK,CAAA;AACd,CAAA;AACA;AACA;AACA,SAAS,WAAW,GAA8B;AAClD,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,WAAY,GAAE,cAAc,EAAE,CAAA;AAClC,GAAE;AACF,EAAE,OAAO,WAAW,CAAA;AACpB,CAAA;AACA;AACA,MAAM,mBAAoB,IAAG,MAAM;AACnC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB,MAAM,KAAK,CAAC,OAAA,GAAU;AACtB,QAAQ,GAAG,KAAK,CAAC,OAAO;AACxB,QAAQ,GAAG,WAAW,EAAE;AACxB,OAAO,CAAA;AACP;AACA,MAAM,OAAO,KAAK,CAAA;AAClB,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,kBAAmB,GAAE,iBAAiB,CAAC,mBAAmB,EAAC;AACxE;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,UAAU,2BAA2B,CAAC,gBAAgB,EAAE,kBAAkB,CAAE;;CAEzF;AACA;AACA;;;;"}