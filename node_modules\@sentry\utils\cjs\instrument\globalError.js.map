{"version": 3, "file": "globalError.js", "sources": ["../../../src/instrument/globalError.ts"], "sourcesContent": ["import type { HandlerDataError } from '@sentry/types';\n\nimport { GLOBAL_OBJ } from '../worldwide';\nimport { addHandler, maybeInstrument, triggerHandlers } from './_handlers';\n\nlet _oldOnErrorHandler: (typeof GLOBAL_OBJ)['onerror'] | null = null;\n\n/**\n * Add an instrumentation handler for when an error is captured by the global error handler.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addGlobalErrorInstrumentationHandler(handler: (data: HandlerDataError) => void): void {\n  const type = 'error';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentError);\n}\n\nfunction instrumentError(): void {\n  _oldOnErrorHandler = GLOBAL_OBJ.onerror;\n\n  GLOBAL_OBJ.onerror = function (\n    msg: string | object,\n    url?: string,\n    line?: number,\n    column?: number,\n    error?: Error,\n  ): boolean {\n    const handlerData: HandlerDataError = {\n      column,\n      error,\n      line,\n      msg,\n      url,\n    };\n    triggerHandlers('error', handlerData);\n\n    if (_oldOnErrorHandler && !_oldOnErrorHandler.__SENTRY_LOADER__) {\n      // eslint-disable-next-line prefer-rest-params\n      return _oldOnErrorHandler.apply(this, arguments);\n    }\n\n    return false;\n  };\n\n  GLOBAL_OBJ.onerror.__SENTRY_INSTRUMENTED__ = true;\n}\n"], "names": ["add<PERSON><PERSON><PERSON>", "maybeInstrument", "GLOBAL_OBJ", "triggerHandlers"], "mappings": ";;;;;AAKA,IAAI,kBAAkB,GAA0C,IAAI,CAAA;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,oCAAoC,CAAC,OAAO,EAA0C;AACtG,EAAE,MAAM,IAAK,GAAE,OAAO,CAAA;AACtB,EAAEA,oBAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAC3B,EAAEC,yBAAe,CAAC,IAAI,EAAE,eAAe,CAAC,CAAA;AACxC,CAAA;AACA;AACA,SAAS,eAAe,GAAS;AACjC,EAAE,kBAAmB,GAAEC,oBAAU,CAAC,OAAO,CAAA;AACzC;AACA,EAAEA,oBAAU,CAAC,OAAQ,GAAE;AACvB,IAAI,GAAG;AACP,IAAI,GAAG;AACP,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,KAAK;AACT,IAAa;AACb,IAAI,MAAM,WAAW,GAAqB;AAC1C,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,IAAI;AACV,MAAM,GAAG;AACT,MAAM,GAAG;AACT,KAAK,CAAA;AACL,IAAIC,yBAAe,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;AACzC;AACA,IAAI,IAAI,kBAAmB,IAAG,CAAC,kBAAkB,CAAC,iBAAiB,EAAE;AACrE;AACA,MAAM,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;AACtD,KAAI;AACJ;AACA,IAAI,OAAO,KAAK,CAAA;AAChB,GAAG,CAAA;AACH;AACA,EAAED,oBAAU,CAAC,OAAO,CAAC,uBAAA,GAA0B,IAAI,CAAA;AACnD;;;;"}