{"version": 3, "file": "history.js", "sources": ["../../../src/instrument/history.ts"], "sourcesContent": ["// TODO(v8): Move everything in this file into the browser package. Nothing here is generic and we run risk of leaking browser types into non-browser packages.\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/ban-types */\nimport type { HandlerDataHistory } from '@sentry/types';\n\nimport { fill } from '../object';\nimport { supportsHistory } from '../supports';\nimport { GLOBAL_OBJ } from '../worldwide';\nimport { addHandler, maybeInstrument, triggerHandlers } from './_handlers';\n\nconst WINDOW = GLOBAL_OBJ as unknown as Window;\n\nlet lastHref: string | undefined;\n\n/**\n * Add an instrumentation handler for when a fetch request happens.\n * The handler function is called once when the request starts and once when it ends,\n * which can be identified by checking if it has an `endTimestamp`.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addHistoryInstrumentationHandler(handler: (data: HandlerDataHistory) => void): void {\n  const type = 'history';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentHistory);\n}\n\nfunction instrumentHistory(): void {\n  if (!supportsHistory()) {\n    return;\n  }\n\n  const oldOnPopState = WINDOW.onpopstate;\n  WINDOW.onpopstate = function (this: WindowEventHandlers, ...args: any[]): any {\n    const to = WINDOW.location.href;\n    // keep track of the current URL state, as we always receive only the updated state\n    const from = lastHref;\n    lastHref = to;\n    const handlerData: HandlerDataHistory = { from, to };\n    triggerHandlers('history', handlerData);\n    if (oldOnPopState) {\n      // Apparently this can throw in Firefox when incorrectly implemented plugin is installed.\n      // https://github.com/getsentry/sentry-javascript/issues/3344\n      // https://github.com/bugsnag/bugsnag-js/issues/469\n      try {\n        return oldOnPopState.apply(this, args);\n      } catch (_oO) {\n        // no-empty\n      }\n    }\n  };\n\n  function historyReplacementFunction(originalHistoryFunction: () => void): () => void {\n    return function (this: History, ...args: any[]): void {\n      const url = args.length > 2 ? args[2] : undefined;\n      if (url) {\n        // coerce to string (this is what pushState does)\n        const from = lastHref;\n        const to = String(url);\n        // keep track of the current URL state, as we always receive only the updated state\n        lastHref = to;\n        const handlerData: HandlerDataHistory = { from, to };\n        triggerHandlers('history', handlerData);\n      }\n      return originalHistoryFunction.apply(this, args);\n    };\n  }\n\n  fill(WINDOW.history, 'pushState', historyReplacementFunction);\n  fill(WINDOW.history, 'replaceState', historyReplacementFunction);\n}\n"], "names": [], "mappings": ";;;;;;;AAWA,MAAM,MAAA,GAAS,UAAW,EAAA;AAC1B;AACA,IAAI,QAAQ,CAAA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gCAAgC,CAAC,OAAO,EAA4C;AACpG,EAAE,MAAM,IAAK,GAAE,SAAS,CAAA;AACxB,EAAE,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAC3B,EAAE,eAAe,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;AAC1C,CAAA;AACA;AACA,SAAS,iBAAiB,GAAS;AACnC,EAAE,IAAI,CAAC,eAAe,EAAE,EAAE;AAC1B,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAE,MAAM,aAAA,GAAgB,MAAM,CAAC,UAAU,CAAA;AACzC,EAAE,MAAM,CAAC,UAAA,GAAa,WAAqC,GAAG,IAAI,EAAc;AAChF,IAAI,MAAM,EAAG,GAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAA;AACnC;AACA,IAAI,MAAM,IAAK,GAAE,QAAQ,CAAA;AACzB,IAAI,QAAA,GAAW,EAAE,CAAA;AACjB,IAAI,MAAM,WAAW,GAAuB,EAAE,IAAI,EAAE,IAAI,CAAA;AACxD,IAAI,eAAe,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;AAC3C,IAAI,IAAI,aAAa,EAAE;AACvB;AACA;AACA;AACA,MAAM,IAAI;AACV,QAAQ,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAC9C,OAAQ,CAAA,OAAO,GAAG,EAAE;AACpB;AACA,OAAM;AACN,KAAI;AACJ,GAAG,CAAA;AACH;AACA,EAAE,SAAS,0BAA0B,CAAC,uBAAuB,EAA0B;AACvF,IAAI,OAAO,WAAyB,GAAG,IAAI,EAAe;AAC1D,MAAM,MAAM,GAAA,GAAM,IAAI,CAAC,MAAO,GAAE,CAAE,GAAE,IAAI,CAAC,CAAC,CAAA,GAAI,SAAS,CAAA;AACvD,MAAM,IAAI,GAAG,EAAE;AACf;AACA,QAAQ,MAAM,IAAK,GAAE,QAAQ,CAAA;AAC7B,QAAQ,MAAM,EAAG,GAAE,MAAM,CAAC,GAAG,CAAC,CAAA;AAC9B;AACA,QAAQ,QAAA,GAAW,EAAE,CAAA;AACrB,QAAQ,MAAM,WAAW,GAAuB,EAAE,IAAI,EAAE,IAAI,CAAA;AAC5D,QAAQ,eAAe,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;AAC/C,OAAM;AACN,MAAM,OAAO,uBAAuB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AACtD,KAAK,CAAA;AACL,GAAE;AACF;AACA,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,EAAE,0BAA0B,CAAC,CAAA;AAC/D,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,cAAc,EAAE,0BAA0B,CAAC,CAAA;AAClE;;;;"}