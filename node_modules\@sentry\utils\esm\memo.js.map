{"version": 3, "file": "memo.js", "sources": ["../../src/memo.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unsafe-member-access */\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nexport type MemoFunc = [\n  // memoize\n  (obj: any) => boolean,\n  // unmemoize\n  (obj: any) => void,\n];\n\n/**\n * Helper to decycle json objects\n */\nexport function memoBuilder(): MemoFunc {\n  const hasWeakSet = typeof WeakSet === 'function';\n  const inner: any = hasWeakSet ? new WeakSet() : [];\n  function memoize(obj: any): boolean {\n    if (hasWeakSet) {\n      if (inner.has(obj)) {\n        return true;\n      }\n      inner.add(obj);\n      return false;\n    }\n    // eslint-disable-next-line @typescript-eslint/prefer-for-of\n    for (let i = 0; i < inner.length; i++) {\n      const value = inner[i];\n      if (value === obj) {\n        return true;\n      }\n    }\n    inner.push(obj);\n    return false;\n  }\n\n  function unmemoize(obj: any): void {\n    if (hasWeakSet) {\n      inner.delete(obj);\n    } else {\n      for (let i = 0; i < inner.length; i++) {\n        if (inner[i] === obj) {\n          inner.splice(i, 1);\n          break;\n        }\n      }\n    }\n  }\n  return [memoize, unmemoize];\n}\n"], "names": [], "mappings": "AAAA;AACA;;AASA;AACA;AACA;AACO,SAAS,WAAW,GAAa;AACxC,EAAE,MAAM,UAAW,GAAE,OAAO,OAAA,KAAY,UAAU,CAAA;AAClD,EAAE,MAAM,KAAK,GAAQ,UAAW,GAAE,IAAI,OAAO,EAAC,GAAI,EAAE,CAAA;AACpD,EAAE,SAAS,OAAO,CAAC,GAAG,EAAgB;AACtC,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1B,QAAQ,OAAO,IAAI,CAAA;AACnB,OAAM;AACN,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;AACpB,MAAM,OAAO,KAAK,CAAA;AAClB,KAAI;AACJ;AACA,IAAI,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAE,GAAE,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,MAAM,MAAM,KAAM,GAAE,KAAK,CAAC,CAAC,CAAC,CAAA;AAC5B,MAAM,IAAI,KAAM,KAAI,GAAG,EAAE;AACzB,QAAQ,OAAO,IAAI,CAAA;AACnB,OAAM;AACN,KAAI;AACJ,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACnB,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,SAAS,SAAS,CAAC,GAAG,EAAa;AACrC,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;AACvB,WAAW;AACX,MAAM,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAE,GAAE,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,QAAQ,IAAI,KAAK,CAAC,CAAC,CAAE,KAAI,GAAG,EAAE;AAC9B,UAAU,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAC5B,UAAU,MAAK;AACf,SAAQ;AACR,OAAM;AACN,KAAI;AACJ,GAAE;AACF,EAAE,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;AAC7B;;;;"}