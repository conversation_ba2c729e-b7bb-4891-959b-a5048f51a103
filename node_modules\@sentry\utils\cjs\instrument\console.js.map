{"version": 3, "file": "console.js", "sources": ["../../../src/instrument/console.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n/* eslint-disable @typescript-eslint/ban-types */\nimport type { ConsoleLevel, HandlerDataConsole } from '@sentry/types';\n\nimport { CONSOLE_LEVELS, originalConsoleMethods } from '../logger';\nimport { fill } from '../object';\nimport { GLOBAL_OBJ } from '../worldwide';\nimport { addHandler, maybeInstrument, triggerHandlers } from './_handlers';\n\n/**\n * Add an instrumentation handler for when a console.xxx method is called.\n *\n * Use at your own risk, this might break without changelog notice, only used internally.\n * @hidden\n */\nexport function addConsoleInstrumentationHandler(handler: (data: HandlerDataConsole) => void): void {\n  const type = 'console';\n  addHandler(type, handler);\n  maybeInstrument(type, instrumentConsole);\n}\n\nfunction instrumentConsole(): void {\n  if (!('console' in GLOBAL_OBJ)) {\n    return;\n  }\n\n  CONSOLE_LEVELS.forEach(function (level: ConsoleLevel): void {\n    if (!(level in GLOBAL_OBJ.console)) {\n      return;\n    }\n\n    fill(GLOBAL_OBJ.console, level, function (originalConsoleMethod: () => any): Function {\n      originalConsoleMethods[level] = originalConsoleMethod;\n\n      return function (...args: any[]): void {\n        const handlerData: HandlerDataConsole = { args, level };\n        triggerHandlers('console', handlerData);\n\n        const log = originalConsoleMethods[level];\n        log && log.apply(GLOBAL_OBJ.console, args);\n      };\n    });\n  });\n}\n"], "names": ["add<PERSON><PERSON><PERSON>", "maybeInstrument", "GLOBAL_OBJ", "CONSOLE_LEVELS", "fill", "originalConsoleMethods", "triggerHandlers"], "mappings": ";;;;;;;AASA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gCAAgC,CAAC,OAAO,EAA4C;AACpG,EAAE,MAAM,IAAK,GAAE,SAAS,CAAA;AACxB,EAAEA,oBAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAC3B,EAAEC,yBAAe,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;AAC1C,CAAA;AACA;AACA,SAAS,iBAAiB,GAAS;AACnC,EAAE,IAAI,EAAE,aAAaC,oBAAU,CAAC,EAAE;AAClC,IAAI,OAAM;AACV,GAAE;AACF;AACA,EAAEC,qBAAc,CAAC,OAAO,CAAC,UAAU,KAAK,EAAsB;AAC9D,IAAI,IAAI,EAAE,KAAA,IAASD,oBAAU,CAAC,OAAO,CAAC,EAAE;AACxC,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAIE,WAAI,CAACF,oBAAU,CAAC,OAAO,EAAE,KAAK,EAAE,UAAU,qBAAqB,EAAuB;AAC1F,MAAMG,6BAAsB,CAAC,KAAK,CAAA,GAAI,qBAAqB,CAAA;AAC3D;AACA,MAAM,OAAO,UAAU,GAAG,IAAI,EAAe;AAC7C,QAAQ,MAAM,WAAW,GAAuB,EAAE,IAAI,EAAE,OAAO,CAAA;AAC/D,QAAQC,yBAAe,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;AAC/C;AACA,QAAQ,MAAM,GAAI,GAAED,6BAAsB,CAAC,KAAK,CAAC,CAAA;AACjD,QAAQ,GAAA,IAAO,GAAG,CAAC,KAAK,CAACH,oBAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;AAClD,OAAO,CAAA;AACP,KAAK,CAAC,CAAA;AACN,GAAG,CAAC,CAAA;AACJ;;;;"}