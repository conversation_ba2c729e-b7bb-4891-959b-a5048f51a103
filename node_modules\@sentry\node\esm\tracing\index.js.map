{"version": 3, "file": "index.js", "sources": ["../../../src/tracing/index.ts"], "sourcesContent": ["import type { LazyLoadedIntegration } from '@sentry-internal/tracing';\nimport { lazyLoadedNodePerformanceMonitoringIntegrations } from '@sentry-internal/tracing';\nimport type { Integration } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\n/**\n * Automatically detects and returns integrations that will work with your dependencies.\n */\nexport function autoDiscoverNodePerformanceMonitoringIntegrations(): Integration[] {\n  const loadedIntegrations = lazyLoadedNodePerformanceMonitoringIntegrations\n    .map(tryLoad => {\n      try {\n        return tryLoad();\n      } catch (_) {\n        return undefined;\n      }\n    })\n    .filter(integration => !!integration) as LazyLoadedIntegration[];\n\n  if (loadedIntegrations.length === 0) {\n    logger.warn('Performance monitoring integrations could not be automatically loaded.');\n  }\n\n  // Only return integrations where their dependencies loaded successfully.\n  return loadedIntegrations.filter(integration => !!integration.loadDependency());\n}\n"], "names": [], "mappings": ";;;AAKA;AACA;AACA;AACO,SAAS,iDAAiD,GAAkB;AACnF,EAAE,MAAM,qBAAqB,+CAAA;AAC7B,KAAK,GAAG,CAAC,OAAA,IAAW;AACpB,MAAM,IAAI;AACV,QAAQ,OAAO,OAAO,EAAE,CAAA;AACxB,OAAQ,CAAA,OAAO,CAAC,EAAE;AAClB,QAAQ,OAAO,SAAS,CAAA;AACxB,OAAM;AACN,KAAK,CAAA;AACL,KAAK,MAAM,CAAC,WAAA,IAAe,CAAC,CAAC,WAAW,CAAE,EAAA;AAC1C;AACA,EAAE,IAAI,kBAAkB,CAAC,MAAO,KAAI,CAAC,EAAE;AACvC,IAAI,MAAM,CAAC,IAAI,CAAC,wEAAwE,CAAC,CAAA;AACzF,GAAE;AACF;AACA;AACA,EAAE,OAAO,kBAAkB,CAAC,MAAM,CAAC,WAAY,IAAG,CAAC,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAA;AACjF;;;;"}