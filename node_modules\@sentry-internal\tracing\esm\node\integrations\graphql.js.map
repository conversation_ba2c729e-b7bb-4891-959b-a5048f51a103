{"version": 3, "file": "graphql.js", "sources": ["../../../../src/node/integrations/graphql.ts"], "sourcesContent": ["import type { Hub } from '@sentry/core';\nimport type { EventProcessor } from '@sentry/types';\nimport { fill, isThenable, loadModule, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../../common/debug-build';\nimport type { LazyLoadedIntegration } from './lazy';\nimport { shouldDisableAutoInstrumentation } from './utils/node-utils';\n\ntype GraphQLModule = {\n  [method: string]: (...args: unknown[]) => unknown;\n};\n\n/** Tracing integration for graphql package */\nexport class GraphQL implements LazyLoadedIntegration<GraphQLModule> {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'GraphQL';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string;\n\n  private _module?: GraphQLModule;\n\n  public constructor() {\n    this.name = GraphQL.id;\n  }\n\n  /** @inheritdoc */\n  public loadDependency(): GraphQLModule | undefined {\n    return (this._module = this._module || loadModule('graphql/execution/execute.js'));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    if (shouldDisableAutoInstrumentation(getCurrentHub)) {\n      DEBUG_BUILD && logger.log('GraphQL Integration is skipped because of instrumenter configuration.');\n      return;\n    }\n\n    const pkg = this.loadDependency();\n\n    if (!pkg) {\n      DEBUG_BUILD && logger.error('GraphQL Integration was unable to require graphql/execution package.');\n      return;\n    }\n\n    fill(pkg, 'execute', function (orig: () => void | Promise<unknown>) {\n      return function (this: unknown, ...args: unknown[]) {\n        // eslint-disable-next-line deprecation/deprecation\n        const scope = getCurrentHub().getScope();\n        // eslint-disable-next-line deprecation/deprecation\n        const parentSpan = scope.getSpan();\n\n        // eslint-disable-next-line deprecation/deprecation\n        const span = parentSpan?.startChild({\n          description: 'execute',\n          op: 'graphql.execute',\n          origin: 'auto.graphql.graphql',\n        });\n\n        // eslint-disable-next-line deprecation/deprecation\n        scope?.setSpan(span);\n\n        const rv = orig.call(this, ...args);\n\n        if (isThenable(rv)) {\n          return rv.then((res: unknown) => {\n            span?.end();\n            // eslint-disable-next-line deprecation/deprecation\n            scope?.setSpan(parentSpan);\n\n            return res;\n          });\n        }\n\n        span?.end();\n        // eslint-disable-next-line deprecation/deprecation\n        scope?.setSpan(parentSpan);\n        return rv;\n      };\n    });\n  }\n}\n"], "names": [], "mappings": ";;;;;AAYA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACO,CAAA,CAAA,CAAA,CAAA,EAAM,SAAwD;EACrE,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;GACS,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,YAAA,CAAA,EAAA,CAAA,IAAA,CAAO,CAAA,EAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;;EAEtC,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;;GAKS,WAAW,CAAG,EAAA;IACnB,IAAI,CAAC,CAAA,CAAA,CAAA,IAAO,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,EAAE;EACxB;;EAEF,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;GACS,cAAc,CAA8B,EAAA;IACjD,CAAQ,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA8B,CAAC,CAAC;EACpF;;EAEF,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;EACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACS,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,EAAsC,aAAa,EAAmB;IACtF,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,EAAE;MACnD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAe,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,GAAG,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuE,CAAC;MAClG,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,MAAM,CAAI,CAAA,EAAA,EAAE,IAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE;;IAEjC,CAAA,EAAA,CAAI,CAAC,CAAA,CAAA,CAAG,EAAE;MACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAe,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsE,CAAC;MACnG,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAG,CAAA,CAAA,EAAE,SAAS,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAU,IAAI,EAAiC;MAClE,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAyB,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,EAAa;QAC1D,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAA,CAAA,CAAA,CAAA,EAAM,MAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE;QAChD,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,MAAM,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE;;QAE1C,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAA,CAAA,CAAA,CAAA,EAAM,KAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAC,MAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;UAClC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAE,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACtB,CAAA,CAAE,EAAE,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACrB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/B,CAAA,CAAC,CAAA,CAAA;;QAEV,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAE,OAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA;;QAEpB,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,EAAK,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE,CAAA,CAAA,CAAG,CAAI,CAAA,CAAA,CAAA,CAAC;;QAEnC,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAA,CAAE,CAAC,EAAE;UAClB,OAAO,CAAE,CAAA,CAAC,IAAI,CAAC,CAAC,GAAG,EAAc,CAAA,EAAA;YAC3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAE,CAAG,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA;YACvB,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAE,OAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA;;YAE1B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,GAAG;UACtB,CAAW,CAAC;QACJ;;QAER,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAE,CAAG,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA;QACnB,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAE,OAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA;QAC1B,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE;MACjB,CAAO;IACP,CAAK,CAAC;EACJ;AACF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;"}