# Cashfree Payment Gateway Integration

A simple and secure implementation of Cashfree Payment Gateway using Node.js, Express, and vanilla JavaScript.

## 🚀 Features

- **Simple Integration**: Easy-to-use payment form with minimal setup
- **Secure Payments**: Uses Cashfree's secure payment infrastructure
- **Responsive Design**: Mobile-friendly payment interface
- **Real-time Status**: Payment status tracking and webhooks
- **Error Handling**: Comprehensive error handling and user feedback
- **Multiple Payment Methods**: Supports all Cashfree payment methods

## 📋 Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Cashfree merchant account
- Basic knowledge of JavaScript and Node.js

## 🛠️ Installation

1. **Clone or download the project**
   ```bash
   git clone <your-repo-url>
   cd cashfree-payment-gateway
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   - Copy `.env.example` to `.env`
   - Update the values with your Cashfree credentials:
   ```env
   CASHFREE_APP_ID=your_app_id_here
   CASHFREE_SECRET_KEY=your_secret_key_here
   CASHFREE_ENVIRONMENT=SANDBOX
   PORT=3000
   BASE_URL=http://localhost:3000
   RETURN_URL=http://localhost:3000/payment/return
   NOTIFY_URL=http://localhost:3000/payment/webhook
   ```

## 🔑 Getting Cashfree Credentials

1. **Sign up** at [Cashfree Dashboard](https://merchant.cashfree.com/)
2. **Complete KYC** verification
3. **Navigate** to Developers > API Keys
4. **Copy** your App ID and Secret Key
5. **Use SANDBOX** for testing, PRODUCTION for live transactions

## 🚀 Running the Application

1. **Start the server**
   ```bash
   npm start
   ```
   
   For development with auto-reload:
   ```bash
   npm run dev
   ```

2. **Open your browser** and navigate to:
   ```
   http://localhost:3000
   ```

## 📁 Project Structure

```
cashfree-payment-gateway/
├── config/
│   └── cashfree.js          # Cashfree configuration
├── routes/
│   └── payment.js           # Payment route handlers
├── public/
│   ├── index.html          # Payment form
│   ├── success.html        # Success page
│   └── failure.html        # Failure page
├── .env                    # Environment variables
├── .env.example           # Environment template
├── server.js              # Main server file
├── package.json           # Dependencies
└── README.md              # This file
```

## 🔄 API Endpoints

### POST `/payment/create-order`
Creates a new payment order.

**Request Body:**
```json
{
  "amount": 100.00,
  "customerName": "John Doe",
  "customerEmail": "<EMAIL>",
  "customerPhone": "9876543210"
}
```

**Response:**
```json
{
  "success": true,
  "orderId": "ORDER_1234567890_abcd1234",
  "paymentSessionId": "session_xxx",
  "orderAmount": 100.00,
  "customerDetails": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "9876543210"
  }
}
```

### GET `/payment/status/:orderId`
Fetches payment status for an order.

### POST `/payment/webhook`
Handles payment status webhooks from Cashfree.

### GET `/payment/return`
Handles payment return from Cashfree checkout.

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `CASHFREE_APP_ID` | Your Cashfree App ID | Required |
| `CASHFREE_SECRET_KEY` | Your Cashfree Secret Key | Required |
| `CASHFREE_ENVIRONMENT` | SANDBOX or PRODUCTION | SANDBOX |
| `PORT` | Server port | 3000 |
| `BASE_URL` | Your application URL | http://localhost:3000 |
| `RETURN_URL` | Payment return URL | {BASE_URL}/payment/return |
| `NOTIFY_URL` | Webhook notification URL | {BASE_URL}/payment/webhook |

## 🧪 Testing

### Test Cards (Sandbox Mode)

| Card Number | CVV | Expiry | Result |
|-------------|-----|--------|--------|
| **************** | 123 | 12/25 | Success |
| **************** | 123 | 12/25 | Failure |

### Test UPI ID
- `success@upi` - Success
- `failure@upi` - Failure

## 🔒 Security Best Practices

1. **Environment Variables**: Never commit `.env` file to version control
2. **HTTPS**: Use HTTPS in production
3. **Webhook Verification**: Verify webhook signatures (implement as needed)
4. **Input Validation**: Validate all user inputs
5. **Error Handling**: Don't expose sensitive information in error messages

## 🚀 Deployment

### Heroku
1. Create a new Heroku app
2. Set environment variables in Heroku dashboard
3. Deploy using Git or GitHub integration

### Other Platforms
1. Ensure Node.js runtime is available
2. Set environment variables
3. Run `npm install` and `npm start`

## 🐛 Troubleshooting

### Common Issues

1. **"Invalid credentials"**
   - Check your App ID and Secret Key
   - Ensure you're using the correct environment (SANDBOX/PRODUCTION)

2. **"Webhook not received"**
   - Ensure your server is publicly accessible
   - Check the NOTIFY_URL configuration
   - Verify webhook endpoint is working

3. **"Payment session creation failed"**
   - Validate all required fields
   - Check amount format (should be a number)
   - Ensure customer details are properly formatted

## 📞 Support

- **Cashfree Documentation**: [https://docs.cashfree.com/](https://docs.cashfree.com/)
- **Cashfree Support**: [https://support.cashfree.com/](https://support.cashfree.com/)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Happy Coding! 💻✨**
