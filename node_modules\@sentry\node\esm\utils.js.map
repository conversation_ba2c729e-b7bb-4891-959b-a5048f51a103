{"version": 3, "file": "utils.js", "sources": ["../../src/utils.ts"], "sourcesContent": ["import * as fs from 'fs';\nimport * as path from 'path';\n\n/**\n * Recursively read the contents of a directory.\n *\n * @param targetDir Absolute or relative path of the directory to scan. All returned paths will be relative to this\n * directory.\n * @returns Array holding all relative paths\n * @deprecated This function will be removed in the next major version.\n */\nexport function deepReadDirSync(targetDir: string): string[] {\n  const targetDirAbsPath = path.resolve(targetDir);\n\n  if (!fs.existsSync(targetDirAbsPath)) {\n    throw new Error(`Cannot read contents of ${targetDirAbsPath}. Directory does not exist.`);\n  }\n\n  if (!fs.statSync(targetDirAbsPath).isDirectory()) {\n    throw new Error(`Cannot read contents of ${targetDirAbsPath}, because it is not a directory.`);\n  }\n\n  // This does the same thing as its containing function, `deepReadDirSync` (except that - purely for convenience - it\n  // deals in absolute paths rather than relative ones). We need this to be separate from the outer function to preserve\n  // the difference between `targetDirAbsPath` and `currentDirAbsPath`.\n  const deepReadCurrentDir = (currentDirAbsPath: string): string[] => {\n    return fs.readdirSync(currentDirAbsPath).reduce((absPaths: string[], itemName: string) => {\n      const itemAbsPath = path.join(currentDirAbsPath, itemName);\n\n      if (fs.statSync(itemAbsPath).isDirectory()) {\n        return absPaths.concat(deepReadCurrentDir(itemAbsPath));\n      }\n\n      absPaths.push(itemAbsPath);\n      return absPaths;\n    }, []);\n  };\n\n  return deepReadCurrentDir(targetDirAbsPath).map(absPath => path.relative(targetDirAbsPath, absPath));\n}\n"], "names": [], "mappings": ";;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,SAAS,EAAoB;AAC7D,EAAE,MAAM,mBAAmB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;AAClD;AACA,EAAE,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;AACxC,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,gBAAgB,CAAC,2BAA2B,CAAC,CAAC,CAAA;AAC7F,GAAE;AACF;AACA,EAAE,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,WAAW,EAAE,EAAE;AACpD,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,gBAAgB,CAAC,gCAAgC,CAAC,CAAC,CAAA;AAClG,GAAE;AACF;AACA;AACA;AACA;AACA,EAAE,MAAM,kBAAA,GAAqB,CAAC,iBAAiB,KAAuB;AACtE,IAAI,OAAO,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAY,QAAQ,KAAa;AAC9F,MAAM,MAAM,WAAY,GAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAA;AAChE;AACA,MAAM,IAAI,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,EAAE;AAClD,QAAQ,OAAO,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAA;AAC/D,OAAM;AACN;AACA,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AAChC,MAAM,OAAO,QAAQ,CAAA;AACrB,KAAK,EAAE,EAAE,CAAC,CAAA;AACV,GAAG,CAAA;AACH;AACA,EAAE,OAAO,kBAAkB,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,OAAA,IAAW,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC,CAAA;AACtG;;;;"}