{"version": 3, "file": "getVisibilityWatcher.js", "sources": ["../../../../../src/browser/web-vitals/lib/getVisibilityWatcher.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../types';\nimport { onHidden } from './onHidden';\n\nlet firstHiddenTime = -1;\n\nconst initHiddenTime = (): void => {\n  // If the document is hidden and not prerendering, assume it was always\n  // hidden and the page was loaded in the background.\n  if (WINDOW.document && WINDOW.document.visibilityState) {\n    firstHiddenTime = WINDOW.document.visibilityState === 'hidden' && !WINDOW.document.prerendering ? 0 : Infinity;\n  }\n};\n\nconst trackChanges = (): void => {\n  // Update the time if/when the document becomes hidden.\n  onHidden(({ timeStamp }) => {\n    firstHiddenTime = timeStamp;\n  }, true);\n};\n\nexport const getVisibilityWatcher = (): {\n  readonly firstHiddenTime: number;\n} => {\n  if (firstHiddenTime < 0) {\n    // If the document is hidden when this code runs, assume it was hidden\n    // since navigation start. This isn't a perfect heuristic, but it's the\n    // best we can do until an API is available to support querying past\n    // visibilityState.\n    initHiddenTime();\n    trackChanges();\n  }\n  return {\n    get firstHiddenTime() {\n      return firstHiddenTime;\n    },\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA,IAAI,eAAA,GAAkB,CAAC,CAAC,CAAA;AACxB;AACA,MAAM,cAAA,GAAiB,MAAY;AACnC;AACA;AACA,EAAE,IAAI,MAAM,CAAC,QAAA,IAAY,MAAM,CAAC,QAAQ,CAAC,eAAe,EAAE;AAC1D,IAAI,kBAAkB,MAAM,CAAC,QAAQ,CAAC,oBAAoB,QAAA,IAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAA,GAAI,QAAQ,CAAA;AAClH,GAAE;AACF,CAAC,CAAA;AACD;AACA,MAAM,YAAA,GAAe,MAAY;AACjC;AACA,EAAE,QAAQ,CAAC,CAAC,EAAE,SAAU,EAAC,KAAK;AAC9B,IAAI,eAAA,GAAkB,SAAS,CAAA;AAC/B,GAAG,EAAE,IAAI,CAAC,CAAA;AACV,CAAC,CAAA;AACD;AACO,MAAM,oBAAqB,GAAE;AAClC;AACF,KAAK;AACL,EAAE,IAAI,eAAgB,GAAE,CAAC,EAAE;AAC3B;AACA;AACA;AACA;AACA,IAAI,cAAc,EAAE,CAAA;AACpB,IAAI,YAAY,EAAE,CAAA;AAClB,GAAE;AACF,EAAE,OAAO;AACT,IAAI,IAAI,eAAe,GAAG;AAC1B,MAAM,OAAO,eAAe,CAAA;AAC5B,KAAK;AACL,GAAG,CAAA;AACH;;;;"}