{"version": 3, "file": "onunhandledrejection.js", "sources": ["../../../src/integrations/onunhandledrejection.ts"], "sourcesContent": ["import { captureException, convertIntegrationFnToClass, defineIntegration, getClient } from '@sentry/core';\nimport type { Client, Integration, IntegrationClass, IntegrationFn } from '@sentry/types';\nimport { consoleSandbox } from '@sentry/utils';\n\nimport { logAndExitProcess } from './utils/errorhandling';\n\ntype UnhandledRejectionMode = 'none' | 'warn' | 'strict';\n\ninterface OnUnhandledRejectionOptions {\n  /**\n   * Option deciding what to do after capturing unhandledRejection,\n   * that mimicks behavior of node's --unhandled-rejection flag.\n   */\n  mode: UnhandledRejectionMode;\n}\n\nconst INTEGRATION_NAME = 'OnUnhandledRejection';\n\nconst _onUnhandledRejectionIntegration = ((options: Partial<OnUnhandledRejectionOptions> = {}) => {\n  const mode = options.mode || 'warn';\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    setup(client) {\n      global.process.on('unhandledRejection', makeUnhandledPromiseHandler(client, { mode }));\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const onUnhandledRejectionIntegration = defineIntegration(_onUnhandledRejectionIntegration);\n\n/**\n * Global Promise Rejection handler.\n * @deprecated Use `onUnhandledRejectionIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const OnUnhandledRejection = convertIntegrationFnToClass(\n  INTEGRATION_NAME,\n  onUnhandledRejectionIntegration,\n) as IntegrationClass<Integration & { setup: (client: Client) => void }> & {\n  new (options?: Partial<{ mode: UnhandledRejectionMode }>): Integration;\n};\n\n// eslint-disable-next-line deprecation/deprecation\nexport type OnUnhandledRejection = typeof OnUnhandledRejection;\n\n/**\n * Send an exception with reason\n * @param reason string\n * @param promise promise\n *\n * Exported only for tests.\n */\nexport function makeUnhandledPromiseHandler(\n  client: Client,\n  options: OnUnhandledRejectionOptions,\n): (reason: unknown, promise: unknown) => void {\n  return function sendUnhandledPromise(reason: unknown, promise: unknown): void {\n    if (getClient() !== client) {\n      return;\n    }\n\n    captureException(reason, {\n      originalException: promise,\n      captureContext: {\n        extra: { unhandledPromiseRejection: true },\n      },\n      mechanism: {\n        handled: false,\n        type: 'onunhandledrejection',\n      },\n    });\n\n    handleRejection(reason, options);\n  };\n}\n\n/**\n * Handler for `mode` option\n\n */\nfunction handleRejection(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  reason: any,\n  options: OnUnhandledRejectionOptions,\n): void {\n  // https://github.com/nodejs/node/blob/7cf6f9e964aa00772965391c23acda6d71972a9a/lib/internal/process/promises.js#L234-L240\n  const rejectionWarning =\n    'This error originated either by ' +\n    'throwing inside of an async function without a catch block, ' +\n    'or by rejecting a promise which was not handled with .catch().' +\n    ' The promise rejected with the reason:';\n\n  /* eslint-disable no-console */\n  if (options.mode === 'warn') {\n    consoleSandbox(() => {\n      console.warn(rejectionWarning);\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      console.error(reason && reason.stack ? reason.stack : reason);\n    });\n  } else if (options.mode === 'strict') {\n    consoleSandbox(() => {\n      console.warn(rejectionWarning);\n    });\n    logAndExitProcess(reason);\n  }\n  /* eslint-enable no-console */\n}\n"], "names": ["defineIntegration", "convertIntegrationFnToClass", "getClient", "captureException", "consoleSandbox", "logAndExitProcess"], "mappings": ";;;;;;AAgBA,MAAM,gBAAA,GAAmB,sBAAsB,CAAA;AAC/C;AACA,MAAM,gCAAA,IAAoC,CAAC,OAAO,GAAyC,EAAE,KAAK;AAClG,EAAE,MAAM,IAAK,GAAE,OAAO,CAAC,IAAA,IAAQ,MAAM,CAAA;AACrC;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,KAAK,CAAC,MAAM,EAAE;AAClB,MAAM,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,2BAA2B,CAAC,MAAM,EAAE,EAAE,IAAK,EAAC,CAAC,CAAC,CAAA;AAC5F,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,+BAAgC,GAAEA,sBAAiB,CAAC,gCAAgC,EAAC;AAClG;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,oBAAqB,GAAEC,gCAA2B;AAC/D,EAAE,gBAAgB;AAClB,EAAE,+BAA+B;AACjC,CAAE;;CAEF;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B;AAC3C,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAA+C;AAC/C,EAAE,OAAO,SAAS,oBAAoB,CAAC,MAAM,EAAW,OAAO,EAAiB;AAChF,IAAI,IAAIC,cAAS,EAAG,KAAI,MAAM,EAAE;AAChC,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAIC,qBAAgB,CAAC,MAAM,EAAE;AAC7B,MAAM,iBAAiB,EAAE,OAAO;AAChC,MAAM,cAAc,EAAE;AACtB,QAAQ,KAAK,EAAE,EAAE,yBAAyB,EAAE,MAAM;AAClD,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,IAAI,EAAE,sBAAsB;AACpC,OAAO;AACP,KAAK,CAAC,CAAA;AACN;AACA,IAAI,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;AACpC,GAAG,CAAA;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,eAAe;AACxB;AACA,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAQ;AACR;AACA,EAAE,MAAM,gBAAiB;AACzB,IAAI,kCAAmC;AACvC,IAAI,8DAA+D;AACnE,IAAI,gEAAiE;AACrE,IAAI,wCAAwC,CAAA;AAC5C;AACA;AACA,EAAE,IAAI,OAAO,CAAC,IAAK,KAAI,MAAM,EAAE;AAC/B,IAAIC,oBAAc,CAAC,MAAM;AACzB,MAAM,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;AACpC;AACA,MAAM,OAAO,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,KAAA,GAAQ,MAAM,CAAC,KAAM,GAAE,MAAM,CAAC,CAAA;AACnE,KAAK,CAAC,CAAA;AACN,GAAE,MAAO,IAAI,OAAO,CAAC,IAAA,KAAS,QAAQ,EAAE;AACxC,IAAIA,oBAAc,CAAC,MAAM;AACzB,MAAM,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;AACpC,KAAK,CAAC,CAAA;AACN,IAAIC,+BAAiB,CAAC,MAAM,CAAC,CAAA;AAC7B,GAAE;AACF;AACA;;;;;;"}