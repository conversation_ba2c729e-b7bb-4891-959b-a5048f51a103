{"version": 3, "file": "escapeStringForRegex.js", "sources": ["../../../src/vendor/escapeStringForRegex.ts"], "sourcesContent": ["// Based on https://github.com/sindresorhus/escape-string-regexp but with modifications to:\n//   a) reduce the size by skipping the runtime type - checking\n//   b) ensure it gets down - compiled for old versions of Node(the published package only supports Node 12+).\n//\n// MIT License\n//\n// Copyright (c) Sindre Sorhus <<EMAIL>> (https://sindresorhus.com)\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated\n// documentation files(the \"Software\"), to deal in the Software without restriction, including without limitation\n// the rights to use, copy, modify, merge, publish, distribute, sublicense, and / or sell copies of the Software, and\n// to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all copies or substantial portions of\n// the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO\n// THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\n// TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\n// IN THE SOFTWARE.\n\n/**\n * Given a string, escape characters which have meaning in the regex grammar, such that the result is safe to feed to\n * `new RegExp()`.\n *\n * @param regexString The string to escape\n * @returns An version of the string with all special regex characters escaped\n */\nexport function escapeStringForRegex(regexString: string): string {\n  // escape the hyphen separately so we can also replace it with a unicode literal hyphen, to avoid the problems\n  // discussed in https://github.com/sindresorhus/escape-string-regexp/issues/20.\n  return regexString.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&').replace(/-/g, '\\\\x2d');\n}\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,oBAAoB,CAAC,WAAW,EAAkB;AAClE;AACA;AACA,EAAE,OAAO,WAAW,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;AAClF;;;;"}