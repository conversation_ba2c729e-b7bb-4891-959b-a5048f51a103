{"version": 3, "file": "ratelimit.js", "sources": ["../../src/ratelimit.ts"], "sourcesContent": ["import type { DataCategory, TransportMakeRequestResponse } from '@sentry/types';\n\n// Intentionally keeping the key broad, as we don't know for sure what rate limit headers get returned from backend\nexport type RateLimits = Record<string, number>;\n\nexport const DEFAULT_RETRY_AFTER = 60 * 1000; // 60 seconds\n\n/**\n * Extracts Retry-After value from the request header or returns default value\n * @param header string representation of 'Retry-After' header\n * @param now current unix timestamp\n *\n */\nexport function parseRetryAfterHeader(header: string, now: number = Date.now()): number {\n  const headerDelay = parseInt(`${header}`, 10);\n  if (!isNaN(headerDelay)) {\n    return headerDelay * 1000;\n  }\n\n  const headerDate = Date.parse(`${header}`);\n  if (!isNaN(headerDate)) {\n    return headerDate - now;\n  }\n\n  return DEFAULT_RETRY_AFTER;\n}\n\n/**\n * Gets the time that the given category is disabled until for rate limiting.\n * In case no category-specific limit is set but a general rate limit across all categories is active,\n * that time is returned.\n *\n * @return the time in ms that the category is disabled until or 0 if there's no active rate limit.\n */\nexport function disabledUntil(limits: RateLimits, dataCategory: DataCategory): number {\n  return limits[dataCategory] || limits.all || 0;\n}\n\n/**\n * Checks if a category is rate limited\n */\nexport function isRateLimited(limits: RateLimits, dataCategory: DataCategory, now: number = Date.now()): boolean {\n  return disabledUntil(limits, dataCategory) > now;\n}\n\n/**\n * Update ratelimits from incoming headers.\n *\n * @return the updated RateLimits object.\n */\nexport function updateRateLimits(\n  limits: RateLimits,\n  { statusCode, headers }: TransportMakeRequestResponse,\n  now: number = Date.now(),\n): RateLimits {\n  const updatedRateLimits: RateLimits = {\n    ...limits,\n  };\n\n  // \"The name is case-insensitive.\"\n  // https://developer.mozilla.org/en-US/docs/Web/API/Headers/get\n  const rateLimitHeader = headers && headers['x-sentry-rate-limits'];\n  const retryAfterHeader = headers && headers['retry-after'];\n\n  if (rateLimitHeader) {\n    /**\n     * rate limit headers are of the form\n     *     <header>,<header>,..\n     * where each <header> is of the form\n     *     <retry_after>: <categories>: <scope>: <reason_code>: <namespaces>\n     * where\n     *     <retry_after> is a delay in seconds\n     *     <categories> is the event type(s) (error, transaction, etc) being rate limited and is of the form\n     *         <category>;<category>;...\n     *     <scope> is what's being limited (org, project, or key) - ignored by SDK\n     *     <reason_code> is an arbitrary string like \"org_quota\" - ignored by SDK\n     *     <namespaces> Semicolon-separated list of metric namespace identifiers. Defines which namespace(s) will be affected.\n     *         Only present if rate limit applies to the metric_bucket data category.\n     */\n    for (const limit of rateLimitHeader.trim().split(',')) {\n      const [retryAfter, categories, , , namespaces] = limit.split(':', 5);\n      const headerDelay = parseInt(retryAfter, 10);\n      const delay = (!isNaN(headerDelay) ? headerDelay : 60) * 1000; // 60sec default\n      if (!categories) {\n        updatedRateLimits.all = now + delay;\n      } else {\n        for (const category of categories.split(';')) {\n          if (category === 'metric_bucket') {\n            // namespaces will be present when category === 'metric_bucket'\n            if (!namespaces || namespaces.split(';').includes('custom')) {\n              updatedRateLimits[category] = now + delay;\n            }\n          } else {\n            updatedRateLimits[category] = now + delay;\n          }\n        }\n      }\n    }\n  } else if (retryAfterHeader) {\n    updatedRateLimits.all = now + parseRetryAfterHeader(retryAfterHeader, now);\n  } else if (statusCode === 429) {\n    updatedRateLimits.all = now + 60 * 1000;\n  }\n\n  return updatedRateLimits;\n}\n"], "names": [], "mappings": "AAEA;;AAGa,MAAA,mBAAA,GAAsB,EAAA,GAAK,KAAI;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,qBAAqB,CAAC,MAAM,EAAU,GAAG,GAAW,IAAI,CAAC,GAAG,EAAE,EAAU;AACxF,EAAE,MAAM,WAAY,GAAE,QAAQ,CAAC,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA;AACA,EAAA,IAAA,CAAA,KAAA,CAAA,WAAA,CAAA,EAAA;AACA,IAAA,OAAA,WAAA,GAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,UAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,EAAA;AACA,IAAA,OAAA,UAAA,GAAA,GAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,mBAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,aAAA,CAAA,MAAA,EAAA,YAAA,EAAA;AACA,EAAA,OAAA,MAAA,CAAA,YAAA,CAAA,IAAA,MAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,aAAA,CAAA,MAAA,EAAA,YAAA,EAAA,GAAA,GAAA,IAAA,CAAA,GAAA,EAAA,EAAA;AACA,EAAA,OAAA,aAAA,CAAA,MAAA,EAAA,YAAA,CAAA,GAAA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,gBAAA;AACA,EAAA,MAAA;AACA,EAAA,EAAA,UAAA,EAAA,OAAA,EAAA;AACA,EAAA,GAAA,GAAA,IAAA,CAAA,GAAA,EAAA;AACA,EAAA;AACA,EAAA,MAAA,iBAAA,GAAA;AACA,IAAA,GAAA,MAAA;AACA,GAAA,CAAA;AACA;AACA;AACA;AACA,EAAA,MAAA,eAAA,GAAA,OAAA,IAAA,OAAA,CAAA,sBAAA,CAAA,CAAA;AACA,EAAA,MAAA,gBAAA,GAAA,OAAA,IAAA,OAAA,CAAA,aAAA,CAAA,CAAA;AACA;AACA,EAAA,IAAA,eAAA,EAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAA,KAAA,MAAA,KAAA,IAAA,eAAA,CAAA,IAAA,EAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AACA,MAAA,MAAA,CAAA,UAAA,EAAA,UAAA,MAAA,UAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA;AACA,MAAA,MAAA,WAAA,GAAA,QAAA,CAAA,UAAA,EAAA,EAAA,CAAA,CAAA;AACA,MAAA,MAAA,KAAA,GAAA,CAAA,CAAA,KAAA,CAAA,WAAA,CAAA,GAAA,WAAA,GAAA,EAAA,IAAA,IAAA,CAAA;AACA,MAAA,IAAA,CAAA,UAAA,EAAA;AACA,QAAA,iBAAA,CAAA,GAAA,GAAA,GAAA,GAAA,KAAA,CAAA;AACA,OAAA,MAAA;AACA,QAAA,KAAA,MAAA,QAAA,IAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AACA,UAAA,IAAA,QAAA,KAAA,eAAA,EAAA;AACA;AACA,YAAA,IAAA,CAAA,UAAA,IAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA;AACA,cAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,GAAA,GAAA,KAAA,CAAA;AACA,aAAA;AACA,WAAA,MAAA;AACA,YAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,GAAA,GAAA,KAAA,CAAA;AACA,WAAA;AACA,SAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA,MAAA,IAAA,gBAAA,EAAA;AACA,IAAA,iBAAA,CAAA,GAAA,GAAA,GAAA,GAAA,qBAAA,CAAA,gBAAA,EAAA,GAAA,CAAA,CAAA;AACA,GAAA,MAAA,IAAA,UAAA,KAAA,GAAA,EAAA;AACA,IAAA,iBAAA,CAAA,GAAA,GAAA,GAAA,GAAA,EAAA,GAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,iBAAA,CAAA;AACA;;;;"}