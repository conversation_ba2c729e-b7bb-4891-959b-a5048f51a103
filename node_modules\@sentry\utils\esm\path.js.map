{"version": 3, "file": "path.js", "sources": ["../../src/path.ts"], "sourcesContent": ["// Slightly modified (no IE8 support, ES6) and transcribed to TypeScript\n// https://github.com/calvinmetcalf/rollup-plugin-node-builtins/blob/63ab8aacd013767445ca299e468d9a60a95328d7/src/es6/path.js\n//\n// Copyright Joyent, Inc.and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n/** JSDoc */\nfunction normalizeArray(parts: string[], allowAboveRoot?: boolean): string[] {\n  // if the path tries to go above the root, `up` ends up > 0\n  let up = 0;\n  for (let i = parts.length - 1; i >= 0; i--) {\n    const last = parts[i];\n    if (last === '.') {\n      parts.splice(i, 1);\n    } else if (last === '..') {\n      parts.splice(i, 1);\n      up++;\n    } else if (up) {\n      parts.splice(i, 1);\n      up--;\n    }\n  }\n\n  // if the path is allowed to go above the root, restore leading ..s\n  if (allowAboveRoot) {\n    for (; up--; up) {\n      parts.unshift('..');\n    }\n  }\n\n  return parts;\n}\n\n// Split a filename into [root, dir, basename, ext], unix version\n// 'root' is just a slash, or nothing.\nconst splitPathRe = /^(\\S+:\\\\|\\/?)([\\s\\S]*?)((?:\\.{1,2}|[^/\\\\]+?|)(\\.[^./\\\\]*|))(?:[/\\\\]*)$/;\n/** JSDoc */\nfunction splitPath(filename: string): string[] {\n  // Truncate files names greater than 1024 characters to avoid regex dos\n  // https://github.com/getsentry/sentry-javascript/pull/8737#discussion_r1285719172\n  const truncated = filename.length > 1024 ? `<truncated>${filename.slice(-1024)}` : filename;\n  const parts = splitPathRe.exec(truncated);\n  return parts ? parts.slice(1) : [];\n}\n\n// path.resolve([from ...], to)\n// posix version\n/** JSDoc */\nexport function resolve(...args: string[]): string {\n  let resolvedPath = '';\n  let resolvedAbsolute = false;\n\n  for (let i = args.length - 1; i >= -1 && !resolvedAbsolute; i--) {\n    const path = i >= 0 ? args[i] : '/';\n\n    // Skip empty entries\n    if (!path) {\n      continue;\n    }\n\n    resolvedPath = `${path}/${resolvedPath}`;\n    resolvedAbsolute = path.charAt(0) === '/';\n  }\n\n  // At this point the path should be resolved to a full absolute path, but\n  // handle relative paths to be safe (might happen when process.cwd() fails)\n\n  // Normalize the path\n  resolvedPath = normalizeArray(\n    resolvedPath.split('/').filter(p => !!p),\n    !resolvedAbsolute,\n  ).join('/');\n\n  return (resolvedAbsolute ? '/' : '') + resolvedPath || '.';\n}\n\n/** JSDoc */\nfunction trim(arr: string[]): string[] {\n  let start = 0;\n  for (; start < arr.length; start++) {\n    if (arr[start] !== '') {\n      break;\n    }\n  }\n\n  let end = arr.length - 1;\n  for (; end >= 0; end--) {\n    if (arr[end] !== '') {\n      break;\n    }\n  }\n\n  if (start > end) {\n    return [];\n  }\n  return arr.slice(start, end - start + 1);\n}\n\n// path.relative(from, to)\n// posix version\n/** JSDoc */\nexport function relative(from: string, to: string): string {\n  /* eslint-disable no-param-reassign */\n  from = resolve(from).slice(1);\n  to = resolve(to).slice(1);\n  /* eslint-enable no-param-reassign */\n\n  const fromParts = trim(from.split('/'));\n  const toParts = trim(to.split('/'));\n\n  const length = Math.min(fromParts.length, toParts.length);\n  let samePartsLength = length;\n  for (let i = 0; i < length; i++) {\n    if (fromParts[i] !== toParts[i]) {\n      samePartsLength = i;\n      break;\n    }\n  }\n\n  let outputParts = [];\n  for (let i = samePartsLength; i < fromParts.length; i++) {\n    outputParts.push('..');\n  }\n\n  outputParts = outputParts.concat(toParts.slice(samePartsLength));\n\n  return outputParts.join('/');\n}\n\n// path.normalize(path)\n// posix version\n/** JSDoc */\nexport function normalizePath(path: string): string {\n  const isPathAbsolute = isAbsolute(path);\n  const trailingSlash = path.slice(-1) === '/';\n\n  // Normalize the path\n  let normalizedPath = normalizeArray(\n    path.split('/').filter(p => !!p),\n    !isPathAbsolute,\n  ).join('/');\n\n  if (!normalizedPath && !isPathAbsolute) {\n    normalizedPath = '.';\n  }\n  if (normalizedPath && trailingSlash) {\n    normalizedPath += '/';\n  }\n\n  return (isPathAbsolute ? '/' : '') + normalizedPath;\n}\n\n// posix version\n/** JSDoc */\nexport function isAbsolute(path: string): boolean {\n  return path.charAt(0) === '/';\n}\n\n// posix version\n/** JSDoc */\nexport function join(...args: string[]): string {\n  return normalizePath(args.join('/'));\n}\n\n/** JSDoc */\nexport function dirname(path: string): string {\n  const result = splitPath(path);\n  const root = result[0];\n  let dir = result[1];\n\n  if (!root && !dir) {\n    // No dirname whatsoever\n    return '.';\n  }\n\n  if (dir) {\n    // It has a dirname, strip trailing slash\n    dir = dir.slice(0, dir.length - 1);\n  }\n\n  return root + dir;\n}\n\n/** JSDoc */\nexport function basename(path: string, ext?: string): string {\n  let f = splitPath(path)[2];\n  if (ext && f.slice(ext.length * -1) === ext) {\n    f = f.slice(0, f.length - ext.length);\n  }\n  return f;\n}\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,cAAc,CAAC,KAAK,EAAY,cAAc,EAAsB;AAC7E;AACA,EAAE,IAAI,EAAG,GAAE,CAAC,CAAA;AACZ,EAAE,KAAK,IAAI,CAAE,GAAE,KAAK,CAAC,MAAA,GAAS,CAAC,EAAE,CAAE,IAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,IAAI,MAAM,IAAK,GAAE,KAAK,CAAC,CAAC,CAAC,CAAA;AACzB,IAAI,IAAI,IAAK,KAAI,GAAG,EAAE;AACtB,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACxB,WAAW,IAAI,IAAK,KAAI,IAAI,EAAE;AAC9B,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACxB,MAAM,EAAE,EAAE,CAAA;AACV,KAAM,MAAK,IAAI,EAAE,EAAE;AACnB,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACxB,MAAM,EAAE,EAAE,CAAA;AACV,KAAI;AACJ,GAAE;AACF;AACA;AACA,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;AACrB,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AACzB,KAAI;AACJ,GAAE;AACF;AACA,EAAE,OAAO,KAAK,CAAA;AACd,CAAA;AACA;AACA;AACA;AACA,MAAM,WAAA,GAAc,wEAAwE,CAAA;AAC5F;AACA,SAAS,SAAS,CAAC,QAAQ,EAAoB;AAC/C;AACA;AACA,EAAE,MAAM,YAAY,QAAQ,CAAC,MAAO,GAAE,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA,GAAA,QAAA,CAAA;AACA,EAAA,MAAA,KAAA,GAAA,WAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA;AACA,EAAA,OAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,OAAA,CAAA,GAAA,IAAA,EAAA;AACA,EAAA,IAAA,YAAA,GAAA,EAAA,CAAA;AACA,EAAA,IAAA,gBAAA,GAAA,KAAA,CAAA;AACA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,IAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,MAAA,IAAA,GAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,CAAA,CAAA,GAAA,GAAA,CAAA;AACA;AACA;AACA,IAAA,IAAA,CAAA,IAAA,EAAA;AACA,MAAA,SAAA;AACA,KAAA;AACA;AACA,IAAA,YAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AACA,IAAA,gBAAA,GAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,KAAA,GAAA,CAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,YAAA,GAAA,cAAA;AACA,IAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,CAAA,gBAAA;AACA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA,EAAA,OAAA,CAAA,gBAAA,GAAA,GAAA,GAAA,EAAA,IAAA,YAAA,IAAA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,IAAA,CAAA,GAAA,EAAA;AACA,EAAA,IAAA,KAAA,GAAA,CAAA,CAAA;AACA,EAAA,OAAA,KAAA,GAAA,GAAA,CAAA,MAAA,EAAA,KAAA,EAAA,EAAA;AACA,IAAA,IAAA,GAAA,CAAA,KAAA,CAAA,KAAA,EAAA,EAAA;AACA,MAAA,MAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,GAAA,GAAA,GAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,EAAA,OAAA,GAAA,IAAA,CAAA,EAAA,GAAA,EAAA,EAAA;AACA,IAAA,IAAA,GAAA,CAAA,GAAA,CAAA,KAAA,EAAA,EAAA;AACA,MAAA,MAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,KAAA,GAAA,GAAA,EAAA;AACA,IAAA,OAAA,EAAA,CAAA;AACA,GAAA;AACA,EAAA,OAAA,GAAA,CAAA,KAAA,CAAA,KAAA,EAAA,GAAA,GAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,QAAA,CAAA,IAAA,EAAA,EAAA,EAAA;AACA;AACA,EAAA,IAAA,GAAA,OAAA,CAAA,IAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,EAAA,GAAA,OAAA,CAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA;AACA,EAAA,MAAA,SAAA,GAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,EAAA,MAAA,OAAA,GAAA,IAAA,CAAA,EAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA;AACA,EAAA,MAAA,MAAA,GAAA,IAAA,CAAA,GAAA,CAAA,SAAA,CAAA,MAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;AACA,EAAA,IAAA,eAAA,GAAA,MAAA,CAAA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,IAAA,SAAA,CAAA,CAAA,CAAA,KAAA,OAAA,CAAA,CAAA,CAAA,EAAA;AACA,MAAA,eAAA,GAAA,CAAA,CAAA;AACA,MAAA,MAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,WAAA,GAAA,EAAA,CAAA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,eAAA,EAAA,CAAA,GAAA,SAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,WAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,WAAA,GAAA,WAAA,CAAA,MAAA,CAAA,OAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AACA;AACA,EAAA,OAAA,WAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,aAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,cAAA,GAAA,UAAA,CAAA,IAAA,CAAA,CAAA;AACA,EAAA,MAAA,aAAA,GAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,KAAA,GAAA,CAAA;AACA;AACA;AACA,EAAA,IAAA,cAAA,GAAA,cAAA;AACA,IAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,CAAA,cAAA;AACA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAA,cAAA,IAAA,CAAA,cAAA,EAAA;AACA,IAAA,cAAA,GAAA,GAAA,CAAA;AACA,GAAA;AACA,EAAA,IAAA,cAAA,IAAA,aAAA,EAAA;AACA,IAAA,cAAA,IAAA,GAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,CAAA,cAAA,GAAA,GAAA,GAAA,EAAA,IAAA,cAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA,SAAA,UAAA,CAAA,IAAA,EAAA;AACA,EAAA,OAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,KAAA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA,SAAA,IAAA,CAAA,GAAA,IAAA,EAAA;AACA,EAAA,OAAA,aAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,OAAA,CAAA,IAAA,EAAA;AACA,EAAA,MAAA,MAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA;AACA,EAAA,MAAA,IAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,IAAA,GAAA,GAAA,MAAA,CAAA,CAAA,CAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAA,IAAA,IAAA,CAAA,GAAA,EAAA;AACA;AACA,IAAA,OAAA,GAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,GAAA,EAAA;AACA;AACA,IAAA,GAAA,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,IAAA,GAAA,GAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,QAAA,CAAA,IAAA,EAAA,GAAA,EAAA;AACA,EAAA,IAAA,CAAA,GAAA,SAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,EAAA,IAAA,GAAA,IAAA,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,KAAA,GAAA,EAAA;AACA,IAAA,CAAA,GAAA,CAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA,CAAA;AACA,GAAA;AACA,EAAA,OAAA,CAAA,CAAA;AACA;;;;"}