{"version": 3, "file": "onHidden.js", "sources": ["../../../../../src/browser/web-vitals/lib/onHidden.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../types';\n\nexport interface OnHiddenCallback {\n  (event: Event): void;\n}\n\nexport const onHidden = (cb: OnHiddenCallback, once?: boolean): void => {\n  const onHiddenOrPageHide = (event: Event): void => {\n    if (event.type === 'pagehide' || WINDOW.document!.visibilityState === 'hidden') {\n      cb(event);\n      if (once) {\n        removeEventListener('visibilitychange', onHiddenOrPageHide, true);\n        removeEventListener('pagehide', onHiddenOrPageHide, true);\n      }\n    }\n  };\n\n  if (WINDOW.document) {\n    addEventListener('visibilitychange', onHiddenOrPageHide, true);\n    // Some browsers have buggy implementations of visibilitychange,\n    // so we use pagehide in addition, just to be safe.\n    addEventListener('pagehide', onHiddenOrPageHide, true);\n  }\n};\n"], "names": [], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAQa,QAAS,GAAE,CAAC,EAAE,EAAoB,IAAI,KAAqB;AACxE,EAAE,MAAM,kBAAA,GAAqB,CAAC,KAAK,KAAkB;AACrD,IAAI,IAAI,KAAK,CAAC,SAAS,UAAA,IAAc,MAAM,CAAC,QAAQ,CAAE,eAAgB,KAAI,QAAQ,EAAE;AACpF,MAAM,EAAE,CAAC,KAAK,CAAC,CAAA;AACf,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,mBAAmB,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAA;AACzE,QAAQ,mBAAmB,CAAC,UAAU,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAA;AACjE,OAAM;AACN,KAAI;AACJ,GAAG,CAAA;AACH;AACA,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAA;AAClE;AACA;AACA,IAAI,gBAAgB,CAAC,UAAU,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAA;AAC1D,GAAE;AACF;;;;"}