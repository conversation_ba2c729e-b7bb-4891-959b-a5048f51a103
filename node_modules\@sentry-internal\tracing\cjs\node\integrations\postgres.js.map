{"version": 3, "file": "postgres.js", "sources": ["../../../../src/node/integrations/postgres.ts"], "sourcesContent": ["import type { Hub } from '@sentry/core';\nimport type { EventProcessor } from '@sentry/types';\nimport { fill, isThenable, loadModule, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../../common/debug-build';\nimport type { LazyLoadedIntegration } from './lazy';\nimport { shouldDisableAutoInstrumentation } from './utils/node-utils';\n\ntype PgClientQuery = (\n  config: unknown,\n  values?: unknown,\n  callback?: (err: unknown, result: unknown) => void,\n) => void | Promise<unknown>;\n\ninterface PgClient {\n  prototype: {\n    query: PgClientQuery;\n  };\n}\n\ninterface PgClientThis {\n  database?: string;\n  host?: string;\n  port?: number;\n  user?: string;\n}\n\ninterface PgOptions {\n  usePgNative?: boolean;\n  /**\n   * Supply your postgres module directly, instead of having Sentry attempt automatic resolution.\n   * Use this if you (a) use a module that's not `pg`, or (b) use a bundler that breaks resolution (e.g. esbuild).\n   *\n   * Usage:\n   * ```\n   * import pg from 'pg';\n   *\n   * Sentry.init({\n   *   integrations: [new Sentry.Integrations.Postgres({ module: pg })],\n   * });\n   * ```\n   */\n  module?: PGModule;\n}\n\ntype PGModule = { Client: PgClient; native: { Client: PgClient } | null };\n\n/** Tracing integration for node-postgres package */\nexport class Postgres implements LazyLoadedIntegration<PGModule> {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Postgres';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string;\n\n  private _usePgNative: boolean;\n\n  private _module?: PGModule;\n\n  public constructor(options: PgOptions = {}) {\n    this.name = Postgres.id;\n    this._usePgNative = !!options.usePgNative;\n    this._module = options.module;\n  }\n\n  /** @inheritdoc */\n  public loadDependency(): PGModule | undefined {\n    return (this._module = this._module || loadModule('pg'));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    if (shouldDisableAutoInstrumentation(getCurrentHub)) {\n      DEBUG_BUILD && logger.log('Postgres Integration is skipped because of instrumenter configuration.');\n      return;\n    }\n\n    const pkg = this.loadDependency();\n\n    if (!pkg) {\n      DEBUG_BUILD && logger.error('Postgres Integration was unable to require `pg` package.');\n      return;\n    }\n\n    const Client = this._usePgNative ? pkg.native?.Client : pkg.Client;\n\n    if (!Client) {\n      DEBUG_BUILD && logger.error(\"Postgres Integration was unable to access 'pg-native' bindings.\");\n      return;\n    }\n\n    /**\n     * function (query, callback) => void\n     * function (query, params, callback) => void\n     * function (query) => Promise\n     * function (query, params) => Promise\n     * function (pg.Cursor) => pg.Cursor\n     */\n    fill(Client.prototype, 'query', function (orig: PgClientQuery) {\n      return function (this: PgClientThis, config: unknown, values: unknown, callback: unknown) {\n        // eslint-disable-next-line deprecation/deprecation\n        const scope = getCurrentHub().getScope();\n        // eslint-disable-next-line deprecation/deprecation\n        const parentSpan = scope.getSpan();\n\n        const data: Record<string, string | number> = {\n          'db.system': 'postgresql',\n        };\n\n        try {\n          if (this.database) {\n            data['db.name'] = this.database;\n          }\n          if (this.host) {\n            data['server.address'] = this.host;\n          }\n          if (this.port) {\n            data['server.port'] = this.port;\n          }\n          if (this.user) {\n            data['db.user'] = this.user;\n          }\n        } catch (e) {\n          // ignore\n        }\n\n        // eslint-disable-next-line deprecation/deprecation\n        const span = parentSpan?.startChild({\n          description: typeof config === 'string' ? config : (config as { text: string }).text,\n          op: 'db',\n          origin: 'auto.db.postgres',\n          data,\n        });\n\n        if (typeof callback === 'function') {\n          return orig.call(this, config, values, function (err: Error, result: unknown) {\n            span?.end();\n            callback(err, result);\n          });\n        }\n\n        if (typeof values === 'function') {\n          return orig.call(this, config, function (err: Error, result: unknown) {\n            span?.end();\n            values(err, result);\n          });\n        }\n\n        const rv = typeof values !== 'undefined' ? orig.call(this, config, values) : orig.call(this, config);\n\n        if (isThenable(rv)) {\n          return rv.then((res: unknown) => {\n            span?.end();\n            return res;\n          });\n        }\n\n        span?.end();\n        return rv;\n      };\n    });\n  }\n}\n"], "names": ["loadModule", "shouldDisableAutoInstrumentation", "DEBUG_BUILD", "logger", "fill", "isThenable"], "mappings": ";;;;;;;;;;AA+CA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACO,CAAA,CAAA,CAAA,CAAA,EAAM,UAAoD;EACjE,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;GACS,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,YAAA,CAAA,EAAA,CAAA,IAAA,CAAO,CAAA,EAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU;;EAEvC,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;;GAOS,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAc,EAAE,EAAE;IAC1C,IAAI,CAAC,CAAA,CAAA,CAAA,IAAO,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,EAAE;IACvB,CAAA,CAAA,CAAA,CAAI,CAAC,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;IACzC,IAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAU,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,MAAM;EAC/B;;EAEF,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;GACS,cAAc,CAAyB,EAAA;IAC5C,CAAQ,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAWA,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAU,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAC;EAC1D;;EAEF,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;EACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACS,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,EAAsC,aAAa,EAAmB;IACtF,CAAIC,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAgC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,EAAE;MACnDC,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,KAAeC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,GAAG,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwE,CAAC;MACnG,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,MAAM,CAAI,CAAA,EAAA,EAAE,IAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE;;IAEjC,CAAA,EAAA,CAAI,CAAC,CAAA,CAAA,CAAG,EAAE;MACRD,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,KAAeC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D,CAAC;MACvF,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAS,CAAG,CAAA,CAAA,CAAC,MAAM;;IAElE,CAAA,EAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE;MACXD,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,KAAeC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiE,CAAC;MAC9F,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEJ,CAAA,CAAA;KACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;KACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;KACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;KACA,CAAA;IACIC,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,EAAiB;MAC7D,OAAO,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAW,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,QAAQ,EAAW;QAChG,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAA,CAAA,CAAA,CAAA,EAAM,MAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE;QAChD,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,MAAM,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE;;QAElC,CAAA,CAAA,CAAA,CAAA,EAAM,KAAwC,EAAA;UAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACnC,CAAS;;QAED,CAAI,CAAA,EAAA;UACF,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE;YACjB,CAAA,CAAA,CAAA,CAAI,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAI,CAAI,CAAA,CAAA,CAAA,CAAC,QAAQ;UACjC;UACA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE;YACb,CAAA,CAAA,CAAA,CAAI,CAAC,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAI,CAAI,CAAA,CAAA,CAAA,CAAC,IAAI;UACpC;UACA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE;YACb,CAAA,CAAA,CAAA,CAAI,CAAC,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAI,CAAI,CAAA,CAAA,CAAA,CAAC,IAAI;UACjC;UACA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE;YACb,CAAA,CAAA,CAAA,CAAI,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAI,CAAI,CAAA,CAAA,CAAA,CAAC,IAAI;UAC7B;QACA,EAAA,CAAO,CAAA,CAAA,CAAA,EAAA,CAAA,CAAC,EAAE;UACpB,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ;;QAER,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAA,CAAA,CAAA,CAAA,EAAM,KAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAC,MAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;UAClC,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,EAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAqB,CAAI,CAAA,CAAA,CAAA;UACpF,CAAA,CAAE,EAAE,CAAI,CAAA,CAAA,CAAA;UACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UAC1B,CAAI,CAAA,CAAA,CAAA;QACL,CAAA,CAAC,CAAA,CAAA;;QAEF,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,UAAU,EAAE;UAClC,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAI,CAAA,CAAA,CAAA,EAAE,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,MAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAU,CAAG,CAAA,CAAA,EAAS,MAAM,EAAW;YACxF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAE,CAAG,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA;YACX,QAAQ,CAAC,CAAA,CAAA,CAAG,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC;UACjC,CAAW,CAAC;QACJ;;QAEA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,UAAU,EAAE;UAChC,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAU,CAAG,CAAA,CAAA,EAAS,MAAM,EAAW;YAChF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAE,CAAG,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA;YACX,MAAM,CAAC,CAAA,CAAA,CAAG,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC;UAC/B,CAAW,CAAC;QACJ;;QAEA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAG,EAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,EAAI,CAAI,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,EAAE,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC;;QAEpG,CAAIC,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAU,CAAC,CAAA,CAAE,CAAC,EAAE;UAClB,OAAO,CAAE,CAAA,CAAC,IAAI,CAAC,CAAC,GAAG,EAAc,CAAA,EAAA;YAC3C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAE,CAAG,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA;YACX,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,GAAG;UACtB,CAAW,CAAC;QACJ;;QAER,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAE,CAAG,CAAA,CAAA,EAAA,MAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA;QACX,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE;MACjB,CAAO;IACP,CAAK,CAAC;EACJ;AACF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;"}