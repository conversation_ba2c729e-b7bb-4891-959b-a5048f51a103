import type { HandlerDataDom } from '@sentry/types';
/**
 * Add an instrumentation handler for when a click or a keypress happens.
 *
 * Use at your own risk, this might break without changelog notice, only used internally.
 * @hidden
 */
export declare function addClickKeypressInstrumentationHandler(handler: (data: HandlerDataDom) => void): void;
/** Exported for tests only. */
export declare function instrumentDOM(): void;
//# sourceMappingURL=dom.d.ts.map