{"version": 3, "file": "baseclient.d.ts", "sourceRoot": "", "sources": ["../../src/baseclient.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,UAAU,EACV,cAAc,EACd,MAAM,EACN,aAAa,EACb,YAAY,EACZ,aAAa,EACb,sBAAsB,EACtB,QAAQ,EAER,KAAK,EACL,eAAe,EACf,SAAS,EACT,cAAc,EACd,aAAa,EACb,WAAW,EACX,gBAAgB,EAChB,gBAAgB,EAChB,iBAAiB,EACjB,OAAO,EACP,mBAAmB,EACnB,WAAW,EACX,OAAO,EACP,iBAAiB,EACjB,QAAQ,EACR,aAAa,EACb,gBAAgB,EAChB,WAAW,EAEX,SAAS,EACT,4BAA4B,EAC7B,MAAM,eAAe,CAAC;AAsBvB,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;AAItD,OAAO,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAOrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,8BAAsB,UAAU,CAAC,CAAC,SAAS,aAAa,CAAE,YAAW,MAAM,CAAC,CAAC,CAAC;IAC5E;;;;OAIG;IACI,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;IAE7C,iCAAiC;IACjC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;IAE/B,2FAA2F;IAC3F,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC;IAExC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,SAAS,CAAC;IAE1C,oCAAoC;IACpC,SAAS,CAAC,aAAa,EAAE,gBAAgB,CAAC;IAE1C,qEAAqE;IACrE,SAAS,CAAC,wBAAwB,EAAE,OAAO,CAAC;IAE5C,sCAAsC;IACtC,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC;IAEjC,SAAS,CAAC,gBAAgB,EAAE,cAAc,EAAE,CAAC;IAE7C,uBAAuB;IACvB,OAAO,CAAC,SAAS,CAA4B;IAG7C,OAAO,CAAC,MAAM,CAA6B;IAE3C;;;;OAIG;IACH,SAAS,aAAa,OAAO,EAAE,CAAC;IA0BhC;;OAEG;IAEI,gBAAgB,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS;IAoB5F;;OAEG;IACI,cAAc,CACnB,OAAO,EAAE,mBAAmB,EAE5B,KAAK,CAAC,EAAE,QAAQ,GAAG,aAAa,EAChC,IAAI,CAAC,EAAE,SAAS,EAChB,KAAK,CAAC,EAAE,KAAK,GACZ,MAAM,GAAG,SAAS;IAoBrB;;OAEG;IACI,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,SAAS;IAqBtF;;OAEG;IACI,cAAc,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAU7C;;OAEG;IACI,MAAM,IAAI,aAAa,GAAG,SAAS;IAI1C;;OAEG;IACI,UAAU,IAAI,CAAC;IAItB;;;;OAIG;IACI,cAAc,IAAI,WAAW,GAAG,SAAS;IAIhD;;OAEG;IACI,YAAY,IAAI,SAAS,GAAG,SAAS;IAI5C;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IAcpD;;OAEG;IACI,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IAUpD,0CAA0C;IACnC,kBAAkB,IAAI,cAAc,EAAE;IAI7C,kBAAkB;IACX,iBAAiB,CAAC,cAAc,EAAE,cAAc,GAAG,IAAI;IAI9D;;;OAGG;IACI,iBAAiB,CAAC,eAAe,CAAC,EAAE,OAAO,GAAG,IAAI;IAMzD,kBAAkB;IACX,IAAI,IAAI,IAAI;IAMnB;;;;;OAKG;IACI,kBAAkB,CAAC,aAAa,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;IAIzE;;;;OAIG;IACI,oBAAoB,CAAC,CAAC,SAAS,WAAW,GAAG,WAAW,EAAE,eAAe,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS;IAIxG;;;OAGG;IACI,cAAc,CAAC,CAAC,SAAS,WAAW,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI;IASxF;;OAEG;IACI,cAAc,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI;IAWrD;;OAEG;IACI,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,SAAc,GAAG,IAAI;IAqB1D;;OAEG;IACI,WAAW,CAAC,OAAO,EAAE,OAAO,GAAG,iBAAiB,GAAG,IAAI;IAQ9D;;OAEG;IACI,kBAAkB,CAAC,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,IAAI;IAkB/G;;OAEG;IACI,uBAAuB,CAAC,iBAAiB,EAAE,KAAK,CAAC,gBAAgB,CAAC,GAAG,IAAI;IAiBhF,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,IAAI,GAAG,IAAI;IAEvF,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,CAAC,WAAW,EAAE,WAAW,KAAK,IAAI,GAAG,IAAI;IAExF,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAE/E,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,KAAK,IAAI,GAAG,IAAI;IAE5F,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,KAAK,IAAI,GAAG,IAAI;IAE5F,kBAAkB;IACX,EAAE,CACP,IAAI,EAAE,gBAAgB,EACtB,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,4BAA4B,GAAG,IAAI,KAAK,IAAI,GAClF,IAAI;IAEP,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,cAAc,KAAK,IAAI,GAAG,IAAI;IAE/G,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,sBAAsB,KAAK,IAAI,GAAG,IAAI;IAEnF,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE;QAAE,IAAI,EAAE,OAAO,CAAA;KAAE,KAAK,IAAI,GAAG,IAAI;IAE9G,kBAAkB;IACX,EAAE,CACP,IAAI,EAAE,oBAAoB,EAC1B,QAAQ,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE;QAAE,aAAa,EAAE,OAAO,CAAA;KAAE,KAAK,IAAI,GAChF,IAAI;IAEP,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI;IAEzF,kBAAkB;IACX,EAAE,CAAC,IAAI,EAAE,qBAAqB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,gBAAgB,KAAK,IAAI,GAAG,IAAI;IAY3F,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,kBAAkB,EAAE,WAAW,EAAE,WAAW,GAAG,IAAI;IAErE,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAE,WAAW,EAAE,WAAW,GAAG,IAAI;IAEtE,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,QAAQ,GAAG,IAAI;IAE7D,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI;IAE1E,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,SAAS,GAAG,IAAI;IAE1E,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,4BAA4B,GAAG,IAAI,GAAG,IAAI;IAE1G,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,cAAc,GAAG,IAAI;IAE7F,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,sBAAsB,GAAG,IAAI;IAEjE,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE;QAAE,IAAI,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI;IAE5F,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,EAAE;QAAE,aAAa,EAAE,OAAO,CAAA;KAAE,GAAG,IAAI;IAE5G,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAEvE,kBAAkB;IACX,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAE,OAAO,EAAE,gBAAgB,GAAG,IAAI;IAWzE,0CAA0C;IAC1C,SAAS,CAAC,kBAAkB,IAAI,IAAI;IASpC,2DAA2D;IAC3D,SAAS,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI;IAgCvE;;;;;;;;;OASG;IACH,SAAS,CAAC,uBAAuB,CAAC,OAAO,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC;IAoBzE,yEAAyE;IACzE,SAAS,CAAC,UAAU,IAAI,OAAO;IAI/B;;;;;;;;;;;;;OAaG;IACH,SAAS,CAAC,aAAa,CACrB,KAAK,EAAE,KAAK,EACZ,IAAI,EAAE,SAAS,EACf,KAAK,CAAC,EAAE,KAAK,EACb,cAAc,QAAsB,GACnC,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;IA0C5B;;;;;OAKG;IACH,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,GAAE,SAAc,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,SAAS,CAAC;IAqB3G;;;;;;;;;;;;OAYG;IACH,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;IAuGzF;;OAEG;IACH,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;IAcpD;;OAEG;IACH,SAAS,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,GAAG,WAAW,CAAC,IAAI,GAAG,4BAA4B,CAAC,GAAG,IAAI;IAYpG;;OAEG;IACH,SAAS,CAAC,cAAc,IAAI,OAAO,EAAE;IAarC;;OAEG;aAEa,kBAAkB,CAAC,UAAU,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;IAE1F;;OAEG;aACa,gBAAgB,CAC9B,QAAQ,EAAE,mBAAmB,EAE7B,MAAM,CAAC,EAAE,QAAQ,GAAG,aAAa,EACjC,KAAK,CAAC,EAAE,SAAS,GAChB,WAAW,CAAC,KAAK,CAAC;CACtB;AAkED;;;GAGG;AACH,wBAAgB,iBAAiB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI,CAQhE"}