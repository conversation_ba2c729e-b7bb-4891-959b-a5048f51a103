{"version": 3, "file": "integration.js", "sources": ["../../src/integration.ts"], "sourcesContent": ["import type {\n  Client,\n  Event,\n  EventHint,\n  EventProcessor,\n  Integration,\n  IntegrationClass,\n  IntegrationFn,\n  IntegrationFnResult,\n  Options,\n} from '@sentry/types';\nimport { arrayify, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from './debug-build';\nimport { addGlobalEventProcessor } from './eventProcessors';\nimport { getClient } from './exports';\nimport { getCurrentHub } from './hub';\n\ndeclare module '@sentry/types' {\n  interface Integration {\n    isDefaultInstance?: boolean;\n  }\n}\n\nexport const installedIntegrations: string[] = [];\n\n/** Map of integrations assigned to a client */\nexport type IntegrationIndex = {\n  [key: string]: Integration;\n};\n\n/**\n * Remove duplicates from the given array, preferring the last instance of any duplicate. Not guaranteed to\n * preseve the order of integrations in the array.\n *\n * @private\n */\nfunction filterDuplicates(integrations: Integration[]): Integration[] {\n  const integrationsByName: { [key: string]: Integration } = {};\n\n  integrations.forEach(currentInstance => {\n    const { name } = currentInstance;\n\n    const existingInstance = integrationsByName[name];\n\n    // We want integrations later in the array to overwrite earlier ones of the same type, except that we never want a\n    // default instance to overwrite an existing user instance\n    if (existingInstance && !existingInstance.isDefaultInstance && currentInstance.isDefaultInstance) {\n      return;\n    }\n\n    integrationsByName[name] = currentInstance;\n  });\n\n  return Object.keys(integrationsByName).map(k => integrationsByName[k]);\n}\n\n/** Gets integrations to install */\nexport function getIntegrationsToSetup(options: Pick<Options, 'defaultIntegrations' | 'integrations'>): Integration[] {\n  const defaultIntegrations = options.defaultIntegrations || [];\n  const userIntegrations = options.integrations;\n\n  // We flag default instances, so that later we can tell them apart from any user-created instances of the same class\n  defaultIntegrations.forEach(integration => {\n    integration.isDefaultInstance = true;\n  });\n\n  let integrations: Integration[];\n\n  if (Array.isArray(userIntegrations)) {\n    integrations = [...defaultIntegrations, ...userIntegrations];\n  } else if (typeof userIntegrations === 'function') {\n    integrations = arrayify(userIntegrations(defaultIntegrations));\n  } else {\n    integrations = defaultIntegrations;\n  }\n\n  const finalIntegrations = filterDuplicates(integrations);\n\n  // The `Debug` integration prints copies of the `event` and `hint` which will be passed to `beforeSend` or\n  // `beforeSendTransaction`. It therefore has to run after all other integrations, so that the changes of all event\n  // processors will be reflected in the printed values. For lack of a more elegant way to guarantee that, we therefore\n  // locate it and, assuming it exists, pop it out of its current spot and shove it onto the end of the array.\n  const debugIndex = findIndex(finalIntegrations, integration => integration.name === 'Debug');\n  if (debugIndex !== -1) {\n    const [debugInstance] = finalIntegrations.splice(debugIndex, 1);\n    finalIntegrations.push(debugInstance);\n  }\n\n  return finalIntegrations;\n}\n\n/**\n * Given a list of integration instances this installs them all. When `withDefaults` is set to `true` then all default\n * integrations are added unless they were already provided before.\n * @param integrations array of integration instances\n * @param withDefault should enable default integrations\n */\nexport function setupIntegrations(client: Client, integrations: Integration[]): IntegrationIndex {\n  const integrationIndex: IntegrationIndex = {};\n\n  integrations.forEach(integration => {\n    // guard against empty provided integrations\n    if (integration) {\n      setupIntegration(client, integration, integrationIndex);\n    }\n  });\n\n  return integrationIndex;\n}\n\n/**\n * Execute the `afterAllSetup` hooks of the given integrations.\n */\nexport function afterSetupIntegrations(client: Client, integrations: Integration[]): void {\n  for (const integration of integrations) {\n    // guard against empty provided integrations\n    if (integration && integration.afterAllSetup) {\n      integration.afterAllSetup(client);\n    }\n  }\n}\n\n/** Setup a single integration.  */\nexport function setupIntegration(client: Client, integration: Integration, integrationIndex: IntegrationIndex): void {\n  if (integrationIndex[integration.name]) {\n    DEBUG_BUILD && logger.log(`Integration skipped because it was already installed: ${integration.name}`);\n    return;\n  }\n  integrationIndex[integration.name] = integration;\n\n  // `setupOnce` is only called the first time\n  if (installedIntegrations.indexOf(integration.name) === -1) {\n    // eslint-disable-next-line deprecation/deprecation\n    integration.setupOnce(addGlobalEventProcessor, getCurrentHub);\n    installedIntegrations.push(integration.name);\n  }\n\n  // `setup` is run for each client\n  if (integration.setup && typeof integration.setup === 'function') {\n    integration.setup(client);\n  }\n\n  if (client.on && typeof integration.preprocessEvent === 'function') {\n    const callback = integration.preprocessEvent.bind(integration) as typeof integration.preprocessEvent;\n    client.on('preprocessEvent', (event, hint) => callback(event, hint, client));\n  }\n\n  if (client.addEventProcessor && typeof integration.processEvent === 'function') {\n    const callback = integration.processEvent.bind(integration) as typeof integration.processEvent;\n\n    const processor: EventProcessor = (event: Event, hint: EventHint): ReturnType<typeof callback> =>\n      callback(event, hint, client);\n    processor.id = integration.name;\n\n    client.addEventProcessor(processor);\n  }\n\n  DEBUG_BUILD && logger.log(`Integration installed: ${integration.name}`);\n}\n\n/** Add an integration to the current hub's client. */\nexport function addIntegration(integration: Integration): void {\n  const client = getClient();\n\n  if (!client || !client.addIntegration) {\n    DEBUG_BUILD && logger.warn(`Cannot add integration \"${integration.name}\" because no SDK Client is available.`);\n    return;\n  }\n\n  client.addIntegration(integration);\n}\n\n// Polyfill for Array.findIndex(), which is not supported in ES5\nfunction findIndex<T>(arr: T[], callback: (item: T) => boolean): number {\n  for (let i = 0; i < arr.length; i++) {\n    if (callback(arr[i]) === true) {\n      return i;\n    }\n  }\n\n  return -1;\n}\n\n/**\n * Convert a new integration function to the legacy class syntax.\n * In v8, we can remove this and instead export the integration functions directly.\n *\n * @deprecated This will be removed in v8!\n */\nexport function convertIntegrationFnToClass<Fn extends IntegrationFn>(\n  name: string,\n  fn: Fn,\n): IntegrationClass<Integration> {\n  const ConvertedIntegration = function ConvertedIntegration(...args: Parameters<Fn>): Integration {\n    return fn(...args);\n  };\n  ConvertedIntegration.id = name;\n  return ConvertedIntegration as unknown as IntegrationClass<Integration>;\n}\n\n/**\n * Define an integration function that can be used to create an integration instance.\n * Note that this by design hides the implementation details of the integration, as they are considered internal.\n */\nexport function defineIntegration<Fn extends IntegrationFn>(fn: Fn): (...args: Parameters<Fn>) => IntegrationFnResult {\n  return fn;\n}\n"], "names": ["arrayify", "DEBUG_BUILD", "logger", "addGlobalEventProcessor", "getCurrentHub", "getClient"], "mappings": ";;;;;;;;AAwBa,MAAA,qBAAqB,GAAa,GAAE;AACjD;AACA;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,YAAY,EAAgC;AACtE,EAAE,MAAM,kBAAkB,GAAmC,EAAE,CAAA;AAC/D;AACA,EAAE,YAAY,CAAC,OAAO,CAAC,mBAAmB;AAC1C,IAAI,MAAM,EAAE,IAAK,EAAA,GAAI,eAAe,CAAA;AACpC;AACA,IAAI,MAAM,gBAAiB,GAAE,kBAAkB,CAAC,IAAI,CAAC,CAAA;AACrD;AACA;AACA;AACA,IAAI,IAAI,gBAAiB,IAAG,CAAC,gBAAgB,CAAC,iBAAA,IAAqB,eAAe,CAAC,iBAAiB,EAAE;AACtG,MAAM,OAAM;AACZ,KAAI;AACJ;AACA,IAAI,kBAAkB,CAAC,IAAI,CAAA,GAAI,eAAe,CAAA;AAC9C,GAAG,CAAC,CAAA;AACJ;AACA,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,KAAK,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAA;AACxE,CAAA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,OAAO,EAAwE;AACtH,EAAE,MAAM,sBAAsB,OAAO,CAAC,mBAAoB,IAAG,EAAE,CAAA;AAC/D,EAAE,MAAM,gBAAA,GAAmB,OAAO,CAAC,YAAY,CAAA;AAC/C;AACA;AACA,EAAE,mBAAmB,CAAC,OAAO,CAAC,eAAe;AAC7C,IAAI,WAAW,CAAC,iBAAkB,GAAE,IAAI,CAAA;AACxC,GAAG,CAAC,CAAA;AACJ;AACA,EAAE,IAAI,YAAY,CAAA;AAClB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;AACvC,IAAI,YAAA,GAAe,CAAC,GAAG,mBAAmB,EAAE,GAAG,gBAAgB,CAAC,CAAA;AAChE,GAAE,MAAO,IAAI,OAAO,gBAAiB,KAAI,UAAU,EAAE;AACrD,IAAI,YAAA,GAAeA,cAAQ,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,CAAA;AAClE,SAAS;AACT,IAAI,YAAA,GAAe,mBAAmB,CAAA;AACtC,GAAE;AACF;AACA,EAAE,MAAM,iBAAkB,GAAE,gBAAgB,CAAC,YAAY,CAAC,CAAA;AAC1D;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,UAAA,GAAa,SAAS,CAAC,iBAAiB,EAAE,WAAY,IAAG,WAAW,CAAC,IAAK,KAAI,OAAO,CAAC,CAAA;AAC9F,EAAE,IAAI,UAAA,KAAe,CAAC,CAAC,EAAE;AACzB,IAAI,MAAM,CAAC,aAAa,CAAA,GAAI,iBAAiB,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAA;AACnE,IAAI,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;AACzC,GAAE;AACF;AACA,EAAE,OAAO,iBAAiB,CAAA;AAC1B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,MAAM,EAAU,YAAY,EAAmC;AACjG,EAAE,MAAM,gBAAgB,GAAqB,EAAE,CAAA;AAC/C;AACA,EAAE,YAAY,CAAC,OAAO,CAAC,eAAe;AACtC;AACA,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,gBAAgB,CAAC,MAAM,EAAE,WAAW,EAAE,gBAAgB,CAAC,CAAA;AAC7D,KAAI;AACJ,GAAG,CAAC,CAAA;AACJ;AACA,EAAE,OAAO,gBAAgB,CAAA;AACzB,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,MAAM,EAAU,YAAY,EAAuB;AAC1F,EAAE,KAAK,MAAM,WAAY,IAAG,YAAY,EAAE;AAC1C;AACA,IAAI,IAAI,WAAA,IAAe,WAAW,CAAC,aAAa,EAAE;AAClD,MAAM,WAAW,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;AACvC,KAAI;AACJ,GAAE;AACF,CAAA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,MAAM,EAAU,WAAW,EAAe,gBAAgB,EAA0B;AACrH,EAAE,IAAI,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AAC1C,IAAIC,sBAAY,IAAGC,YAAM,CAAC,GAAG,CAAC,CAAC,sDAAsD,EAAE,WAAW,CAAC,IAAI,CAAC,CAAA,CAAA,CAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA,EAAA,gBAAA,CAAA,WAAA,CAAA,IAAA,CAAA,GAAA,WAAA,CAAA;AACA;AACA;AACA,EAAA,IAAA,qBAAA,CAAA,OAAA,CAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,EAAA;AACA;AACA,IAAA,WAAA,CAAA,SAAA,CAAAC,uCAAA,EAAAC,iBAAA,CAAA,CAAA;AACA,IAAA,qBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA;AACA,EAAA,IAAA,WAAA,CAAA,KAAA,IAAA,OAAA,WAAA,CAAA,KAAA,KAAA,UAAA,EAAA;AACA,IAAA,WAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,MAAA,CAAA,EAAA,IAAA,OAAA,WAAA,CAAA,eAAA,KAAA,UAAA,EAAA;AACA,IAAA,MAAA,QAAA,GAAA,WAAA,CAAA,eAAA,CAAA,IAAA,CAAA,WAAA,CAAA,EAAA;AACA,IAAA,MAAA,CAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,KAAA,EAAA,IAAA,KAAA,QAAA,CAAA,KAAA,EAAA,IAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,MAAA,CAAA,iBAAA,IAAA,OAAA,WAAA,CAAA,YAAA,KAAA,UAAA,EAAA;AACA,IAAA,MAAA,QAAA,GAAA,WAAA,CAAA,YAAA,CAAA,IAAA,CAAA,WAAA,CAAA,EAAA;AACA;AACA,IAAA,MAAA,SAAA,GAAA,CAAA,KAAA,EAAA,IAAA;AACA,MAAA,QAAA,CAAA,KAAA,EAAA,IAAA,EAAA,MAAA,CAAA,CAAA;AACA,IAAA,SAAA,CAAA,EAAA,GAAA,WAAA,CAAA,IAAA,CAAA;AACA;AACA,IAAA,MAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,CAAA;AACA,GAAA;AACA;AACA,EAAAH,sBAAA,IAAAC,YAAA,CAAA,GAAA,CAAA,CAAA,uBAAA,EAAA,WAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,cAAA,CAAA,WAAA,EAAA;AACA,EAAA,MAAA,MAAA,GAAAG,mBAAA,EAAA,CAAA;AACA;AACA,EAAA,IAAA,CAAA,MAAA,IAAA,CAAA,MAAA,CAAA,cAAA,EAAA;AACA,IAAAJ,sBAAA,IAAAC,YAAA,CAAA,IAAA,CAAA,CAAA,wBAAA,EAAA,WAAA,CAAA,IAAA,CAAA,qCAAA,CAAA,CAAA,CAAA;AACA,IAAA,OAAA;AACA,GAAA;AACA;AACA,EAAA,MAAA,CAAA,cAAA,CAAA,WAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA,SAAA,SAAA,CAAA,GAAA,EAAA,QAAA,EAAA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,GAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,IAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,KAAA,IAAA,EAAA;AACA,MAAA,OAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,2BAAA;AACA,EAAA,IAAA;AACA,EAAA,EAAA;AACA,EAAA;AACA,EAAA,MAAA,oBAAA,GAAA,SAAA,oBAAA,CAAA,GAAA,IAAA,EAAA;AACA,IAAA,OAAA,EAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AACA,GAAA,CAAA;AACA,EAAA,oBAAA,CAAA,EAAA,GAAA,IAAA,CAAA;AACA,EAAA,OAAA,oBAAA,EAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,iBAAA,CAAA,EAAA,EAAA;AACA,EAAA,OAAA,EAAA,CAAA;AACA;;;;;;;;;;;"}