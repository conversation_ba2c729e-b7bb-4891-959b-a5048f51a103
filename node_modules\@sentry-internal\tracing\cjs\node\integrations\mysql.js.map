{"version": 3, "file": "mysql.js", "sources": ["../../../../src/node/integrations/mysql.ts"], "sourcesContent": ["import type { Hub } from '@sentry/core';\nimport type { EventProcessor, Span } from '@sentry/types';\nimport { fill, loadModule, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../../common/debug-build';\nimport type { LazyLoadedIntegration } from './lazy';\nimport { shouldDisableAutoInstrumentation } from './utils/node-utils';\n\ninterface MysqlConnection {\n  prototype: {\n    connect: () => void;\n  };\n  createQuery: () => void;\n}\n\ninterface MysqlConnectionConfig {\n  host: string;\n  port: number;\n  user: string;\n}\n\n/** Tracing integration for node-mysql package */\nexport class Mysql implements LazyLoadedIntegration<MysqlConnection> {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Mysql';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string;\n\n  private _module?: MysqlConnection;\n\n  public constructor() {\n    this.name = Mysql.id;\n  }\n\n  /** @inheritdoc */\n  public loadDependency(): MysqlConnection | undefined {\n    return (this._module = this._module || loadModule('mysql/lib/Connection.js'));\n  }\n\n  /**\n   * @inheritDoc\n   */\n  // eslint-disable-next-line deprecation/deprecation\n  public setupOnce(_: (callback: EventProcessor) => void, getCurrentHub: () => Hub): void {\n    if (shouldDisableAutoInstrumentation(getCurrentHub)) {\n      DEBUG_BUILD && logger.log('Mysql Integration is skipped because of instrumenter configuration.');\n      return;\n    }\n\n    const pkg = this.loadDependency();\n\n    if (!pkg) {\n      DEBUG_BUILD && logger.error('Mysql Integration was unable to require `mysql` package.');\n      return;\n    }\n\n    let mySqlConfig: MysqlConnectionConfig | undefined = undefined;\n\n    try {\n      pkg.prototype.connect = new Proxy(pkg.prototype.connect, {\n        apply(wrappingTarget, thisArg: { config: MysqlConnectionConfig }, args) {\n          if (!mySqlConfig) {\n            mySqlConfig = thisArg.config;\n          }\n          return wrappingTarget.apply(thisArg, args);\n        },\n      });\n    } catch (e) {\n      DEBUG_BUILD && logger.error('Mysql Integration was unable to instrument `mysql` config.');\n    }\n\n    function spanDataFromConfig(): Record<string, string | number | undefined> {\n      if (!mySqlConfig) {\n        return {};\n      }\n      return {\n        'server.address': mySqlConfig.host,\n        'server.port': mySqlConfig.port,\n        'db.user': mySqlConfig.user,\n      };\n    }\n\n    function finishSpan(span: Span | undefined): void {\n      if (!span) {\n        return;\n      }\n\n      const data = spanDataFromConfig();\n      Object.keys(data).forEach(key => {\n        span.setAttribute(key, data[key]);\n      });\n\n      span.end();\n    }\n\n    // The original function will have one of these signatures:\n    //    function (callback) => void\n    //    function (options, callback) => void\n    //    function (options, values, callback) => void\n    fill(pkg, 'createQuery', function (orig: () => void) {\n      return function (this: unknown, options: unknown, values: unknown, callback: unknown) {\n        // eslint-disable-next-line deprecation/deprecation\n        const scope = getCurrentHub().getScope();\n        // eslint-disable-next-line deprecation/deprecation\n        const parentSpan = scope.getSpan();\n\n        // eslint-disable-next-line deprecation/deprecation\n        const span = parentSpan?.startChild({\n          description: typeof options === 'string' ? options : (options as { sql: string }).sql,\n          op: 'db',\n          origin: 'auto.db.mysql',\n          data: {\n            'db.system': 'mysql',\n          },\n        });\n\n        if (typeof callback === 'function') {\n          return orig.call(this, options, values, function (err: Error, result: unknown, fields: unknown) {\n            finishSpan(span);\n            callback(err, result, fields);\n          });\n        }\n\n        if (typeof values === 'function') {\n          return orig.call(this, options, function (err: Error, result: unknown, fields: unknown) {\n            finishSpan(span);\n            values(err, result, fields);\n          });\n        }\n\n        // streaming, no callback!\n        const query = orig.call(this, options, values) as { on: (event: string, callback: () => void) => void };\n\n        query.on('end', () => {\n          finishSpan(span);\n        });\n\n        return query;\n      };\n    });\n  }\n}\n"], "names": ["loadModule", "shouldDisableAutoInstrumentation", "DEBUG_BUILD", "logger", "fill"], "mappings": ";;;;;;;;;;AAqBA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;AACO,CAAA,CAAA,CAAA,CAAA,EAAM,OAAwD;EACrE,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;GACS,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,YAAA,CAAA,EAAA,CAAA,IAAA,CAAO,CAAA,EAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;;EAEpC,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;;GAKS,WAAW,CAAG,EAAA;IACnB,IAAI,CAAC,CAAA,CAAA,CAAA,IAAO,CAAK,CAAA,CAAA,CAAA,CAAA,CAAC,EAAE;EACtB;;EAEF,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;GACS,cAAc,CAAgC,EAAA;IACnD,CAAQ,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAWA,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAU,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAC,CAAC;EAC/E;;EAEF,CAAA,CAAA;GACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACA,CAAA;EACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;GACS,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,EAAsC,aAAa,EAAmB;IACtF,CAAIC,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAgC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAC,EAAE;MACnDC,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,KAAeC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,GAAG,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqE,CAAC;MAChG,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,MAAM,CAAI,CAAA,EAAA,EAAE,IAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE;;IAEjC,CAAA,EAAA,CAAI,CAAC,CAAA,CAAA,CAAG,EAAE;MACRD,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,KAAeC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0D,CAAC;MACvF,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;IACR;;IAEA,CAAI,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiD,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;;IAE9D,CAAI,CAAA,EAAA;MACF,CAAG,CAAA,CAAA,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,QAAU,EAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAC,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,OAAO,EAAE;QACvD,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAqC,IAAI,EAAE;UACtE,CAAA,EAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAE;YAChB,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,MAAM;UAC9B;UACA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,cAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAI,CAAA,CAAA,CAAA,CAAC;QAC3C,CAAA;MACT,CAAO,CAAC;IACF,EAAA,CAAO,CAAA,CAAA,CAAA,EAAA,CAAA,CAAC,EAAE;MACVD,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,KAAeC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA4D,CAAC;IAC3F;;IAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,kBAAkB,CAAgD,EAAA;MACzE,CAAA,EAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAE;QAChB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,EAAE;MACX;MACA,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA;QACL,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA;QAClC,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA;QAC/B,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA;MACnC,CAAO;IACH;;IAEA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAA,CAAA,CAAA,CAAI,EAA0B;MAChD,CAAA,EAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE;QACT,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;MACR;;MAEA,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAE;MACjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,EAAO,CAAA,EAAA;QAC/B,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAC,CAAA,CAAA,CAAG,EAAE,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAG,CAAC,CAAC;MACzC,CAAO,CAAC;;MAEF,CAAI,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAA,CAAE;IACZ;;IAEJ,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;IACIC,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAI,CAAC,CAAG,CAAA,CAAA,EAAE,aAAa,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAU,IAAI,EAAc;MACnD,OAAO,CAAyB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAW,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,QAAQ,EAAW;QAC5F,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAA,CAAA,CAAA,CAAA,EAAM,MAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAE;QAChD,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,MAAM,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,KAAK,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE;;QAE1C,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAA,CAAA,CAAA,CAAA,EAAM,KAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAC,MAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA;UAClC,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,EAAA,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAoB,CAAG,CAAA,CAAA;UACrF,CAAA,CAAE,EAAE,CAAI,CAAA,CAAA,CAAA;UACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACvB,IAAI,EAAE;YACJ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACrB,CAAA;QACF,CAAA,CAAC,CAAA,CAAA;;QAEF,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,UAAU,EAAE;UAClC,OAAO,CAAI,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,CAAC,IAAI,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAG,EAAS,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,MAAM,EAAW;YAC9F,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,CAAC;YAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAG,CAAA,CAAA,EAAE,MAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC;UACzC,CAAW,CAAC;QACJ;;QAEA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAI,UAAU,EAAE;UAChC,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAI,CAAA,CAAA,CAAA,EAAE,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,UAAU,CAAG,CAAA,CAAA,EAAS,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,MAAM,EAAW;YACtF,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,CAAC;YAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAG,CAAA,CAAA,EAAE,MAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC;UACvC,CAAW,CAAC;QACJ;;QAER,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACQ,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,EAAA,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE;;QAE/C,KAAK,CAAC,CAAA,CAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAK,EAAE,CAAM,EAAA,CAAA,EAAA;UACpB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,CAAC;QAC1B,CAAS,CAAC;;QAEF,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,KAAK;MACpB,CAAO;IACP,CAAK,CAAC;EACJ;AACF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;"}