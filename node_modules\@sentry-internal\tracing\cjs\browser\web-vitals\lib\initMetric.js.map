{"version": 3, "file": "initMetric.js", "sources": ["../../../../../src/browser/web-vitals/lib/initMetric.ts"], "sourcesContent": ["/*\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { WINDOW } from '../../types';\nimport type { Metric } from '../types';\nimport { generateUniqueID } from './generateUniqueID';\nimport { getActivationStart } from './getActivationStart';\nimport { getNavigationEntry } from './getNavigationEntry';\n\nexport const initMetric = (name: Metric['name'], value?: number): Metric => {\n  const navEntry = getNavigationEntry();\n  let navigationType: Metric['navigationType'] = 'navigate';\n\n  if (navEntry) {\n    if ((WINDOW.document && WINDOW.document.prerendering) || getActivationStart() > 0) {\n      navigationType = 'prerender';\n    } else {\n      navigationType = navEntry.type.replace(/_/g, '-') as Metric['navigationType'];\n    }\n  }\n\n  return {\n    name,\n    value: typeof value === 'undefined' ? -1 : value,\n    rating: 'good', // Will be updated if the value changes.\n    delta: 0,\n    entries: [],\n    id: generateUniqueID(),\n    navigationType,\n  };\n};\n"], "names": ["getNavigationEntry", "WINDOW", "getActivationStart", "generateUniqueID"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;MACa,UAAW,GAAE,CAAC,IAAI,EAAkB,KAAK,KAAsB;AAC5E,EAAE,MAAM,QAAA,GAAWA,qCAAkB,EAAE,CAAA;AACvC,EAAE,IAAI,cAAc,GAA6B,UAAU,CAAA;AAC3D;AACA,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,IAAI,CAACC,YAAM,CAAC,QAAA,IAAYA,YAAM,CAAC,QAAQ,CAAC,YAAY,KAAKC,qCAAkB,EAAG,GAAE,CAAC,EAAE;AACvF,MAAM,cAAA,GAAiB,WAAW,CAAA;AAClC,WAAW;AACX,MAAM,cAAA,GAAiB,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAE,EAAA;AACxD,KAAI;AACJ,GAAE;AACF;AACA,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,KAAK,EAAE,OAAO,KAAM,KAAI,cAAc,CAAC,CAAE,GAAE,KAAK;AACpD,IAAI,MAAM,EAAE,MAAM;AAClB,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,OAAO,EAAE,EAAE;AACf,IAAI,EAAE,EAAEC,iCAAgB,EAAE;AAC1B,IAAI,cAAc;AAClB,GAAG,CAAA;AACH;;;;"}