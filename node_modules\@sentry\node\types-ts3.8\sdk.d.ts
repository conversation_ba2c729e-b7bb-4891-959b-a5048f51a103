import { Integration, Options, StackParser } from '@sentry/types';
import { NodeClient } from './client';
import { NodeOptions } from './types';
/** @deprecated Use `getDefaultIntegrations(options)` instead. */
export declare const defaultIntegrations: import("@sentry/types").IntegrationFnResult[];
/** Get the default integrations for the Node SDK. */
export declare function getDefaultIntegrations(_options: Options): Integration[];
/**
 * The Sentry Node SDK Client.
 *
 * To use this SDK, call the {@link init} function as early as possible in the
 * main entry module. To set context information or send manual events, use the
 * provided methods.
 *
 * @example
 * ```
 *
 * const { init } = require('@sentry/node');
 *
 * init({
 *   dsn: '__DSN__',
 *   // ...
 * });
 * ```
 *
 * @example
 * ```
 *
 * const { configureScope } = require('@sentry/node');
 * configureScope((scope: Scope) => {
 *   scope.setExtra({ battery: 0.7 });
 *   scope.setTag({ user_mode: 'admin' });
 *   scope.setUser({ id: '4711' });
 * });
 * ```
 *
 * @example
 * ```
 *
 * const { addBreadcrumb } = require('@sentry/node');
 * addBreadcrumb({
 *   message: 'My Breadcrumb',
 *   // ...
 * });
 * ```
 *
 * @example
 * ```
 *
 * const Sentry = require('@sentry/node');
 * Sentry.captureMessage('Hello, world!');
 * Sentry.captureException(new Error('Good bye'));
 * Sentry.captureEvent({
 *   message: 'Manual',
 *   stacktrace: [
 *     // ...
 *   ],
 * });
 * ```
 *
 * @see {@link NodeOptions} for documentation on configuration options.
 */
export declare function init(options?: NodeOptions): void;
/**
 * Function that takes an instance of NodeClient and checks if autoSessionTracking option is enabled for that client
 */
export declare function isAutoSessionTrackingEnabled(client?: NodeClient): boolean;
/**
 * Returns a release dynamically from environment variables.
 */
export declare function getSentryRelease(fallback?: string): string | undefined;
/** Node.js stack parser */
export declare const defaultStackParser: StackParser;
//# sourceMappingURL=sdk.d.ts.map
