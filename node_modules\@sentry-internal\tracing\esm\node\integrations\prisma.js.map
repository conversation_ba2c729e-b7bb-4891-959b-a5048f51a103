{"version": 3, "file": "prisma.js", "sources": ["../../../../src/node/integrations/prisma.ts"], "sourcesContent": ["import { SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN, getCurrentHub, startSpan } from '@sentry/core';\nimport type { Integration } from '@sentry/types';\nimport { addNonEnumerableProperty, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from '../../common/debug-build';\nimport { shouldDisableAutoInstrumentation } from './utils/node-utils';\n\ntype PrismaAction =\n  | 'findUnique'\n  | 'findMany'\n  | 'findFirst'\n  | 'create'\n  | 'createMany'\n  | 'update'\n  | 'updateMany'\n  | 'upsert'\n  | 'delete'\n  | 'deleteMany'\n  | 'executeRaw'\n  | 'queryRaw'\n  | 'aggregate'\n  | 'count'\n  | 'runCommandRaw';\n\ninterface PrismaMiddlewareParams {\n  model?: unknown;\n  action: PrismaAction;\n  args: unknown;\n  dataPath: string[];\n  runInTransaction: boolean;\n}\n\ntype PrismaMiddleware<T = unknown> = (\n  params: PrismaMiddlewareParams,\n  next: (params: PrismaMiddlewareParams) => Promise<T>,\n) => Promise<T>;\n\ninterface PrismaClient {\n  _sentryInstrumented?: boolean;\n  _engineConfig?: {\n    activeProvider?: string;\n    clientVersion?: string;\n  };\n  $use: (cb: PrismaMiddleware) => void;\n}\n\nfunction isValidPrismaClient(possibleClient: unknown): possibleClient is PrismaClient {\n  return !!possibleClient && !!(possibleClient as PrismaClient)['$use'];\n}\n\n/** Tracing integration for @prisma/client package */\nexport class Prisma implements Integration {\n  /**\n   * @inheritDoc\n   */\n  public static id: string = 'Prisma';\n\n  /**\n   * @inheritDoc\n   */\n  public name: string;\n\n  /**\n   * @inheritDoc\n   */\n  public constructor(options: { client?: unknown } = {}) {\n    this.name = Prisma.id;\n\n    // We instrument the PrismaClient inside the constructor and not inside `setupOnce` because in some cases of server-side\n    // bundling (Next.js) multiple Prisma clients can be instantiated, even though users don't intend to. When instrumenting\n    // in setupOnce we can only ever instrument one client.\n    // https://github.com/getsentry/sentry-javascript/issues/7216#issuecomment-1602375012\n    // In the future we might explore providing a dedicated PrismaClient middleware instead of this hack.\n    if (isValidPrismaClient(options.client) && !options.client._sentryInstrumented) {\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      addNonEnumerableProperty(options.client as any, '_sentryInstrumented', true);\n\n      const clientData: Record<string, string | number> = {};\n      try {\n        const engineConfig = (options.client as PrismaClient)._engineConfig;\n        if (engineConfig) {\n          const { activeProvider, clientVersion } = engineConfig;\n          if (activeProvider) {\n            clientData['db.system'] = activeProvider;\n          }\n          if (clientVersion) {\n            clientData['db.prisma.version'] = clientVersion;\n          }\n        }\n      } catch (e) {\n        // ignore\n      }\n\n      options.client.$use((params, next: (params: PrismaMiddlewareParams) => Promise<unknown>) => {\n        // eslint-disable-next-line deprecation/deprecation\n        if (shouldDisableAutoInstrumentation(getCurrentHub)) {\n          return next(params);\n        }\n\n        const action = params.action;\n        const model = params.model;\n\n        return startSpan(\n          {\n            name: model ? `${model} ${action}` : action,\n            onlyIfParent: true,\n            op: 'db.prisma',\n            attributes: {\n              [SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN]: 'auto.db.prisma',\n            },\n            data: { ...clientData, 'db.operation': action },\n          },\n          () => next(params),\n        );\n      });\n    } else {\n      DEBUG_BUILD &&\n        logger.warn('Unsupported Prisma client provided to PrismaIntegration. Provided client:', options.client);\n    }\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public setupOnce(): void {\n    // Noop - here for backwards compatibility\n  }\n}\n"], "names": [], "mappings": ";;;;;AA8CA,SAAS,mBAAmB,CAAC,cAAc,EAA2C;AACtF,EAAE,OAAO,CAAC,CAAC,cAAA,IAAkB,CAAC,CAAC,CAAC,cAAe,GAAiB,MAAM,CAAC,CAAA;AACvE,CAAA;AACA;AACA;AACO,MAAM,QAA8B;AAC3C;AACA;AACA;AACA,GAAS,OAAA,YAAA,GAAA,CAAA,IAAA,CAAO,EAAE,GAAW,SAAQ,CAAA;AACrC;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA,GAAS,WAAW,CAAC,OAAO,GAAyB,EAAE,EAAE;AACzD,IAAI,IAAI,CAAC,IAAA,GAAO,MAAM,CAAC,EAAE,CAAA;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,mBAAmB,CAAC,OAAO,CAAC,MAAM,CAAE,IAAG,CAAC,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE;AACpF;AACA,MAAM,wBAAwB,CAAC,OAAO,CAAC,SAAe,qBAAqB,EAAE,IAAI,CAAC,CAAA;AAClF;AACA,MAAM,MAAM,UAAU,GAAoC,EAAE,CAAA;AAC5D,MAAM,IAAI;AACV,QAAQ,MAAM,eAAe,CAAC,OAAO,CAAC,MAAA,GAAwB,aAAa,CAAA;AAC3E,QAAQ,IAAI,YAAY,EAAE;AAC1B,UAAU,MAAM,EAAE,cAAc,EAAE,aAAc,EAAA,GAAI,YAAY,CAAA;AAChE,UAAU,IAAI,cAAc,EAAE;AAC9B,YAAY,UAAU,CAAC,WAAW,CAAA,GAAI,cAAc,CAAA;AACpD,WAAU;AACV,UAAU,IAAI,aAAa,EAAE;AAC7B,YAAY,UAAU,CAAC,mBAAmB,CAAA,GAAI,aAAa,CAAA;AAC3D,WAAU;AACV,SAAQ;AACR,OAAQ,CAAA,OAAO,CAAC,EAAE;AAClB;AACA,OAAM;AACN;AACA,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,IAAI,KAA2D;AAClG;AACA,QAAQ,IAAI,gCAAgC,CAAC,aAAa,CAAC,EAAE;AAC7D,UAAU,OAAO,IAAI,CAAC,MAAM,CAAC,CAAA;AAC7B,SAAQ;AACR;AACA,QAAQ,MAAM,MAAA,GAAS,MAAM,CAAC,MAAM,CAAA;AACpC,QAAQ,MAAM,KAAA,GAAQ,MAAM,CAAC,KAAK,CAAA;AAClC;AACA,QAAQ,OAAO,SAAS;AACxB,UAAU;AACV,YAAY,IAAI,EAAE,KAAM,GAAE,CAAC,EAAA,KAAA,CAAA,CAAA,EAAA,MAAA,CAAA,CAAA,GAAA,MAAA;AACA,YAAA,YAAA,EAAA,IAAA;AACA,YAAA,EAAA,EAAA,WAAA;AACA,YAAA,UAAA,EAAA;AACA,cAAA,CAAA,gCAAA,GAAA,gBAAA;AACA,aAAA;AACA,YAAA,IAAA,EAAA,EAAA,GAAA,UAAA,EAAA,cAAA,EAAA,MAAA,EAAA;AACA,WAAA;AACA,UAAA,MAAA,IAAA,CAAA,MAAA,CAAA;AACA,SAAA,CAAA;AACA,OAAA,CAAA,CAAA;AACA,KAAA,MAAA;AACA,MAAA,WAAA;AACA,QAAA,MAAA,CAAA,IAAA,CAAA,2EAAA,EAAA,OAAA,CAAA,MAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA;AACA;AACA;AACA;AACA,GAAA,SAAA,GAAA;AACA;AACA,GAAA;AACA,CAAA,CAAA,MAAA,CAAA,YAAA,EAAA;;;;"}