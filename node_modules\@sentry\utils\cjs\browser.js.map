{"version": 3, "file": "browser.js", "sources": ["../../src/browser.ts"], "sourcesContent": ["import { isString } from './is';\nimport { getGlobalObject } from './worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\nconst DEFAULT_MAX_STRING_LENGTH = 80;\n\ntype SimpleNode = {\n  parentNode: SimpleNode;\n} | null;\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nexport function htmlTreeAsString(\n  elem: unknown,\n  options: string[] | { keyAttrs?: string[]; maxStringLength?: number } = {},\n): string {\n  if (!elem) {\n    return '<unknown>';\n  }\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem as SimpleNode;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n    const keyAttrs = Array.isArray(options) ? options : options.keyAttrs;\n    const maxStringLength = (!Array.isArray(options) && options.maxStringLength) || DEFAULT_MAX_STRING_LENGTH;\n\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem, keyAttrs);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds maxStringLength\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= maxStringLength)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el: unknown, keyAttrs?: string[]): string {\n  const elem = el as {\n    tagName?: string;\n    id?: string;\n    className?: string;\n    getAttribute(key: string): string;\n  };\n\n  const out = [];\n  let className;\n  let classes;\n  let key;\n  let attr;\n  let i;\n\n  if (!elem || !elem.tagName) {\n    return '';\n  }\n\n  // @ts-expect-error WINDOW has HTMLElement\n  if (WINDOW.HTMLElement) {\n    // If using the component name annotation plugin, this value may be available on the DOM node\n    if (elem instanceof HTMLElement && elem.dataset && elem.dataset['sentryComponent']) {\n      return elem.dataset['sentryComponent'];\n    }\n  }\n\n  out.push(elem.tagName.toLowerCase());\n\n  // Pairs of attribute keys defined in `serializeAttribute` and their values on element.\n  const keyAttrPairs =\n    keyAttrs && keyAttrs.length\n      ? keyAttrs.filter(keyAttr => elem.getAttribute(keyAttr)).map(keyAttr => [keyAttr, elem.getAttribute(keyAttr)])\n      : null;\n\n  if (keyAttrPairs && keyAttrPairs.length) {\n    keyAttrPairs.forEach(keyAttrPair => {\n      out.push(`[${keyAttrPair[0]}=\"${keyAttrPair[1]}\"]`);\n    });\n  } else {\n    if (elem.id) {\n      out.push(`#${elem.id}`);\n    }\n\n    // eslint-disable-next-line prefer-const\n    className = elem.className;\n    if (className && isString(className)) {\n      classes = className.split(/\\s+/);\n      for (i = 0; i < classes.length; i++) {\n        out.push(`.${classes[i]}`);\n      }\n    }\n  }\n  const allowedAttrs = ['aria-label', 'type', 'name', 'title', 'alt'];\n  for (i = 0; i < allowedAttrs.length; i++) {\n    key = allowedAttrs[i];\n    attr = elem.getAttribute(key);\n    if (attr) {\n      out.push(`[${key}=\"${attr}\"]`);\n    }\n  }\n  return out.join('');\n}\n\n/**\n * A safe form of location.href\n */\nexport function getLocationHref(): string {\n  try {\n    return WINDOW.document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n/**\n * Gets a DOM element by using document.querySelector.\n *\n * This wrapper will first check for the existance of the function before\n * actually calling it so that we don't have to take care of this check,\n * every time we want to access the DOM.\n *\n * Reason: DOM/querySelector is not available in all environments.\n *\n * We have to cast to any because utils can be consumed by a variety of environments,\n * and we don't want to break TS users. If you know what element will be selected by\n * `document.querySelector`, specify it as part of the generic call. For example,\n * `const element = getDomElement<Element>('selector');`\n *\n * @param selector the selector string passed on to document.querySelector\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function getDomElement<E = any>(selector: string): E | null {\n  if (WINDOW.document && WINDOW.document.querySelector) {\n    return WINDOW.document.querySelector(selector) as unknown as E;\n  }\n  return null;\n}\n\n/**\n * Given a DOM element, traverses up the tree until it finds the first ancestor node\n * that has the `data-sentry-component` attribute. This attribute is added at build-time\n * by projects that have the component name annotation plugin installed.\n *\n * @returns a string representation of the component for the provided DOM element, or `null` if not found\n */\nexport function getComponentName(elem: unknown): string | null {\n  // @ts-expect-error WINDOW has HTMLElement\n  if (!WINDOW.HTMLElement) {\n    return null;\n  }\n\n  let currentElem = elem as SimpleNode;\n  const MAX_TRAVERSE_HEIGHT = 5;\n  for (let i = 0; i < MAX_TRAVERSE_HEIGHT; i++) {\n    if (!currentElem) {\n      return null;\n    }\n\n    if (currentElem instanceof HTMLElement && currentElem.dataset['sentryComponent']) {\n      return currentElem.dataset['sentryComponent'];\n    }\n\n    currentElem = currentElem.parentNode;\n  }\n\n  return null;\n}\n"], "names": ["getGlobalObject", "isString"], "mappings": ";;;;;AAGA;AACA,MAAM,MAAO,GAAEA,yBAAe,EAAU,CAAA;AACxC;AACA,MAAM,yBAAA,GAA4B,EAAE,CAAA;;AAMpC;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB;AAChC,EAAE,IAAI;AACN,EAAE,OAAO,GAAiE,EAAE;AAC5E,EAAU;AACV,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,WAAW,CAAA;AACtB,GAAE;AACF;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI;AACN,IAAI,IAAI,WAAY,GAAE,IAAK,EAAA;AAC3B,IAAI,MAAM,mBAAoB,GAAE,CAAC,CAAA;AACjC,IAAI,MAAM,GAAA,GAAM,EAAE,CAAA;AAClB,IAAI,IAAI,MAAO,GAAE,CAAC,CAAA;AAClB,IAAI,IAAI,GAAI,GAAE,CAAC,CAAA;AACf,IAAI,MAAM,SAAU,GAAE,KAAK,CAAA;AAC3B,IAAI,MAAM,SAAA,GAAY,SAAS,CAAC,MAAM,CAAA;AACtC,IAAI,IAAI,OAAO,CAAA;AACf,IAAI,MAAM,QAAA,GAAW,KAAK,CAAC,OAAO,CAAC,OAAO,CAAA,GAAI,OAAA,GAAU,OAAO,CAAC,QAAQ,CAAA;AACxE,IAAI,MAAM,eAAgB,GAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,eAAe,KAAK,yBAAyB,CAAA;AAC7G;AACA,IAAI,OAAO,WAAY,IAAG,MAAM,EAAG,GAAE,mBAAmB,EAAE;AAC1D,MAAM,UAAU,oBAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAA;AAC3D;AACA;AACA;AACA;AACA,MAAM,IAAI,OAAA,KAAY,MAAA,KAAW,MAAA,GAAS,CAAA,IAAK,GAAA,GAAM,GAAG,CAAC,MAAO,GAAE,SAAU,GAAE,OAAO,CAAC,MAAO,IAAG,eAAe,CAAC,EAAE;AAClH,QAAQ,MAAK;AACb,OAAM;AACN;AACA,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;AACvB;AACA,MAAM,GAAI,IAAG,OAAO,CAAC,MAAM,CAAA;AAC3B,MAAM,WAAY,GAAE,WAAW,CAAC,UAAU,CAAA;AAC1C,KAAI;AACJ;AACA,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;AACxC,GAAI,CAAA,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,WAAW,CAAA;AACtB,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,oBAAoB,CAAC,EAAE,EAAW,QAAQ,EAAqB;AACxE,EAAE,MAAM,IAAK,GAAE,EAAG;;AAKhB,CAAA;AACF;AACA,EAAE,MAAM,GAAA,GAAM,EAAE,CAAA;AAChB,EAAE,IAAI,SAAS,CAAA;AACf,EAAE,IAAI,OAAO,CAAA;AACb,EAAE,IAAI,GAAG,CAAA;AACT,EAAE,IAAI,IAAI,CAAA;AACV,EAAE,IAAI,CAAC,CAAA;AACP;AACA,EAAE,IAAI,CAAC,IAAA,IAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;AAC9B,IAAI,OAAO,EAAE,CAAA;AACb,GAAE;AACF;AACA;AACA,EAAE,IAAI,MAAM,CAAC,WAAW,EAAE;AAC1B;AACA,IAAI,IAAI,IAAA,YAAgB,WAAA,IAAe,IAAI,CAAC,OAAQ,IAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;AACxF,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;AAC5C,KAAI;AACJ,GAAE;AACF;AACA,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAA;AACtC;AACA;AACA,EAAE,MAAM,YAAa;AACrB,IAAI,QAAA,IAAY,QAAQ,CAAC,MAAA;AACzB,QAAQ,QAAQ,CAAC,MAAM,CAAC,OAAA,IAAW,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,OAAA,IAAW,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAA;AACnH,QAAQ,IAAI,CAAA;AACZ;AACA,EAAE,IAAI,YAAA,IAAgB,YAAY,CAAC,MAAM,EAAE;AAC3C,IAAI,YAAY,CAAC,OAAO,CAAC,eAAe;AACxC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AACzD,KAAK,CAAC,CAAA;AACN,SAAS;AACT,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE;AACjB,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA;AACA,IAAA,SAAA,GAAA,IAAA,CAAA,SAAA,CAAA;AACA,IAAA,IAAA,SAAA,IAAAC,WAAA,CAAA,SAAA,CAAA,EAAA;AACA,MAAA,OAAA,GAAA,SAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAA,KAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,OAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,QAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,OAAA;AACA,KAAA;AACA,GAAA;AACA,EAAA,MAAA,YAAA,GAAA,CAAA,YAAA,EAAA,MAAA,EAAA,MAAA,EAAA,OAAA,EAAA,KAAA,CAAA,CAAA;AACA,EAAA,KAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,YAAA,CAAA,MAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,GAAA,GAAA,YAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,GAAA,IAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA;AACA,IAAA,IAAA,IAAA,EAAA;AACA,MAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA,KAAA;AACA,GAAA;AACA,EAAA,OAAA,GAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA,SAAA,eAAA,GAAA;AACA,EAAA,IAAA;AACA,IAAA,OAAA,MAAA,CAAA,QAAA,CAAA,QAAA,CAAA,IAAA,CAAA;AACA,GAAA,CAAA,OAAA,EAAA,EAAA;AACA,IAAA,OAAA,EAAA,CAAA;AACA,GAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,aAAA,CAAA,QAAA,EAAA;AACA,EAAA,IAAA,MAAA,CAAA,QAAA,IAAA,MAAA,CAAA,QAAA,CAAA,aAAA,EAAA;AACA,IAAA,OAAA,MAAA,CAAA,QAAA,CAAA,aAAA,CAAA,QAAA,CAAA,EAAA;AACA,GAAA;AACA,EAAA,OAAA,IAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,gBAAA,CAAA,IAAA,EAAA;AACA;AACA,EAAA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA;AACA,IAAA,OAAA,IAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,IAAA,WAAA,GAAA,IAAA,EAAA;AACA,EAAA,MAAA,mBAAA,GAAA,CAAA,CAAA;AACA,EAAA,KAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,mBAAA,EAAA,CAAA,EAAA,EAAA;AACA,IAAA,IAAA,CAAA,WAAA,EAAA;AACA,MAAA,OAAA,IAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,IAAA,WAAA,YAAA,WAAA,IAAA,WAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,EAAA;AACA,MAAA,OAAA,WAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,CAAA;AACA,KAAA;AACA;AACA,IAAA,WAAA,GAAA,WAAA,CAAA,UAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,IAAA,CAAA;AACA;;;;;;;"}