{"version": 3, "file": "stacktrace.js", "sources": ["../../src/stacktrace.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack<PERSON>ars<PERSON> } from '@sentry/types';\n\nimport type { GetModuleFn } from './node-stack-trace';\nimport { filenameIsInApp, node } from './node-stack-trace';\n\nexport { filenameIsInApp };\n\nconst STACKTRACE_FRAME_LIMIT = 50;\n// Used to sanitize webpack (error: *) wrapped stack errors\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/;\nconst STRIP_FRAME_REGEXP = /captureMessage|captureException/;\n\n/**\n * Creates a stack parser with the supplied line parsers\n *\n * StackFrames are returned in the correct order for Sentry Exception\n * frames and with Sentry SDK internal frames removed from the top and bottom\n *\n */\nexport function createStackParser(...parsers: StackLineParser[]): StackParser {\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n\n  return (stack: string, skipFirst: number = 0): StackFrame[] => {\n    const frames: StackFrame[] = [];\n    const lines = stack.split('\\n');\n\n    for (let i = skipFirst; i < lines.length; i++) {\n      const line = lines[i];\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      // Many of the regular expressions use backtracking which results in run time that increases exponentially with\n      // input size. Huge strings can result in hangs/Denial of Service:\n      // https://github.com/getsentry/sentry-javascript/issues/2286\n      if (line.length > 1024) {\n        continue;\n      }\n\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line;\n\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue;\n      }\n\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n\n      if (frames.length >= STACKTRACE_FRAME_LIMIT) {\n        break;\n      }\n    }\n\n    return stripSentryFramesAndReverse(frames);\n  };\n}\n\n/**\n * Gets a stack parser implementation from Options.stackParser\n * @see Options\n *\n * If options contains an array of line parsers, it is converted into a parser\n */\nexport function stackParserFromStackParserOptions(stackParser: StackParser | StackLineParser[]): StackParser {\n  if (Array.isArray(stackParser)) {\n    return createStackParser(...stackParser);\n  }\n  return stackParser;\n}\n\n/**\n * Removes Sentry frames from the top and bottom of the stack if present and enforces a limit of max number of frames.\n * Assumes stack input is ordered from top to bottom and returns the reverse representation so call site of the\n * function that caused the crash is the last frame in the array.\n * @hidden\n */\nexport function stripSentryFramesAndReverse(stack: ReadonlyArray<StackFrame>): StackFrame[] {\n  if (!stack.length) {\n    return [];\n  }\n\n  const localStack = Array.from(stack);\n\n  // If stack starts with one of our API calls, remove it (starts, meaning it's the top of the stack - aka last call)\n  if (/sentryWrapped/.test(localStack[localStack.length - 1].function || '')) {\n    localStack.pop();\n  }\n\n  // Reversing in the middle of the procedure allows us to just pop the values off the stack\n  localStack.reverse();\n\n  // If stack ends with one of our internal API calls, remove it (ends, meaning it's the bottom of the stack - aka top-most call)\n  if (STRIP_FRAME_REGEXP.test(localStack[localStack.length - 1].function || '')) {\n    localStack.pop();\n\n    // When using synthetic events, we will have a 2 levels deep stack, as `new Error('Sentry syntheticException')`\n    // is produced within the hub itself, making it:\n    //\n    //   Sentry.captureException()\n    //   getCurrentHub().captureException()\n    //\n    // instead of just the top `Sentry` call itself.\n    // This forces us to possibly strip an additional frame in the exact same was as above.\n    if (STRIP_FRAME_REGEXP.test(localStack[localStack.length - 1].function || '')) {\n      localStack.pop();\n    }\n  }\n\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map(frame => ({\n    ...frame,\n    filename: frame.filename || localStack[localStack.length - 1].filename,\n    function: frame.function || '?',\n  }));\n}\n\nconst defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nexport function getFunctionName(fn: unknown): string {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n\n/**\n * Node.js stack line parser\n *\n * This is in @sentry/utils so it can be used from the Electron SDK in the browser for when `nodeIntegration == true`.\n * This allows it to be used without referencing or importing any node specific code which causes bundlers to complain\n */\nexport function nodeStackLineParser(getModule?: GetModuleFn): StackLineParser {\n  return [90, node(getModule)];\n}\n"], "names": [], "mappings": ";;;AAOA,MAAM,sBAAA,GAAyB,EAAE,CAAA;AACjC;AACA,MAAM,oBAAA,GAAuB,iBAAiB,CAAA;AAC9C,MAAM,kBAAA,GAAqB,iCAAiC,CAAA;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iBAAiB,CAAC,GAAG,OAAO,EAAkC;AAC9E,EAAE,MAAM,aAAA,GAAgB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;AAC1E;AACA,EAAE,OAAO,CAAC,KAAK,EAAU,SAAS,GAAW,CAAC,KAAmB;AACjE,IAAI,MAAM,MAAM,GAAiB,EAAE,CAAA;AACnC,IAAI,MAAM,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;AACnC;AACA,IAAI,KAAK,IAAI,CAAA,GAAI,SAAS,EAAE,CAAE,GAAE,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,MAAM,IAAK,GAAE,KAAK,CAAC,CAAC,CAAC,CAAA;AAC3B;AACA;AACA;AACA;AACA,MAAM,IAAI,IAAI,CAAC,MAAO,GAAE,IAAI,EAAE;AAC9B,QAAQ,SAAQ;AAChB,OAAM;AACN;AACA;AACA;AACA,MAAM,MAAM,WAAY,GAAE,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAA,GAAI,IAAI,CAAA;AAC3G;AACA;AACA;AACA,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AAC3C,QAAQ,SAAQ;AAChB,OAAM;AACN;AACA,MAAM,KAAK,MAAM,MAAO,IAAG,aAAa,EAAE;AAC1C,QAAQ,MAAM,KAAM,GAAE,MAAM,CAAC,WAAW,CAAC,CAAA;AACzC;AACA,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAC5B,UAAU,MAAK;AACf,SAAQ;AACR,OAAM;AACN;AACA,MAAM,IAAI,MAAM,CAAC,MAAO,IAAG,sBAAsB,EAAE;AACnD,QAAQ,MAAK;AACb,OAAM;AACN,KAAI;AACJ;AACA,IAAI,OAAO,2BAA2B,CAAC,MAAM,CAAC,CAAA;AAC9C,GAAG,CAAA;AACH,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,iCAAiC,CAAC,WAAW,EAAgD;AAC7G,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAClC,IAAI,OAAO,iBAAiB,CAAC,GAAG,WAAW,CAAC,CAAA;AAC5C,GAAE;AACF,EAAE,OAAO,WAAW,CAAA;AACpB,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2BAA2B,CAAC,KAAK,EAA2C;AAC5F,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACrB,IAAI,OAAO,EAAE,CAAA;AACb,GAAE;AACF;AACA,EAAE,MAAM,aAAa,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AACtC;AACA;AACA,EAAE,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAO,GAAE,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,EAAE;AAC9E,IAAI,UAAU,CAAC,GAAG,EAAE,CAAA;AACpB,GAAE;AACF;AACA;AACA,EAAE,UAAU,CAAC,OAAO,EAAE,CAAA;AACtB;AACA;AACA,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAO,GAAE,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,EAAE;AACjF,IAAI,UAAU,CAAC,GAAG,EAAE,CAAA;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAO,GAAE,CAAC,CAAC,CAAC,YAAY,EAAE,CAAC,EAAE;AACnF,MAAM,UAAU,CAAC,GAAG,EAAE,CAAA;AACtB,KAAI;AACJ,GAAE;AACF;AACA,EAAE,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,sBAAsB,CAAC,CAAC,GAAG,CAAC,KAAA,KAAU;AACnE,IAAI,GAAG,KAAK;AACZ,IAAI,QAAQ,EAAE,KAAK,CAAC,YAAY,UAAU,CAAC,UAAU,CAAC,MAAO,GAAE,CAAC,CAAC,CAAC,QAAQ;AAC1E,IAAI,QAAQ,EAAE,KAAK,CAAC,QAAA,IAAY,GAAG;AACnC,GAAG,CAAC,CAAC,CAAA;AACL,CAAA;AACA;AACA,MAAM,mBAAA,GAAsB,aAAa,CAAA;AACzC;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,EAAE,EAAmB;AACrD,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,EAAA,IAAM,OAAO,EAAA,KAAO,UAAU,EAAE;AACzC,MAAM,OAAO,mBAAmB,CAAA;AAChC,KAAI;AACJ,IAAI,OAAO,EAAE,CAAC,IAAA,IAAQ,mBAAmB,CAAA;AACzC,GAAI,CAAA,OAAO,CAAC,EAAE;AACd;AACA;AACA,IAAI,OAAO,mBAAmB,CAAA;AAC9B,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,mBAAmB,CAAC,SAAS,EAAiC;AAC9E,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;AAC9B;;;;"}