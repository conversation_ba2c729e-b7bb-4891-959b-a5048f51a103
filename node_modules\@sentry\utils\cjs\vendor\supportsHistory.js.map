{"version": 3, "file": "supportsHistory.js", "sources": ["../../../src/vendor/supportsHistory.ts"], "sourcesContent": ["// Based on https://github.com/angular/angular.js/pull/13945/files\n// The MIT License\n\n// Copyright (c) 2010-2016 Google, Inc. http://angularjs.org\n\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nimport { getGlobalObject } from '../worldwide';\n\n// eslint-disable-next-line deprecation/deprecation\nconst WINDOW = getGlobalObject<Window>();\n\n/**\n * Tells whether current environment supports History API\n * {@link supportsHistory}.\n *\n * @returns Answer to the given question.\n */\nexport function supportsHistory(): boolean {\n  // NOTE: in Chrome App environment, touching history.pushState, *even inside\n  //       a try/catch block*, will cause Chrome to output an error to console.error\n  // borrowed from: https://github.com/angular/angular.js/pull/13945/files\n  /* eslint-disable @typescript-eslint/no-unsafe-member-access */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const chromeVar = (WINDOW as any).chrome;\n  const isChromePackagedApp = chromeVar && chromeVar.app && chromeVar.app.runtime;\n  /* eslint-enable @typescript-eslint/no-unsafe-member-access */\n  const hasHistoryApi = 'history' in WINDOW && !!WINDOW.history.pushState && !!WINDOW.history.replaceState;\n\n  return !isChromePackagedApp && hasHistoryApi;\n}\n"], "names": ["getGlobalObject"], "mappings": ";;;;AAAA;AAwBA;AACA;AACA,MAAM,MAAO,GAAEA,yBAAe,EAAU,CAAA;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,GAAY;AAC3C;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,SAAU,GAAE,CAAC,MAAO,GAAQ,MAAM,CAAA;AAC1C,EAAE,MAAM,mBAAA,GAAsB,SAAA,IAAa,SAAS,CAAC,GAAA,IAAO,SAAS,CAAC,GAAG,CAAC,OAAO,CAAA;AACjF;AACA,EAAE,MAAM,gBAAgB,SAAA,IAAa,MAAO,IAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAA;AAC1G;AACA,EAAE,OAAO,CAAC,mBAAoB,IAAG,aAAa,CAAA;AAC9C;;;;"}