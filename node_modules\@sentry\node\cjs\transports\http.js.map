{"version": 3, "file": "http.js", "sources": ["../../../src/transports/http.ts"], "sourcesContent": ["import * as http from 'http';\nimport * as https from 'https';\nimport { Readable } from 'stream';\nimport { URL } from 'url';\nimport { createGzip } from 'zlib';\nimport { createTransport } from '@sentry/core';\nimport type {\n  BaseTransportOptions,\n  Transport,\n  TransportMakeRequestResponse,\n  TransportRequest,\n  TransportRequestExecutor,\n} from '@sentry/types';\nimport { consoleSandbox } from '@sentry/utils';\nimport { HttpsProxyAgent } from '../proxy';\n\nimport type { HTTPModule } from './http-module';\n\nexport interface NodeTransportOptions extends BaseTransportOptions {\n  /** Define custom headers */\n  headers?: Record<string, string>;\n  /** Set a proxy that should be used for outbound requests. */\n  proxy?: string;\n  /** HTTPS proxy CA certificates */\n  caCerts?: string | Buffer | Array<string | Buffer>;\n  /** Custom HTTP module. Defaults to the native 'http' and 'https' modules. */\n  httpModule?: HTTPModule;\n  /** Allow overriding connection keepAlive, defaults to false */\n  keepAlive?: boolean;\n}\n\n// Estimated maximum size for reasonable standalone event\nconst GZIP_THRESHOLD = 1024 * 32;\n\n/**\n * Gets a stream from a Uint8Array or string\n * Readable.from is ideal but was added in node.js v12.3.0 and v10.17.0\n */\nfunction streamFromBody(body: Uint8Array | string): Readable {\n  return new Readable({\n    read() {\n      this.push(body);\n      this.push(null);\n    },\n  });\n}\n\n/**\n * Creates a Transport that uses native the native 'http' and 'https' modules to send events to Sentry.\n */\nexport function makeNodeTransport(options: NodeTransportOptions): Transport {\n  let urlSegments: URL;\n\n  try {\n    urlSegments = new URL(options.url);\n  } catch (e) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[@sentry/node]: Invalid dsn or tunnel option, will not send any events. The tunnel option must be a full URL when used.',\n      );\n    });\n    return createTransport(options, () => Promise.resolve({}));\n  }\n\n  const isHttps = urlSegments.protocol === 'https:';\n\n  // Proxy prioritization: http => `options.proxy` | `process.env.http_proxy`\n  // Proxy prioritization: https => `options.proxy` | `process.env.https_proxy` | `process.env.http_proxy`\n  const proxy = applyNoProxyOption(\n    urlSegments,\n    options.proxy || (isHttps ? process.env.https_proxy : undefined) || process.env.http_proxy,\n  );\n\n  const nativeHttpModule = isHttps ? https : http;\n  const keepAlive = options.keepAlive === undefined ? false : options.keepAlive;\n\n  // TODO(v7): Evaluate if we can set keepAlive to true. This would involve testing for memory leaks in older node\n  // versions(>= 8) as they had memory leaks when using it: #2555\n  const agent = proxy\n    ? (new HttpsProxyAgent(proxy) as http.Agent)\n    : new nativeHttpModule.Agent({ keepAlive, maxSockets: 30, timeout: 2000 });\n\n  const requestExecutor = createRequestExecutor(options, options.httpModule ?? nativeHttpModule, agent);\n  return createTransport(options, requestExecutor);\n}\n\n/**\n * Honors the `no_proxy` env variable with the highest priority to allow for hosts exclusion.\n *\n * @param transportUrl The URL the transport intends to send events to.\n * @param proxy The client configured proxy.\n * @returns A proxy the transport should use.\n */\nfunction applyNoProxyOption(transportUrlSegments: URL, proxy: string | undefined): string | undefined {\n  const { no_proxy } = process.env;\n\n  const urlIsExemptFromProxy =\n    no_proxy &&\n    no_proxy\n      .split(',')\n      .some(\n        exemption => transportUrlSegments.host.endsWith(exemption) || transportUrlSegments.hostname.endsWith(exemption),\n      );\n\n  if (urlIsExemptFromProxy) {\n    return undefined;\n  } else {\n    return proxy;\n  }\n}\n\n/**\n * Creates a RequestExecutor to be used with `createTransport`.\n */\nfunction createRequestExecutor(\n  options: NodeTransportOptions,\n  httpModule: HTTPModule,\n  agent: http.Agent,\n): TransportRequestExecutor {\n  const { hostname, pathname, port, protocol, search } = new URL(options.url);\n  return function makeRequest(request: TransportRequest): Promise<TransportMakeRequestResponse> {\n    return new Promise((resolve, reject) => {\n      let body = streamFromBody(request.body);\n\n      const headers: Record<string, string> = { ...options.headers };\n\n      if (request.body.length > GZIP_THRESHOLD) {\n        headers['content-encoding'] = 'gzip';\n        body = body.pipe(createGzip());\n      }\n\n      const req = httpModule.request(\n        {\n          method: 'POST',\n          agent,\n          headers,\n          hostname,\n          path: `${pathname}${search}`,\n          port,\n          protocol,\n          ca: options.caCerts,\n        },\n        res => {\n          res.on('data', () => {\n            // Drain socket\n          });\n\n          res.on('end', () => {\n            // Drain socket\n          });\n\n          res.setEncoding('utf8');\n\n          // \"Key-value pairs of header names and values. Header names are lower-cased.\"\n          // https://nodejs.org/api/http.html#http_message_headers\n          const retryAfterHeader = res.headers['retry-after'] ?? null;\n          const rateLimitsHeader = res.headers['x-sentry-rate-limits'] ?? null;\n\n          resolve({\n            statusCode: res.statusCode,\n            headers: {\n              'retry-after': retryAfterHeader,\n              'x-sentry-rate-limits': Array.isArray(rateLimitsHeader) ? rateLimitsHeader[0] : rateLimitsHeader,\n            },\n          });\n        },\n      );\n\n      req.on('error', reject);\n      body.pipe(req);\n    });\n  };\n}\n"], "names": ["Readable", "URL", "consoleSandbox", "createTransport", "HttpsProxyAgent", "createGzip"], "mappings": ";;;;;;;;;;;;;;;AA+BA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,CAAK,CAAA,CAAA,EAAA,EAAE,EAAE;;AAEhC,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,CAAA;AACA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAI,EAAiC;EAC3D,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAIA,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAQ,CAAC;IAClB,IAAI,CAAG,EAAA;MACL,IAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC;MACf,IAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC;IAChB,CAAA;EACL,CAAG,CAAC;AACJ;;AAEA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,CAAA;AACO,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAmC;EAC1E,CAAA,CAAA,EAAI,WAAW;;EAEf,CAAI,CAAA,EAAA;IACF,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAc,CAAIC,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAG,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAG,CAAA,CAAA,CAAC;EAClC,EAAA,CAAO,CAAA,CAAA,CAAA,EAAA,CAAA,CAAC,EAAE;IACVC,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAc,CAAC,CAAM,EAAA,CAAA,EAAA;MACzB,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;MACM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAI,CAAA,CAAA,CAAA;QACV,CAAyH,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;MACjI,CAAO;IACP,CAAK,CAAC;IACF,CAAOC,CAAAA,CAAAA,CAAAA,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAe,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,CAAA,EAAA,CAAA,EAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAE,CAAA,CAAC,CAAC;EAC5D;;EAEA,CAAA,CAAA,CAAA,CAAA,EAAM,QAAU,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAa,QAAQ;;EAEnD,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACE,CAAA,CAAA,CAAA,CAAA,EAAM,CAAM,CAAA,CAAA,CAAA,EAAA,EAAE,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAC9B,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACX,OAAO,CAAC,CAAA,CAAA,CAAA,CAAA,MAAU,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,OAAO,CAAC,CAAA,CAAA,CAAG,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAE,SAAS,EAAE,CAAA,EAAG,OAAO,CAAC,CAAA,CAAA,CAAG,CAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAC9F,CAAG;;EAED,CAAA,CAAA,CAAA,CAAA,EAAM,iBAAmB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAU,CAAA,CAAA,CAAA,CAAA,EAAA,EAAQ,IAAI;EAC/C,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAY,CAAA,CAAA,CAAA,CAAA,EAAA,EAAQ,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,SAAS;;EAE/E,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;EACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;EACE,CAAA,CAAA,CAAA,CAAA,EAAM,QAAQ,CAAA,CAAA,CAAA,CAAA;IAChB,EAAA,CAAO,CAAIC,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAe,CAAC,CAAA,CAAA,CAAA,CAAA,CAAK,EAAE;IAClC,EAAM,IAAI,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAC,EAAE,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,EAAE,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAE,CAAA,CAAA,CAAA,EAAA,CAAM,CAAC;;EAE5E,CAAA,CAAA,CAAA,CAAA,EAAM,gBAAkB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,gBAAA,CAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAC,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,QAAG,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC;EACrG,OAAOD,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,OAAO,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAC;AAClD;;AAEA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA;CACA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,KAAK,EAA0C;EACpG,CAAA,CAAA,CAAA,CAAA,EAAM,EAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAa,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,GAAG;;EAEhC,MAAM,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA;IACzB,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;MACG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA,CAAA,CAAG;MAChB,CAAO,CAAI,CAAA,CAAA,CAAA;QACH,UAAa,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAoB,CAAC,CAAI,CAAA,CAAA,CAAA,CAAC,QAAQ,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAE,CAAA,EAAG,oBAAoB,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,SAAS,CAAC;MACvH,CAAO;;EAEL,CAAA,EAAA,CAAI,oBAAoB,EAAE;IACxB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,SAAS;EACpB,EAAS,CAAA,CAAA,CAAA,EAAA;IACL,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,KAAK;EACd;AACF;;AAEA,CAAA,CAAA;CACA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;CACA,CAAA;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EAC5B,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACP,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;EACV,CAAK,CAAA,CAAA,CAAA,CAAA;AACP,EAA4B;EAC1B,CAAA,CAAA,CAAA,CAAA,EAAM,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,QAAQ,EAAE,CAAA,CAAA,CAAA,CAAI,EAAE,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,OAAO,EAAA,EAAI,IAAIF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,OAAO,CAAC,CAAA,CAAA,CAAG,CAAC;EAC3E,CAAO,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,OAAO,EAA2D;IAC5F,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,IAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,MAAM,EAAK,CAAA,EAAA;MACtC,CAAA,CAAA,EAAI,KAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAI,CAAA,CAAA,CAAA,CAAC;;MAEvC,CAAA,CAAA,CAAA,CAAA,EAAM,QAAkC,EAAA,EAAE,GAAG,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAS;;MAE9D,CAAA,EAAA,CAAI,OAAO,CAAC,CAAA,CAAA,CAAA,CAAI,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAS,cAAc,EAAE;QACxC,OAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,EAAA,EAAI,MAAM;QACpC,CAAA,CAAA,CAAA,IAAO,CAAI,CAAA,CAAA,CAAA,CAAC,IAAI,CAACI,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAAA,CAAU,CAAE,CAAA,CAAC;MAChC;;MAEA,CAAM,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC5B;UACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAE,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA;UACd,CAAK,CAAA,CAAA,CAAA,CAAA;UACL,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACP,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACR,CAAA,CAAA,CAAA,CAAI,EAAE,CAAC,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACA,CAAA,CAAA,CAAA,CAAA;UACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA,CAAA;QACA,IAAA,CAAA,EAAA;UACA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;YACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACA,CAAA,CAAA;;UAEA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA;YACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACA,CAAA,CAAA;;UAEA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;UAEA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;UACA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;UAEA,OAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,OAAA,EAAA;cACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;cACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA;UACA,CAAA,CAAA;QACA,CAAA;MACA,CAAA;;MAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;MACA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACA,CAAA,CAAA;EACA,CAAA;AACA;;"}