{"version": 3, "file": "getActivationStart.js", "sources": ["../../../../../src/browser/web-vitals/lib/getActivationStart.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getNavigationEntry } from './getNavigationEntry';\n\nexport const getActivationStart = (): number => {\n  const navEntry = getNavigationEntry();\n  return (navEntry && navEntry.activationStart) || 0;\n};\n"], "names": ["getNavigationEntry"], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACa,MAAA,kBAAA,GAAqB,MAAc;AAChD,EAAE,MAAM,QAAA,GAAWA,qCAAkB,EAAE,CAAA;AACvC,EAAE,OAAO,CAAC,QAAS,IAAG,QAAQ,CAAC,eAAe,KAAK,CAAC,CAAA;AACpD;;;;"}