import { Event, EventHint, Exception, StackParser } from '@sentry/types';
/**
 * Creates exceptions inside `event.exception.values` for errors that are nested on properties based on the `key` parameter.
 */
export declare function applyAggregateErrorsToEvent(exceptionFromErrorImplementation: (stackParser: StackParser, ex: Error) => Exception, parser: StackParser, maxValueLimit: number | undefined, key: string, limit: number, event: Event, hint?: EventHint): void;
//# sourceMappingURL=aggregate-errors.d.ts.map
