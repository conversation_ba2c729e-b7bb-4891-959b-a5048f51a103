{"version": 3, "file": "cron.js", "sources": ["../../../src/cron/cron.ts"], "sourcesContent": ["import { withMonitor } from '@sentry/core';\nimport { replaceCronNames } from './common';\n\nexport type CronJobParams = {\n  cronTime: string | Date;\n  onTick: (context: unknown, onComplete?: unknown) => void | Promise<void>;\n  onComplete?: () => void | Promise<void>;\n  start?: boolean | null;\n  context?: unknown;\n  runOnInit?: boolean | null;\n  unrefTimeout?: boolean | null;\n} & (\n  | {\n      timeZone?: string | null;\n      utcOffset?: never;\n    }\n  | {\n      timeZone?: never;\n      utcOffset?: number | null;\n    }\n);\n\nexport type CronJob = {\n  //\n};\n\nexport type CronJobConstructor = {\n  from: (param: CronJobParams) => CronJob;\n\n  new (\n    cronTime: CronJobParams['cronTime'],\n    onTick: CronJobParams['onTick'],\n    onComplete?: CronJobParams['onComplete'],\n    start?: CronJobParams['start'],\n    timeZone?: CronJobParams['timeZone'],\n    context?: CronJobParams['context'],\n    runOnInit?: CronJobParams['runOnInit'],\n    utcOffset?: null,\n    unrefTimeout?: CronJobParams['unrefTimeout'],\n  ): CronJob;\n  new (\n    cronTime: CronJobParams['cronTime'],\n    onTick: CronJobParams['onTick'],\n    onComplete?: CronJobParams['onComplete'],\n    start?: CronJobParams['start'],\n    timeZone?: null,\n    context?: CronJobParams['context'],\n    runOnInit?: CronJobParams['runOnInit'],\n    utcOffset?: CronJobParams['utcOffset'],\n    unrefTimeout?: CronJobParams['unrefTimeout'],\n  ): CronJob;\n};\n\nconst ERROR_TEXT = 'Automatic instrumentation of CronJob only supports crontab string';\n\n/**\n * Instruments the `cron` library to send a check-in event to Sentry for each job execution.\n *\n * ```ts\n * import * as Sentry from '@sentry/node';\n * import { CronJob } from 'cron';\n *\n * const CronJobWithCheckIn = Sentry.cron.instrumentCron(CronJob, 'my-cron-job');\n *\n * // use the constructor\n * const job = new CronJobWithCheckIn('* * * * *', () => {\n *  console.log('You will see this message every minute');\n * });\n *\n * // or from\n * const job = CronJobWithCheckIn.from({ cronTime: '* * * * *', onTick: () => {\n *   console.log('You will see this message every minute');\n * });\n * ```\n */\nexport function instrumentCron<T>(lib: T & CronJobConstructor, monitorSlug: string): T {\n  let jobScheduled = false;\n\n  return new Proxy(lib, {\n    construct(target, args: ConstructorParameters<CronJobConstructor>) {\n      const [cronTime, onTick, onComplete, start, timeZone, ...rest] = args;\n\n      if (typeof cronTime !== 'string') {\n        throw new Error(ERROR_TEXT);\n      }\n\n      if (jobScheduled) {\n        throw new Error(`A job named '${monitorSlug}' has already been scheduled`);\n      }\n\n      jobScheduled = true;\n\n      const cronString = replaceCronNames(cronTime);\n\n      function monitoredTick(context: unknown, onComplete?: unknown): void | Promise<void> {\n        return withMonitor(\n          monitorSlug,\n          () => {\n            return onTick(context, onComplete);\n          },\n          {\n            schedule: { type: 'crontab', value: cronString },\n            timezone: timeZone || undefined,\n          },\n        );\n      }\n\n      return new target(cronTime, monitoredTick, onComplete, start, timeZone, ...rest);\n    },\n    get(target, prop: keyof CronJobConstructor) {\n      if (prop === 'from') {\n        return (param: CronJobParams) => {\n          const { cronTime, onTick, timeZone } = param;\n\n          if (typeof cronTime !== 'string') {\n            throw new Error(ERROR_TEXT);\n          }\n\n          if (jobScheduled) {\n            throw new Error(`A job named '${monitorSlug}' has already been scheduled`);\n          }\n\n          jobScheduled = true;\n\n          const cronString = replaceCronNames(cronTime);\n\n          param.onTick = (context: unknown, onComplete?: unknown) => {\n            return withMonitor(\n              monitorSlug,\n              () => {\n                return onTick(context, onComplete);\n              },\n              {\n                schedule: { type: 'crontab', value: cronString },\n                timezone: timeZone || undefined,\n              },\n            );\n          };\n\n          return target.from(param);\n        };\n      } else {\n        return target[prop];\n      }\n    },\n  });\n}\n"], "names": [], "mappings": ";;;AAqDA,MAAM,UAAA,GAAa,mEAAmE,CAAA;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,CAAI,GAAG,EAA0B,WAAW,EAAa;AACvF,EAAE,IAAI,YAAa,GAAE,KAAK,CAAA;AAC1B;AACA,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE;AACxB,IAAI,SAAS,CAAC,MAAM,EAAE,IAAI,EAA6C;AACvE,MAAM,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAA,GAAI,IAAI,CAAA;AAC3E;AACA,MAAM,IAAI,OAAO,QAAS,KAAI,QAAQ,EAAE;AACxC,QAAQ,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAA;AACnC,OAAM;AACN;AACA,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,4BAA4B,CAAC,CAAC,CAAA;AAClF,OAAM;AACN;AACA,MAAM,YAAA,GAAe,IAAI,CAAA;AACzB;AACA,MAAM,MAAM,UAAW,GAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAA;AACnD;AACA,MAAM,SAAS,aAAa,CAAC,OAAO,EAAW,UAAU,EAAkC;AAC3F,QAAQ,OAAO,WAAW;AAC1B,UAAU,WAAW;AACrB,UAAU,MAAM;AAChB,YAAY,OAAO,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;AAC9C,WAAW;AACX,UAAU;AACV,YAAY,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,UAAA,EAAY;AAC5D,YAAY,QAAQ,EAAE,QAAS,IAAG,SAAS;AAC3C,WAAW;AACX,SAAS,CAAA;AACT,OAAM;AACN;AACA,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,CAAA;AACtF,KAAK;AACL,IAAI,GAAG,CAAC,MAAM,EAAE,IAAI,EAA4B;AAChD,MAAM,IAAI,IAAK,KAAI,MAAM,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,KAAoB;AACzC,UAAU,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAA,EAAW,GAAE,KAAK,CAAA;AACtD;AACA,UAAU,IAAI,OAAO,QAAS,KAAI,QAAQ,EAAE;AAC5C,YAAY,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,CAAA;AACvC,WAAU;AACV;AACA,UAAU,IAAI,YAAY,EAAE;AAC5B,YAAY,MAAM,IAAI,KAAK,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,4BAA4B,CAAC,CAAC,CAAA;AACtF,WAAU;AACV;AACA,UAAU,YAAA,GAAe,IAAI,CAAA;AAC7B;AACA,UAAU,MAAM,UAAW,GAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAA;AACvD;AACA,UAAU,KAAK,CAAC,MAAO,GAAE,CAAC,OAAO,EAAW,UAAU,KAAe;AACrE,YAAY,OAAO,WAAW;AAC9B,cAAc,WAAW;AACzB,cAAc,MAAM;AACpB,gBAAgB,OAAO,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;AAClD,eAAe;AACf,cAAc;AACd,gBAAgB,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,UAAA,EAAY;AAChE,gBAAgB,QAAQ,EAAE,QAAS,IAAG,SAAS;AAC/C,eAAe;AACf,aAAa,CAAA;AACb,WAAW,CAAA;AACX;AACA,UAAU,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AACnC,SAAS,CAAA;AACT,aAAa;AACb,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,CAAA;AAC3B,OAAM;AACN,KAAK;AACL,GAAG,CAAC,CAAA;AACJ;;;;"}