{"version": 3, "file": "baggage.js", "sources": ["../../src/baggage.ts"], "sourcesContent": ["import type { DynamicSamplingContext } from '@sentry/types';\n\nimport { DEBUG_BUILD } from './debug-build';\nimport { isString } from './is';\nimport { logger } from './logger';\n\nexport const BAGGAGE_HEADER_NAME = 'baggage';\n\nexport const SENTRY_BAGGAGE_KEY_PREFIX = 'sentry-';\n\nexport const SENTRY_BAGGAGE_KEY_PREFIX_REGEX = /^sentry-/;\n\n/**\n * Max length of a serialized baggage string\n *\n * https://www.w3.org/TR/baggage/#limits\n */\nexport const MAX_BAGGAGE_STRING_LENGTH = 8192;\n\n/**\n * Takes a baggage header and turns it into Dynamic Sampling Context, by extracting all the \"sentry-\" prefixed values\n * from it.\n *\n * @param baggageHeader A very bread definition of a baggage header as it might appear in various frameworks.\n * @returns The Dynamic Sampling Context that was found on `baggageHeader`, if there was any, `undefined` otherwise.\n */\nexport function baggageHeaderToDynamicSamplingContext(\n  // Very liberal definition of what any incoming header might look like\n  baggageHeader: string | string[] | number | null | undefined | boolean,\n): Partial<DynamicSamplingContext> | undefined {\n  if (!isString(baggageHeader) && !Array.isArray(baggageHeader)) {\n    return undefined;\n  }\n\n  // Intermediary object to store baggage key value pairs of incoming baggage headers on.\n  // It is later used to read Sentry-DSC-values from.\n  let baggageObject: Readonly<Record<string, string>> = {};\n\n  if (Array.isArray(baggageHeader)) {\n    // Combine all baggage headers into one object containing the baggage values so we can later read the Sentry-DSC-values from it\n    baggageObject = baggageHeader.reduce<Record<string, string>>((acc, curr) => {\n      const currBaggageObject = baggageHeaderToObject(curr);\n      for (const key of Object.keys(currBaggageObject)) {\n        acc[key] = currBaggageObject[key];\n      }\n      return acc;\n    }, {});\n  } else {\n    // Return undefined if baggage header is an empty string (technically an empty baggage header is not spec conform but\n    // this is how we choose to handle it)\n    if (!baggageHeader) {\n      return undefined;\n    }\n\n    baggageObject = baggageHeaderToObject(baggageHeader);\n  }\n\n  // Read all \"sentry-\" prefixed values out of the baggage object and put it onto a dynamic sampling context object.\n  const dynamicSamplingContext = Object.entries(baggageObject).reduce<Record<string, string>>((acc, [key, value]) => {\n    if (key.match(SENTRY_BAGGAGE_KEY_PREFIX_REGEX)) {\n      const nonPrefixedKey = key.slice(SENTRY_BAGGAGE_KEY_PREFIX.length);\n      acc[nonPrefixedKey] = value;\n    }\n    return acc;\n  }, {});\n\n  // Only return a dynamic sampling context object if there are keys in it.\n  // A keyless object means there were no sentry values on the header, which means that there is no DSC.\n  if (Object.keys(dynamicSamplingContext).length > 0) {\n    return dynamicSamplingContext as Partial<DynamicSamplingContext>;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Turns a Dynamic Sampling Object into a baggage header by prefixing all the keys on the object with \"sentry-\".\n *\n * @param dynamicSamplingContext The Dynamic Sampling Context to turn into a header. For convenience and compatibility\n * with the `getDynamicSamplingContext` method on the Transaction class ,this argument can also be `undefined`. If it is\n * `undefined` the function will return `undefined`.\n * @returns a baggage header, created from `dynamicSamplingContext`, or `undefined` either if `dynamicSamplingContext`\n * was `undefined`, or if `dynamicSamplingContext` didn't contain any values.\n */\nexport function dynamicSamplingContextToSentryBaggageHeader(\n  // this also takes undefined for convenience and bundle size in other places\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>,\n): string | undefined {\n  if (!dynamicSamplingContext) {\n    return undefined;\n  }\n\n  // Prefix all DSC keys with \"sentry-\" and put them into a new object\n  const sentryPrefixedDSC = Object.entries(dynamicSamplingContext).reduce<Record<string, string>>(\n    (acc, [dscKey, dscValue]) => {\n      if (dscValue) {\n        acc[`${SENTRY_BAGGAGE_KEY_PREFIX}${dscKey}`] = dscValue;\n      }\n      return acc;\n    },\n    {},\n  );\n\n  return objectToBaggageHeader(sentryPrefixedDSC);\n}\n\n/**\n * Will parse a baggage header, which is a simple key-value map, into a flat object.\n *\n * @param baggageHeader The baggage header to parse.\n * @returns a flat object containing all the key-value pairs from `baggageHeader`.\n */\nfunction baggageHeaderToObject(baggageHeader: string): Record<string, string> {\n  return baggageHeader\n    .split(',')\n    .map(baggageEntry => baggageEntry.split('=').map(keyOrValue => decodeURIComponent(keyOrValue.trim())))\n    .reduce<Record<string, string>>((acc, [key, value]) => {\n      acc[key] = value;\n      return acc;\n    }, {});\n}\n\n/**\n * Turns a flat object (key-value pairs) into a baggage header, which is also just key-value pairs.\n *\n * @param object The object to turn into a baggage header.\n * @returns a baggage header string, or `undefined` if the object didn't have any values, since an empty baggage header\n * is not spec compliant.\n */\nfunction objectToBaggageHeader(object: Record<string, string>): string | undefined {\n  if (Object.keys(object).length === 0) {\n    // An empty baggage header is not spec compliant: We return undefined.\n    return undefined;\n  }\n\n  return Object.entries(object).reduce((baggageHeader, [objectKey, objectValue], currentIndex) => {\n    const baggageEntry = `${encodeURIComponent(objectKey)}=${encodeURIComponent(objectValue)}`;\n    const newBaggageHeader = currentIndex === 0 ? baggageEntry : `${baggageHeader},${baggageEntry}`;\n    if (newBaggageHeader.length > MAX_BAGGAGE_STRING_LENGTH) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Not adding key: ${objectKey} with val: ${objectValue} to baggage header due to exceeding baggage size limits.`,\n        );\n      return baggageHeader;\n    } else {\n      return newBaggageHeader;\n    }\n  }, '');\n}\n"], "names": [], "mappings": ";;;;AAMO,MAAM,mBAAoB,GAAE,UAAS;AAC5C;AACO,MAAM,yBAA0B,GAAE,UAAS;AAClD;AACO,MAAM,+BAAgC,GAAE,WAAU;AACzD;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,yBAA0B,GAAE,KAAI;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,qCAAqC;AACrD;AACA,EAAE,aAAa;AACf,EAA+C;AAC/C,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAA,IAAK,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AACjE,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA;AACA;AACA,EAAE,IAAI,aAAa,GAAqC,EAAE,CAAA;AAC1D;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AACpC;AACA,IAAI,aAAA,GAAgB,aAAa,CAAC,MAAM,CAAyB,CAAC,GAAG,EAAE,IAAI,KAAK;AAChF,MAAM,MAAM,iBAAkB,GAAE,qBAAqB,CAAC,IAAI,CAAC,CAAA;AAC3D,MAAM,KAAK,MAAM,GAAI,IAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;AACxD,QAAQ,GAAG,CAAC,GAAG,CAAA,GAAI,iBAAiB,CAAC,GAAG,CAAC,CAAA;AACzC,OAAM;AACN,MAAM,OAAO,GAAG,CAAA;AAChB,KAAK,EAAE,EAAE,CAAC,CAAA;AACV,SAAS;AACT;AACA;AACA,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,OAAO,SAAS,CAAA;AACtB,KAAI;AACJ;AACA,IAAI,aAAc,GAAE,qBAAqB,CAAC,aAAa,CAAC,CAAA;AACxD,GAAE;AACF;AACA;AACA,EAAE,MAAM,yBAAyB,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CAAyB,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;AACrH,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAAE;AACpD,MAAM,MAAM,cAAe,GAAE,GAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAA;AACxE,MAAM,GAAG,CAAC,cAAc,CAAA,GAAI,KAAK,CAAA;AACjC,KAAI;AACJ,IAAI,OAAO,GAAG,CAAA;AACd,GAAG,EAAE,EAAE,CAAC,CAAA;AACR;AACA;AACA;AACA,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,MAAA,GAAS,CAAC,EAAE;AACtD,IAAI,OAAO,sBAAuB,EAAA;AAClC,SAAS;AACT,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,2CAA2C;AAC3D;AACA,EAAE,sBAAsB;AACxB,EAAsB;AACtB,EAAE,IAAI,CAAC,sBAAsB,EAAE;AAC/B,IAAI,OAAO,SAAS,CAAA;AACpB,GAAE;AACF;AACA;AACA,EAAE,MAAM,iBAAkB,GAAE,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,MAAM;AACzE,IAAI,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK;AACjC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,GAAG,CAAC,CAAC,EAAA,yBAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA,GAAA,QAAA,CAAA;AACA,OAAA;AACA,MAAA,OAAA,GAAA,CAAA;AACA,KAAA;AACA,IAAA,EAAA;AACA,GAAA,CAAA;AACA;AACA,EAAA,OAAA,qBAAA,CAAA,iBAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,aAAA,EAAA;AACA,EAAA,OAAA,aAAA;AACA,KAAA,KAAA,CAAA,GAAA,CAAA;AACA,KAAA,GAAA,CAAA,YAAA,IAAA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,UAAA,IAAA,kBAAA,CAAA,UAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA;AACA,KAAA,MAAA,CAAA,CAAA,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,CAAA,KAAA;AACA,MAAA,GAAA,CAAA,GAAA,CAAA,GAAA,KAAA,CAAA;AACA,MAAA,OAAA,GAAA,CAAA;AACA,KAAA,EAAA,EAAA,CAAA,CAAA;AACA,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAA,qBAAA,CAAA,MAAA,EAAA;AACA,EAAA,IAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACA;AACA,IAAA,OAAA,SAAA,CAAA;AACA,GAAA;AACA;AACA,EAAA,OAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,CAAA,aAAA,EAAA,CAAA,SAAA,EAAA,WAAA,CAAA,EAAA,YAAA,KAAA;AACA,IAAA,MAAA,YAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA;AACA,IAAA,MAAA,gBAAA,GAAA,YAAA,KAAA,CAAA,GAAA,YAAA,GAAA,CAAA,EAAA,aAAA,CAAA,CAAA,EAAA,YAAA,CAAA,CAAA,CAAA;AACA,IAAA,IAAA,gBAAA,CAAA,MAAA,GAAA,yBAAA,EAAA;AACA,MAAA,WAAA;AACA,QAAA,MAAA,CAAA,IAAA;AACA,UAAA,CAAA,gBAAA,EAAA,SAAA,CAAA,WAAA,EAAA,WAAA,CAAA,wDAAA,CAAA;AACA,SAAA,CAAA;AACA,MAAA,OAAA,aAAA,CAAA;AACA,KAAA,MAAA;AACA,MAAA,OAAA,gBAAA,CAAA;AACA,KAAA;AACA,GAAA,EAAA,EAAA,CAAA,CAAA;AACA;;;;"}